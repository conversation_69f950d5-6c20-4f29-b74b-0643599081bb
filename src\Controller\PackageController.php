<?php
namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Entity\ReleasedPackage;
use App\Entity\User;
use App\Entity\Document;
use App\Repository\DMORepository;
use App\Entity\DMO;
use App\Repository\ProjectRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Service\WorkflowSimulator;
use Symfony\Component\Workflow\Registry;
use App\Entity\Commentaire;


class PackageController extends AbstractController
{

    private WorkflowSimulator $workflowSimulator;
    public function __construct(private Registry $registry, 
    WorkflowSimulator $workflowSimulator){
        $this->workflowSimulator = $workflowSimulator;
    }

    #[Route('/package', name: 'app_package')]
    public function index(EntityManagerInterface $entityManager, DMORepository $dmoRepository, ProjectRepository $projectRepository): Response
    {
        $packages = $entityManager->getRepository(ReleasedPackage::class)->findAll();
        $BEStatuses = $entityManager->getRepository(ReleasedPackage::class)->findPackageBeStatuses();
        $packages_grouped = [
            'REMOVE' => [],
            'BE_0' => [],
            'BE_1' => [],
            'BE' => [],
        ];

        foreach ($packages as $package) {
            $beStatus = $BEStatuses[$package->getId()] ?? 'REMOVE';
            if (isset($packages_grouped[$beStatus])) {
                $packages_grouped[$beStatus][] = $package;
            }
        }

        $dmobyProjects = $this->get_projects($dmoRepository)->getContent();
        $dmobyProjects = json_decode($dmobyProjects, true);

        // get all project
        $projects = $projectRepository->findAll();
        $projects = array_map(function($project){
            return [
                'id' => $project->getId(),
                'otp' => $project->getOTP(),
            ];
        }, $projects);

        return $this->render('package/index.html.twig', [
            'BE_0' => $packages_grouped['BE_0'],
            'BE_1' => $packages_grouped['BE_1'],
            'BE' => $packages_grouped['BE'],
            'dmobyProjects' => $dmobyProjects,
            'projects' => $projects,
        ]);
    }

    #[Route('/package/update/{id}', name: 'update_package')]
    public function update_package(EntityManagerInterface $entityManager, $id, DMORepository $dmoRepository, ProjectRepository $projectRepository): Response
    {
        $package = $entityManager->getRepository(ReleasedPackage::class)->find($id);
        if ($package->getBE() != 'BE_0') {
            $this->addFlash('error', 'Package non modifiable');
            return $this->redirectToRoute('app_package');
        }
        $dmobyProjects = $this->get_projects($dmoRepository)->getContent();
        $dmobyProjects = json_decode($dmobyProjects, true);
        $projects = $projectRepository->findAll();
        $projects = array_map(function($project){
            return [
                'id' => $project->getId(),
                'otp' => $project->getOTP(),
            ];
        }, $projects);

        return $this->render('package/update.html.twig', [
            'users' => $entityManager->getRepository(User::class)->findBy([], ['nom' => 'ASC']),
            'package' => $package,
            'dmobyProjects' => $dmobyProjects,
            'projects' => $projects,
        ]);
    }

    #[Route('/package/verif/{id}', name: 'verif_package')]
    public function verif_package(EntityManagerInterface $entityManager, $id, DMORepository $dmoRepository, ProjectRepository $projectRepository): Response
    {
        $package = $entityManager->getRepository(ReleasedPackage::class)->find($id);
        if ($package->getBE() != 'BE_1') {
            $this->addFlash('error', 'Package non vérifiable');
            return $this->redirectToRoute('app_package');
        }
        $dmobyProjects = $this->get_projects($dmoRepository)->getContent();
        $dmobyProjects = json_decode($dmobyProjects, true);
        $projects = $projectRepository->findAll();
        $projects = array_map(function($project){
            return [
                'id' => $project->getId(),
                'otp' => $project->getOTP(),
            ];
        }, $projects);

        return $this->render('package/verif.html.twig', [
            'users' => $entityManager->getRepository(User::class)->findBy([], ['nom' => 'ASC']),
            'package' => $package,
            'dmobyProjects' => $dmobyProjects,
            'projects' => $projects,
        ]);
    }

    #[Route('/package/valid/{id}', name: 'valid_package')]
    public function valid_package(EntityManagerInterface $entityManager, $id, DMORepository $dmoRepository, ProjectRepository $projectRepository): Response
    {
        $package = $entityManager->getRepository(ReleasedPackage::class)->find($id);
        if ($package->getBE() != 'BE') {
            $this->addFlash('error', 'Package non validable');
            return $this->redirectToRoute('app_package');
        }

        $dmobyProjects = $this->get_projects($dmoRepository)->getContent();
        $dmobyProjects = json_decode($dmobyProjects, true);
        $projects = $projectRepository->findAll();
        $projects = array_map(function($project){
            return [
                'id' => $project->getId(),
                'otp' => $project->getOTP(),
            ];
        }, $projects);

        return $this->render('package/valid.html.twig', [
            'users' => $entityManager->getRepository(User::class)->findBy([], ['nom' => 'ASC']),
            'package' => $package,
            'dmobyProjects' => $dmobyProjects,
            'projects' => $projects,
        ]);
    }

    #[Route('/package/getProjects', name: 'get_projects')]
    public function get_projects(DMORepository $dmoRepository): JsonResponse
    {
        $dmos = $dmoRepository->findAll();
        
        // Tableau associatif pour regrouper les DMO par OTP
        $dmobyProjects = [];

        foreach ($dmos as $dmo) {
            $project = $dmo->getProjectRelation();

            // On ne traite que les DMO liés à un projet
            if ($project !== null) {
                $otp = $project->getOTP();

                // Initialiser le tableau s’il n’existe pas encore pour ce OTP
                if (!isset($dmobyProjects[$otp])) {
                    $dmobyProjects[$otp] = [];
                }

                // Vous pouvez choisir les données à renvoyer pour chaque DMO
                $dmobyProjects[$otp][] = [
                    'id' => $dmo->getId(),
                    'id_dmo' => $dmo->getDmoId(),
                    'description' => $dmo->getDescription(),
                    'projectId' => $project->getId(),
                ];
            }
        }

        return new JsonResponse($dmobyProjects, Response::HTTP_OK);
    }


    #[Route('/package/create', name: 'create_package')]
    public function create_package(EntityManagerInterface $entityManager, Request $request, ProjectRepository $projectRepository, DMORepository $dmoRepository): Response
    {
        $package = new ReleasedPackage();
        // dd($request->request->all());
        $package->setActivity($request->request->get('activity'));
        $package->setEx($request->request->get('ex'));
        // $package->setDmo($request->request->all()['dmo'] ?? []);
        // dd($request->request->get('project'));
        $project = $projectRepository->find($request->request->get('project'));
        if ($project) {
            $package->setProjectRelation($project);
        }
        // $project = $request->request->get('project');
        // if (!is_string($project)) {
        //     $package->setProjectRelation($project);
        // }
        // $package->setProject('');
        $package->setDescription($request->request->get('description'));
        $package->setOwner($this->getUser());
        $package->setReservationDate(new \DateTime());
        $entityManager->persist($package);
        $entityManager->flush();

        $dmoIds = $request->request->all()['dmo'] ?? [];
        foreach ($dmoIds as $dmoId) {
            $dmo = $entityManager->getRepository(DMO::class)->find($dmoId);
            if ($dmo) {
                // On passe l'entité $dmo (de type DMO) et non $dmoEntity
                $package->addDmo($dmo);
                $entityManager->persist($dmo);
            }
        }
        
        $entityManager->flush();

        $id = $package->getId();
        $this->addFlash('success', 'Package créé ! N°' . $id);
        return $this->redirectToRoute('app_package');
    }

    #[Route('/package/edit/{id}', name: 'edit_package')]
    public function edit_package(EntityManagerInterface $entityManager, Request $request, $id, ProjectRepository $projectRepository
    ): JsonResponse
    {
        $package = $entityManager->getRepository(ReleasedPackage::class)->find($id);

        if (!$package) {
            return new JsonResponse(['error' => 'Package not found'], Response::HTTP_NOT_FOUND);
        }

        $package->setActivity($request->request->get('activity'));
        $package->setEx($request->request->get('ex'));
        $dmocollection = [];
        if (isset($request->request->all()['dmo'])){
            foreach ($request->request->all()['dmo'] as $dmoId) {
                $dmo = $entityManager->getRepository(DMO::class)->find($dmoId);
                if ($dmo) {
                    $dmocollection[] = $dmo;
                }
            }
        }
        $package->setDmos($dmocollection ?? []);
        // $package->setProject($request->request->get('project'));
        $project = $projectRepository->find($request->request->get('project'));
        // dd($project);
        if ($project) {
            $package->setProjectRelation($project);
        }
        $package->setDescription($request->request->get('description'));
        $entityManager->persist($package);
        $entityManager->flush();

        return new JsonResponse(['success' => 'Package modifié ! N°' . $id], Response::HTTP_OK);
    }


    #[Route('/package/references/{id}', name: 'get_references')]
    public function get_references(EntityManagerInterface $entityManager, $id): JsonResponse
    {
        $package = $entityManager->getRepository(ReleasedPackage::class)->find($id);

        if (!$package) {
            return new JsonResponse(['error' => 'Package not found'], Response::HTTP_NOT_FOUND);
        }

        $documents = $package->getDocuments();
        $references = [];
        foreach ($documents as $document) {
            $references[] = $document->getReference();
        }
        return new JsonResponse($references, Response::HTTP_OK);
    }

    // detail package
    #[Route('/package/detail/{id}', name: 'detail_package')]
    public function detail_package(EntityManagerInterface $entityManager, $id, WorkflowSimulator $workflowSimulator): Response
    {
        $package = $entityManager->getRepository(ReleasedPackage::class)->find($id);

        $documents = $package->getDocumentsArray();
        $simulated = $workflowSimulator->simulateWorkflowList($documents);

        foreach ($documents as $document) {
            $entityManager->refresh($document);
        }

        return $this->render('package/detail.html.twig', [
            'results' => $simulated,
            'package' => $package,
        ]);
    }


    #[Route('/delete_package', name: 'delete_package', methods: ['POST'])]
    public function delete_package(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        if (!($this->getUser()->isManager())) {
            return new JsonResponse(['status' => 'error', 'message' => 'Vous ne pouvez pas supprimer ce package'], 403);
        }

        $data = json_decode($request->getContent(), true);

        if ($data === null) {
            return new JsonResponse(['status' => 'error', 'message' => 'Requête mal formée ou vide'], 400);
        }

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['id'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $packageId = $data['id'];

        // Rechercher le package correspondant
        $package = $entityManager->getRepository(ReleasedPackage::class)->find($packageId);
        if (!$package) {
            return new JsonResponse(['status' => 'error', 'message' => 'Package non trouvé'], 404);
        }

        // Supprimer les documents associés au package
        foreach ($package->getDocuments() as $document) {
            foreach ($document->getCommentaires() as $commentaire) {
                $entityManager->remove($commentaire);
            }
            $entityManager->remove($document);
        }

        // Supprimer le package
        $entityManager->remove($package);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Package et ses relations supprimés avec succès']);
    }

}
