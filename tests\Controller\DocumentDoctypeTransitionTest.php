<?php

namespace App\Tests\Controller;

use App\Entity\Document;
use App\Entity\User;
use App\Controller\DocumentController;
use App\Service\DoctypeResetService;
use App\Service\DocumentDuplicator;
use App\Service\WorkflowSimulator;
use App\Service\WorkflowTeleportation;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Workflow\Registry;
use ReflectionClass;
use ReflectionMethod;

class DocumentDoctypeTransitionTest extends TestCase
{
    private DocumentController $controller;
    private EntityManagerInterface|MockObject $entityManager;
    private DoctypeResetService|MockObject $doctypeResetService;
    private Registry|MockObject $workflowRegistry;
    private DocumentDuplicator|MockObject $documentDuplicator;
    private WorkflowSimulator|MockObject $workflowSimulator;
    private WorkflowTeleportation|MockObject $workflowTeleportation;
    private Document $document;
    private User|MockObject $user;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->doctypeResetService = $this->createMock(DoctypeResetService::class);
        $this->workflowRegistry = $this->createMock(Registry::class);
        $this->documentDuplicator = $this->createMock(DocumentDuplicator::class);
        $this->workflowSimulator = $this->createMock(WorkflowSimulator::class);
        $this->workflowTeleportation = $this->createMock(WorkflowTeleportation::class);
        $this->user = $this->createMock(User::class);

        $this->controller = new DocumentController(
            $this->workflowRegistry,
            $this->documentDuplicator,
            $this->workflowSimulator,
            $this->workflowTeleportation,
            $this->doctypeResetService
        );

        $this->document = new Document();
    }

    /**
     * Test du nettoyage lors d'une transition MACH -> PUR depuis Machining
     */
    public function testMachToPurFromMachiningTransition(): void
    {
        // Préparer le document avec des données de production et place Machining
        $this->document->setMatProdType('HALB');
        $this->document->setUnit('PC');
        $this->document->setLeadtime(30);
        $this->document->setProcType('E');
        $this->document->setPrisDans1('Value1');
        $this->document->setPrisDans2('Value2');
        $this->document->setMof('MOF123');
        $this->document->setCurrentSteps(['Machining' => 1]);

        // Appeler directement la méthode de nettoyage complet
        $cleanedFields = [];
        $this->callPrivateMethod('cleanProductionFieldsComplete', [$this->document, &$cleanedFields]);

        // Vérifier que tous les champs ont été nettoyés (y compris MOF et pris dans)
        $this->assertNull($this->document->getMatProdType());
        $this->assertNull($this->document->getUnit());
        $this->assertNull($this->document->getLeadtime());
        $this->assertNull($this->document->getProcType());
        $this->assertNull($this->document->getPrisDans1());
        $this->assertNull($this->document->getPrisDans2());
        $this->assertNull($this->document->getMof());

        // Vérifier que les champs nettoyés sont bien trackés
        $this->assertContains('matProdType', $cleanedFields);
        $this->assertContains('unit', $cleanedFields);
        $this->assertContains('leadtime', $cleanedFields);
        $this->assertContains('procType', $cleanedFields);
        $this->assertContains('prisDans1', $cleanedFields);
        $this->assertContains('prisDans2', $cleanedFields);
        $this->assertContains('mof', $cleanedFields);
    }

    /**
     * Test du nettoyage lors d'une transition ASSY -> PUR depuis Assembly
     */
    public function testAssyToPurFromAssemblyTransition(): void
    {
        // Préparer le document avec des données de production et place Assembly
        $this->document->setMatProdType('HALB');
        $this->document->setUnit('PC');
        $this->document->setLeadtime(30);
        $this->document->setProcType('E');
        $this->document->setMof('MOF123'); // Ne doit pas être nettoyé pour ASSY
        $this->document->setPrisDans1('Value1'); // Ne doit pas être nettoyé pour ASSY
        $this->document->setPrisDans2('Value2'); // Ne doit pas être nettoyé pour ASSY
        $this->document->setCurrentSteps(['Assembly' => 1]);

        // Appeler directement la méthode de nettoyage Assembly
        $cleanedFields = [];
        $this->callPrivateMethod('cleanProductionFieldsAssembly', [$this->document, &$cleanedFields]);

        // Vérifier que les champs de base ont été nettoyés mais pas MOF ni pris dans
        $this->assertNull($this->document->getMatProdType());
        $this->assertNull($this->document->getUnit());
        $this->assertNull($this->document->getLeadtime());
        $this->assertNull($this->document->getProcType());
        $this->assertEquals('MOF123', $this->document->getMof()); // MOF ne doit pas être nettoyé pour ASSY
        $this->assertEquals('Value1', $this->document->getPrisDans1()); // prisDans1 ne doit pas être nettoyé pour ASSY
        $this->assertEquals('Value2', $this->document->getPrisDans2()); // prisDans2 ne doit pas être nettoyé pour ASSY

        // Vérifier que MOF et pris dans ne sont pas dans les champs nettoyés
        $this->assertNotContains('mof', $cleanedFields);
        $this->assertNotContains('prisDans1', $cleanedFields);
        $this->assertNotContains('prisDans2', $cleanedFields);
    }

    /**
     * Test du nettoyage lors d'une transition PUR -> MACH depuis Achat_Rfq
     */
    public function testPurToMachFromAchatRfqTransition(): void
    {
        // Préparer le document avec des données RFQ et place Achat_Rfq
        $this->document->setMatProdType('ROH');
        $this->document->setUnit('KG');
        $this->document->setCommodityCode('COMM123');
        $this->document->setPurchasingGroup('BUYER1');
        $this->document->setProcType('F');
        $this->document->setCurrentSteps(['Achat_Rfq' => 1]);

        // Appeler directement la méthode de nettoyage des champs RFQ
        $cleanedFields = [];
        $this->callPrivateMethod('cleanRFQFields', [$this->document, &$cleanedFields]);

        // Vérifier que tous les champs RFQ ont été nettoyés
        $this->assertNull($this->document->getMatProdType());
        $this->assertNull($this->document->getUnit());
        $this->assertNull($this->document->getCommodityCode());
        $this->assertNull($this->document->getPurchasingGroup());
        $this->assertNull($this->document->getProcType());

        // Vérifier que les champs nettoyés sont bien trackés
        $this->assertContains('matProdType', $cleanedFields);
        $this->assertContains('unit', $cleanedFields);
        $this->assertContains('commodityCode', $cleanedFields);
        $this->assertContains('purchasingGroup', $cleanedFields);
        $this->assertContains('procType', $cleanedFields);
    }

    /**
     * Test qu'aucun nettoyage n'est effectué pour des champs déjà vides
     */
    public function testNoCleaningForEmptyFields(): void
    {
        // Préparer le document avec des champs vides
        $this->document->setMatProdType(null);
        $this->document->setUnit(null);
        $this->document->setLeadtime(null);

        // Appeler la méthode de nettoyage des champs de production Assembly
        $cleanedFields = [];
        $this->callPrivateMethod('cleanProductionFieldsAssembly', [$this->document, &$cleanedFields]);

        // Vérifier qu'aucun champ n'a été ajouté à la liste des champs nettoyés
        $this->assertEmpty($cleanedFields);
    }

    /**
     * Test de la détection de la place actuelle du workflow
     */
    public function testGetCurrentWorkflowPlace(): void
    {
        // Test avec place Assembly
        $currentSteps = ['Assembly' => 1, 'BE' => 1];
        $place = $this->callPrivateMethod('getCurrentWorkflowPlace', [$currentSteps]);
        $this->assertEquals('Assembly', $place);

        // Test avec place Achat_F30
        $currentSteps = ['Achat_F30' => 1, 'Quality' => 1];
        $place = $this->callPrivateMethod('getCurrentWorkflowPlace', [$currentSteps]);
        $this->assertEquals('Achat_F30', $place);

        // Test avec place Machining
        $currentSteps = ['Machining' => 1];
        $place = $this->callPrivateMethod('getCurrentWorkflowPlace', [$currentSteps]);
        $this->assertEquals('Machining', $place);

        // Test avec place non prioritaire
        $currentSteps = ['BE' => 1, 'Produit' => 1];
        $place = $this->callPrivateMethod('getCurrentWorkflowPlace', [$currentSteps]);
        $this->assertEquals('BE', $place);

        // Test avec aucune place
        $currentSteps = [];
        $place = $this->callPrivateMethod('getCurrentWorkflowPlace', [$currentSteps]);
        $this->assertEquals('Unknown', $place);
    }

    /**
     * Méthode utilitaire pour appeler des méthodes privées
     */
    private function callPrivateMethod(string $methodName, array $args = []): mixed
    {
        $reflection = new ReflectionClass($this->controller);
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        
        return $method->invokeArgs($this->controller, $args);
    }
}
