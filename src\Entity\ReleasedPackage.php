<?php

namespace App\Entity;

use App\Repository\ReleasedPackageRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ReleasedPackageRepository::class)]
class ReleasedPackage
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;


    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    /**
     * @var Collection<int, Document>
     */
    #[ORM\OneToMany(targetEntity: Document::class, mappedBy: 'relPack')]
    private Collection $documents;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $Activity = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $Ex = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $Reservation_Date = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $Creation_Date = null;

    #[ORM\ManyToOne(inversedBy: 'releasedPackages')]
    private ?User $owner = null;

    #[ORM\ManyToOne(inversedBy: 'verifPackages')]
    private ?User $verif = null;

    #[ORM\ManyToOne(inversedBy: 'validPackages')]
    private ?User $valid = null;

    // La relation vers Project (ManyToOne)
    #[ORM\ManyToOne(inversedBy: 'packages')]
    private ?Project $project_relation = null;

    /**
     * Relation ManyToMany vers DMO
     * - Inverse side défini dans l’entité DMO par $releasedPackages
     */
    #[ORM\ManyToMany(targetEntity: DMO::class, inversedBy: 'releasedPackages')]
    private Collection $dmos;

    public function __construct()
    {
        $this->documents = new ArrayCollection();
        $this->dmos = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        return $this;
    }

    /**
     * @return Collection<int, Document>
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(Document $document): static
    {
        if (!$this->documents->contains($document)) {
            $this->documents->add($document);
            $document->setRelPack($this);
        }
        return $this;
    }

    public function removeDocument(Document $document): static
    {
        if ($this->documents->removeElement($document)) {
            // set the owning side to null (unless already changed)
            if ($document->getRelPack() === $this) {
                $document->setRelPack(null);
            }
        }

        return $this;
    }

    public function getActivity(): ?string
    {
        return $this->Activity;
    }

    public function setActivity(?string $Activity): static
    {
        $this->Activity = $Activity;

        return $this;
    }

    public function getEx(): ?string
    {
        return $this->Ex;
    }

    public function setEx(?string $Ex): static
    {
        $this->Ex = $Ex;

        return $this;
    }

    public function getReservationDate(): ?\DateTimeInterface
    {
        return $this->Reservation_Date;
    }

    public function setReservationDate(?\DateTimeInterface $Reservation_Date): static
    {
        $this->Reservation_Date = $Reservation_Date;

        return $this;
    }

    public function getCreationDate(): ?\DateTimeInterface
    {
        return $this->Creation_Date;
    }

    public function setCreationDate(?\DateTimeInterface $Creation_Date): static
    {
        $this->Creation_Date = $Creation_Date;

        return $this;
    }

    public function getOwner(): ?User
    {
        return $this->owner;
    }

    public function setOwner(?User $owner): static
    {
        $this->owner = $owner;

        return $this;
    }

    public function getVerif(): ?User
    {
        return $this->verif;
    }

    public function setVerif(?User $verif): static
    {
        $this->verif = $verif;

        return $this;
    }

    public function getValid(): ?User
    {
        return $this->valid;
    }

    public function setValid(?User $valid): static
    {
        $this->valid = $valid;

        return $this;
    }

    public function getProjectRelation(): ?Project
    {
        return $this->project_relation;
    }

    public function setProjectRelation(?Project $project_relation): static
    {
        $this->project_relation = $project_relation;

        return $this;
    }

    /**
     * @return Collection<int, DMO>
     */
    public function getDmos(): Collection
    {
        return $this->dmos;
    }

    /**
     * Ajoute un DMO à la relation ManyToMany.
     */
    public function addDmo(DMO $dmo): static
    {
        if (!$this->dmos->contains($dmo)) {
            $this->dmos->add($dmo);
            // côté DMO, la méthode addReleasedPackage($this) 
            // est déjà gérée si vous le souhaitez
            $dmo->addReleasedPackage($this);
        }

        return $this;
    }

    public function setDmos(array $dmos): static
    {
        $this->dmos = new ArrayCollection($dmos);

        return $this;
    }

    /**
     * Retire un DMO de la relation ManyToMany.
     */
    public function removeDmo(DMO $dmo): static
    {
        if ($this->dmos->removeElement($dmo)) {
            // côté DMO, vous pouvez aussi appeler removeReleasedPackage($this)
            $dmo->removeReleasedPackage($this);
        }

        return $this;
    }


    public function getBE(): ?string
    {
        $counts = ['BE_0' => 0, 'BE_1' => 0, 'BE' => 0];
        $totalDocuments = count($this->documents);

        foreach ($this->documents as $document) {
            $currentSteps = $document->getCurrentSteps();
            foreach (array_keys($counts) as $key) {
                if (array_key_exists($key, $currentSteps) || ($key === 'BE_0' && empty($currentSteps))) {
                    $counts[$key]++;
                }
            }
        }

        foreach ($counts as $key => $count) {
            if ($totalDocuments === $count) {
                return $key;
            }
        }

        return 'REMOVE';
    }

    public function getDateBE1(): ?\DateTimeInterface
    {
        $date = null;
        foreach ($this->documents as $document) {
            foreach ($document->getVisas() as $visa) {
                if ($visa->getName() === 'visa_BE_1') {
                    return $visa->getDateVisa();
                }
            }
        }
        return $date;
    }

    public function getDateBE0(): ?\DateTimeInterface
    {
        $date = null;
        foreach ($this->documents as $document) {
            foreach ($document->getVisas() as $visa) {
                if ($visa->getName() === 'visa_BE_0') {
                    return $visa->getDateVisa();
                }
            }
        }
        return $date;
    }

    public function getDocumentsArray(): array
    {
        $doc = [];
        foreach ($this->documents as $document) {
            $doc[] = $document;
        }
        return $doc;
    }
}
