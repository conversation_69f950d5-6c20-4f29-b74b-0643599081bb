# Ordre des commandes de migration

## 📋 **Ordre d'exécution des migrations**

Voici l'ordre recommandé pour exécuter les commandes de migration :

### 1. **Migrations des entités de base**
```bash
# Migration des utilisateurs
php bin/console app:migrate-users

# Migration des projets
php bin/console app:migrate-projects

# Migration des matériaux
php bin/console app:migrate-materials

# Migration des ProductCode
php bin/console app:migrate-product-code
```

### 2. **Migration des packages**
```bash
# Migration des packages (dépend des utilisateurs et projets)
php bin/console app:migrate-packages
```

### 3. **Migration des documents**
```bash
# Migration des documents (dépend des packages, matériaux et ProductCode)
php bin/console app:migrate-documents
```

### 4. **Migration des DMO**
```bash
# Migration des DMO depuis la base db_dmo
php bin/console app:migrate-dmo
```

### 5. **Migration des commentaires**
```bash
# Migration des commentaires (dépend des documents et utilisateurs)
php bin/console app:migrate-commentaires
```

### 6. **Migration du workflow**
```bash
# Migration des états de workflow (dépend des packages)
php bin/console app:migrate-workflow
```

### 7. **Établissement des relations Package ↔ DMO**
```bash
# Création des relations entre packages et DMO (dépend des packages et DMO)
php bin/console app:link-package-dmo
```

## 🔗 **Dépendances entre migrations**

- **Users** : Aucune dépendance
- **Projects** : Aucune dépendance
- **Materials** : Aucune dépendance
- **ProductCode** : Aucune dépendance
- **Packages** : Dépend de Users et Projects
- **Documents** : Dépend de Packages, Materials et ProductCode
- **DMO** : Aucune dépendance (base séparée)
- **Commentaires** : Dépend de Documents et Users
- **Workflow** : Dépend de Packages
- **Package-DMO Relations** : Dépend de Packages et DMO

## ⚠️ **Notes importantes**

1. **Respecter l'ordre** : Ne pas exécuter une migration avant ses dépendances
2. **Vérifier les données** : Contrôler les résultats après chaque étape
3. **Sauvegardes** : Faire des sauvegardes avant les migrations importantes
4. **Tests** : Tester les fonctionnalités après chaque migration

## 🧪 **Commandes de vérification**

Après chaque migration, vous pouvez vérifier les données :

```sql
-- Vérifier le nombre d'enregistrements migrés
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM projects;
SELECT COUNT(*) FROM materials;
SELECT COUNT(*) FROM product_code;
SELECT COUNT(*) FROM released_package;
SELECT COUNT(*) FROM documents;
SELECT COUNT(*) FROM dmo;
SELECT COUNT(*) FROM commentaires;
SELECT COUNT(*) FROM released_package_dmo;
```

## 🎯 **Résultat attendu**

Après toutes les migrations :
- ✅ Tous les utilisateurs migrés
- ✅ Tous les projets migrés
- ✅ Tous les matériaux migrés
- ✅ Tous les ProductCode migrés
- ✅ Tous les packages migrés avec leurs relations
- ✅ Tous les documents migrés avec leurs matériaux
- ✅ Tous les DMO migrés
- ✅ Tous les commentaires migrés
- ✅ États de workflow migrés
- ✅ Relations Package ↔ DMO établies
