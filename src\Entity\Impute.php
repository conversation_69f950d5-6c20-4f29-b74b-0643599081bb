<?php

namespace App\Entity;

use App\Repository\ImputeRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ImputeRepository::class)]
class Impute
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'imputes')]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $user = null;

    #[ORM\Column]
    private ?int $nbHeures = null;

    #[ORM\ManyToOne(inversedBy: 'imputes')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Code $code = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getNbHeures(): ?int
    {
        return $this->nbHeures;
    }

    public function setNbHeures(int $nbHeures): static
    {
        $this->nbHeures = $nbHeures;

        return $this;
    }

    public function getCode(): ?Code
    {
        return $this->code;
    }

    public function setCode(?Code $code): static
    {
        $this->code = $code;

        return $this;
    }

    //toArray
    public function toArray(): array
    {
        setlocale(LC_TIME, 'fr_FR.UTF-8', 'fr_FR', 'fr', 'french');
        return [
            'id' => $this->id,
            'user' => $this->user->getId(),
            'userObject' => $this->user->toArray(),
            'username' => $this->user->getUsername(),
            'nom' => $this->user->getNom(),
            'prenom' => $this->user->getPrenom(),
            'department' => $this->user->getDepartement(),
            'nbHeures' => $this->nbHeures,
            'code' => $this->code->toArray(),
            'phase' => $this->code->getPhase()->toArray(),
            'project' => $this->code->getPhase()->getProjet()->toArray(),
            'periode' => utf8_encode(strftime('%B %Y', $this->createdAt->getTimestamp())),
            'year' => $this->createdAt->format('Y'),
            'createdAt' => $this->createdAt->format('Y-m-d'),
        ];
    }

    public function toArrayLight(): array
    {
        // item.code.projet
        // item.code.phase
        // item.code.code
        return [
            'id' => $this->id,
            'periode' => utf8_encode(strftime('%B %Y', $this->createdAt->getTimestamp())),
            'userNom' => $this->user->getNom(),
            'userPrenom' => $this->user->getPrenom(),
            'code' => $this->code->getCode(),
            'phase' => $this->code->getPhase()->getTitle(),
            'projet' => $this->code->getPhase()->getProjet()->getTitle(),
            'nbHeures' => $this->nbHeures,
            'workCenter' => $this->code->getWorkCenter(),
        ];
    }



    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }
}
