php bin/console doctrine:database:drop --force
php bin/console doctrine:database:create

php bin/console doctrine:migrations:migrate --no-interaction

///////////////////////////////////////////
INSERT INTO newbe.user (id,email,username,nom,prenom,roles,password,manager,titre,is_manager,departement,imputation,ci,sap,work_center,managed_places,last_ldap_sync) VALUES ('1', '<EMAIL>', 'admin', 'Admin', 'System', '[\"ROLE_ADMIN\"]', '$2y$13$hMzHnQULfS/FVsXZjn9qYuPSPKELxjLOY8NSyVmj.ZK2x8latYAYe', NULL, NULL, NULL, 'IT', '0', NULL, NULL, NULL, NULL, '2025-06-02 15:39:32');
INSERT INTO newbe.commodity_code 
SELECT * FROM db_release.tbl_commodity_code;
///////////////////////////////////////////

php bin/console app:populate-ldap-users
php bin/console app:migrate-materials
php bin/console app:migrate-product-code
php bin/console app:migrate-legacy-optimized
php bin/console app:migrate-dmo
php bin/console app:projets-import
php bin/console app:link-package-dmo
php bin/console app:fix-material-relations-pdo