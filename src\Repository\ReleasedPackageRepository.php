<?php

namespace App\Repository;

use App\Entity\ReleasedPackage;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ReleasedPackage>
 */
class ReleasedPackageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ReleasedPackage::class);
    }

    public function findPackageBeStatuses(): array {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "SELECT
            p.id AS package_id,
            CASE
                WHEN COUNT(d.id) = SUM(
                    CASE
                        WHEN JSON_LENGTH(d.current_steps) = 0
                            OR JSON_CONTAINS_PATH(d.current_steps, 'one', '$.BE_0')
                        THEN 1 ELSE 0 END
                ) THEN 'BE_0'
                WHEN COUNT(d.id) = SUM(
                    CASE
                        WHEN JSON_CONTAINS_PATH(d.current_steps, 'one', '$.BE_1')
                        THEN 1 ELSE 0 END
                ) THEN 'BE_1'
                WHEN COUNT(d.id) = SUM(
                    CASE
                        WHEN JSON_CONTAINS_PATH(d.current_steps, 'one', '$.BE')
                        THEN 1 ELSE 0 END
                ) THEN 'BE'
                ELSE 'REMOVE'
            END AS be_status
        FROM released_package p
        LEFT JOIN document d ON d.rel_pack_id = p.id
        GROUP BY p.id";

        // Exécution et récupération id => statut
        $stmt = $conn->executeQuery($sql);
        // fetchAllKeyValue() retourne [package_id => be_status]
        return $stmt->fetchAllKeyValue();
    }
}
