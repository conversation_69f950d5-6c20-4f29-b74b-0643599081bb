<?php

/**
 * Test de la logique de destinations multiples dans le workflow
 */

// Simuler la logique du contrôleur
function testWorkflowDestinations() {
    echo "=== Test des destinations de workflow ===\n\n";
    
    $places_docType = [
        'MACH' => ['ORIGINE'=> 'Machining', 'DESTINATION'=> 'Machining'],
        'MOLD' => ['ORIGINE'=> 'Molding', 'DESTINATION'=> 'Molding'],
        'ASSY' => ['ORIGINE'=> 'Assembly', 'DESTINATION'=> ['Assembly', 'Quality']],
        'PUR'  => ['ORIGINE'=> ['Achat_F30','Achat_Rfq','Quality'], 'DESTINATION'=> 'Quality'],
        'DOC'  => ['ORIGINE'=> 'Quality', 'DESTINATION'=> 'Quality'],
    ];
    
    // Test 1: ASSY avec destinations multiples
    echo "Test 1: ASSY → Assembly + Quality\n";
    $newDocType = 'ASSY';
    $DESTINATION = $places_docType[$newDocType]['DESTINATION'];
    $markingStart = ['BE' => 1]; // État initial
    
    echo "Avant: " . json_encode($markingStart) . "\n";
    
    // Simuler la logique du contrôleur
    if (is_array($DESTINATION)) {
        foreach ($DESTINATION as $dest) {
            $markingStart[$dest] = 1;
        }
    } else {
        $markingStart[$DESTINATION] = 1;
    }
    
    echo "Après: " . json_encode($markingStart) . "\n";
    echo "Destinations: " . (is_array($DESTINATION) ? implode(', ', $DESTINATION) : $DESTINATION) . "\n\n";
    
    // Test 2: MACH avec destination simple
    echo "Test 2: MACH → Machining\n";
    $newDocType = 'MACH';
    $DESTINATION = $places_docType[$newDocType]['DESTINATION'];
    $markingStart = ['BE' => 1]; // État initial
    
    echo "Avant: " . json_encode($markingStart) . "\n";
    
    if (is_array($DESTINATION)) {
        foreach ($DESTINATION as $dest) {
            $markingStart[$dest] = 1;
        }
    } else {
        $markingStart[$DESTINATION] = 1;
    }
    
    echo "Après: " . json_encode($markingStart) . "\n";
    echo "Destinations: " . (is_array($DESTINATION) ? implode(', ', $DESTINATION) : $DESTINATION) . "\n\n";
    
    // Test 3: PUR avec origine multiple
    echo "Test 3: PUR avec gestion d'origine multiple\n";
    $newDocType = 'PUR';
    $ORIGINE = $places_docType['ASSY']['ORIGINE']; // Simuler un changement depuis ASSY
    $DESTINATION = $places_docType[$newDocType]['DESTINATION'];
    $markingStart = ['Assembly' => 1, 'Quality' => 1]; // État initial ASSY
    
    echo "Avant: " . json_encode($markingStart) . "\n";
    echo "Origine à supprimer: " . $ORIGINE . "\n";
    
    // Supprimer l'origine
    unset($markingStart[$ORIGINE]);
    
    // Ajouter la destination
    if (is_array($DESTINATION)) {
        foreach ($DESTINATION as $dest) {
            $markingStart[$dest] = 1;
        }
    } else {
        $markingStart[$DESTINATION] = 1;
    }
    
    echo "Après: " . json_encode($markingStart) . "\n";
    echo "Destinations: " . (is_array($DESTINATION) ? implode(', ', $DESTINATION) : $DESTINATION) . "\n\n";
}

// Test du mapping des noms d'affichage
function testDisplayNames() {
    echo "=== Test des noms d'affichage ===\n\n";
    
    $placeDisplayNames = [
        'Quality' => 'Qualité',
        'Assembly' => 'Assemblage',
        'Machining' => 'Usinage',
        'Molding' => 'Moulage',
        'Achat_F30' => 'F30',
        'Achat_Rfq' => 'RFQ',
    ];
    
    $testCases = [
        ['Assembly', 'Quality'],
        ['Machining'],
        ['Molding'],
        ['Quality'],
        ['Achat_Rfq'],
        ['Achat_F30'],
    ];
    
    foreach ($testCases as $places) {
        $displayPlaces = array_map(function($place) use ($placeDisplayNames) {
            return $placeDisplayNames[$place] ?? $place;
        }, $places);
        
        $placesText = implode(', ', $displayPlaces);
        echo "Places techniques: " . implode(', ', $places) . "\n";
        echo "Affichage utilisateur: " . $placesText . "\n";
        echo "Message complet: Le document va maintenant vers : " . $placesText . "\n\n";
    }
}

// Exécuter les tests
testWorkflowDestinations();
testDisplayNames();

echo "=== Tests terminés avec succès ===\n";
