-- Données de test pour les tables vides

-- ProductRange
INSERT INTO product_range (product_range, division) VALUES
('Connectors', 'Aerospace'),
('Cables', 'Aerospace'),
('Harnesses', 'Aerospace'),
('Sensors', 'Aerospace'),
('Automation', 'Industrial'),
('Robotics', 'Industrial'),
('Control Systems', 'Industrial'),
('Power Distribution', 'Industrial'),
('Solar', 'Energy'),
('Wind', 'Energy'),
('Hydro', 'Energy'),
('Nuclear', 'Energy'),
('Imaging', 'Medical'),
('Diagnostics', 'Medical'),
('Monitoring', 'Medical'),
('Therapy', 'Medical'),
('EV Systems', 'Automotive'),
('Infotainment', 'Automotive'),
('Safety', 'Automotive'),
('Powertrain', 'Automotive');

-- Project
INSERT INTO project (otp, title, project_manager_id) VALUES
('OTP-0001', 'Développement connecteur haute performance', 1),
('OTP-0002', 'Amélioration système de câblage', 1),
('OTP-0003', 'Nouveau capteur de température', 1),
('OTP-0004', 'Optimisation processus de fabrication', 1),
('OTP-0005', 'Réduction des coûts de production', 1),
('OTP-0006', 'Certification ISO 9001', 1),
('OTP-0007', 'Expansion marché asiatique', 1),
('OTP-0008', 'Développement produit médical', 1),
('OTP-0009', 'Intégration système automobile', 1),
('OTP-0010', 'Projet R&D énergie renouvelable', 1);

-- Phase
INSERT INTO phase (title, code, level, status, status_manuel, commentaire, projet_id) VALUES
('Conception', 'PH-0001-01', 1, 1, 1, 'Commentaire pour la phase Conception', 1),
('Développement', 'PH-0001-02', 2, 1, 0, 'Commentaire pour la phase Développement', 1),
('Prototypage', 'PH-0001-03', 3, 0, 1, 'Commentaire pour la phase Prototypage', 1),
('Tests', 'PH-0001-04', 4, 0, 0, 'Commentaire pour la phase Tests', 1),
('Validation', 'PH-0001-05', 5, 0, 0, 'Commentaire pour la phase Validation', 1),
('Production', 'PH-0001-06', 6, 0, 0, 'Commentaire pour la phase Production', 1),
('Déploiement', 'PH-0001-07', 7, 0, 0, 'Commentaire pour la phase Déploiement', 1),

('Conception', 'PH-0002-01', 1, 1, 1, 'Commentaire pour la phase Conception', 2),
('Développement', 'PH-0002-02', 2, 1, 0, 'Commentaire pour la phase Développement', 2),
('Prototypage', 'PH-0002-03', 3, 0, 1, 'Commentaire pour la phase Prototypage', 2),
('Tests', 'PH-0002-04', 4, 0, 0, 'Commentaire pour la phase Tests', 2),
('Validation', 'PH-0002-05', 5, 0, 0, 'Commentaire pour la phase Validation', 2),
('Production', 'PH-0002-06', 6, 0, 0, 'Commentaire pour la phase Production', 2),
('Déploiement', 'PH-0002-07', 7, 0, 0, 'Commentaire pour la phase Déploiement', 2),

('Conception', 'PH-0003-01', 1, 1, 1, 'Commentaire pour la phase Conception', 3),
('Développement', 'PH-0003-02', 2, 1, 0, 'Commentaire pour la phase Développement', 3),
('Prototypage', 'PH-0003-03', 3, 0, 1, 'Commentaire pour la phase Prototypage', 3),
('Tests', 'PH-0003-04', 4, 0, 0, 'Commentaire pour la phase Tests', 3),
('Validation', 'PH-0003-05', 5, 0, 0, 'Commentaire pour la phase Validation', 3),
('Production', 'PH-0003-06', 6, 0, 0, 'Commentaire pour la phase Production', 3),
('Déploiement', 'PH-0003-07', 7, 0, 0, 'Commentaire pour la phase Déploiement', 3);

-- Code
INSERT INTO code (title, code, level, status, work_center, phase_id) VALUES
('Conception mécanique', 'CD-PH-0001-01-01', 1, 'Active', 'WC001', 1),
('Conception électrique', 'CD-PH-0001-01-02', 2, 'Active', 'WC002', 1),
('Développement logiciel', 'CD-PH-0001-01-03', 3, 'Inactive', 'WC003', 1),
('Tests fonctionnels', 'CD-PH-0001-01-04', 4, 'Pending', 'WC004', 1),
('Documentation technique', 'CD-PH-0001-01-05', 5, 'Completed', 'WC005', 1),
('Gestion de projet', 'CD-PH-0001-01-06', 6, 'Active', 'WC001', 1),
('Assurance qualité', 'CD-PH-0001-01-07', 7, 'Active', 'WC002', 1),

('Conception mécanique', 'CD-PH-0001-02-01', 1, 'Active', 'WC001', 2),
('Conception électrique', 'CD-PH-0001-02-02', 2, 'Active', 'WC002', 2),
('Développement logiciel', 'CD-PH-0001-02-03', 3, 'Inactive', 'WC003', 2),
('Tests fonctionnels', 'CD-PH-0001-02-04', 4, 'Pending', 'WC004', 2),
('Documentation technique', 'CD-PH-0001-02-05', 5, 'Completed', 'WC005', 2),
('Gestion de projet', 'CD-PH-0001-02-06', 6, 'Active', 'WC001', 2),
('Assurance qualité', 'CD-PH-0001-02-07', 7, 'Active', 'WC002', 2),

('Conception mécanique', 'CD-PH-0002-01-01', 1, 'Active', 'WC001', 8),
('Conception électrique', 'CD-PH-0002-01-02', 2, 'Active', 'WC002', 8),
('Développement logiciel', 'CD-PH-0002-01-03', 3, 'Inactive', 'WC003', 8),
('Tests fonctionnels', 'CD-PH-0002-01-04', 4, 'Pending', 'WC004', 8),
('Documentation technique', 'CD-PH-0002-01-05', 5, 'Completed', 'WC005', 8),
('Gestion de projet', 'CD-PH-0002-01-06', 6, 'Active', 'WC001', 8),
('Assurance qualité', 'CD-PH-0002-01-07', 7, 'Active', 'WC002', 8);

-- Config
INSERT INTO config (periode, date_deb, date_fin) VALUES
(CURRENT_DATE(), DATE_SUB(CURRENT_DATE(), INTERVAL 15 DAY), DATE_ADD(CURRENT_DATE(), INTERVAL 15 DAY));

-- DMO
INSERT INTO dmo (date_init, description, requestor_id, decision, status, ex, indus_related, eng_owner_id, date_end, pr_number, last_modificator_id, last_update_date, ex_assessment, spent_time, type, document, product_range_id, project_relation_id) VALUES
(NOW(), 'Modification de conception pour améliorer la performance', 1, 'Approved', 1, 'EX0001', 1, 1, NOW(), 1001, 1, NOW(), 'Assessment for DMO #1', 20, 'Design', 'DOC-0001', 1, 1),
(NOW(), 'Correction d\'un défaut de fabrication', 1, 'Rejected', 0, 'EX0002', 0, 1, NOW(), 1002, 1, NOW(), 'Assessment for DMO #2', 15, 'Process', 'DOC-0002', 2, 2),
(NOW(), 'Optimisation du processus de production', 1, 'Pending', 1, 'EX0003', 1, 1, NOW(), 1003, 1, NOW(), 'Assessment for DMO #3', 30, 'Material', 'DOC-0003', 3, 3),
(NOW(), 'Mise à jour de la documentation technique', 1, 'Under Review', 0, 'EX0004', 0, 1, NOW(), 1004, 1, NOW(), 'Assessment for DMO #4', 10, 'Documentation', 'DOC-0004', 4, 1),
(NOW(), 'Changement de matériau pour réduction des coûts', 1, 'Approved', 1, 'EX0005', 1, 1, NOW(), 1005, 1, NOW(), 'Assessment for DMO #5', 25, 'Quality', 'DOC-0005', 5, 2);

-- Commentaire pour DMO
INSERT INTO commentaire (state, created_at, commentaire, user_id, type, dmo_id) VALUES
('draft', NOW(), 'Excellente proposition, je suis d\'accord avec les modifications.', 1, 'technical', 1),
('review', DATE_SUB(NOW(), INTERVAL 1 DAY), 'Il faudrait revoir certains aspects techniques avant validation.', 1, 'quality', 1),
('approved', DATE_SUB(NOW(), INTERVAL 2 DAY), 'Document conforme aux spécifications requises.', 1, 'process', 2),
('rejected', DATE_SUB(NOW(), INTERVAL 3 DAY), 'Quelques corrections mineures à apporter avant approbation finale.', 1, 'general', 2),
('draft', DATE_SUB(NOW(), INTERVAL 4 DAY), 'Validation technique effectuée avec succès.', 1, 'technical', 3),
('review', DATE_SUB(NOW(), INTERVAL 5 DAY), 'Des tests supplémentaires sont nécessaires.', 1, 'quality', 3),
('approved', DATE_SUB(NOW(), INTERVAL 6 DAY), 'Modifications approuvées, prêt pour la prochaine étape.', 1, 'process', 4),
('rejected', DATE_SUB(NOW(), INTERVAL 7 DAY), 'Révision nécessaire sur les points 3 et 4.', 1, 'general', 4),
('draft', DATE_SUB(NOW(), INTERVAL 8 DAY), 'Conforme aux normes ISO en vigueur.', 1, 'technical', 5),
('review', DATE_SUB(NOW(), INTERVAL 9 DAY), 'Vérification complète effectuée, aucun problème détecté.', 1, 'quality', 5);

-- Commentaire pour Document (utiliser des IDs de documents existants)
INSERT INTO commentaire (state, created_at, commentaire, user_id, type, documents_id) VALUES
('draft', NOW(), 'Excellente proposition, je suis d\'accord avec les modifications.', 1, 'technical', 1),
('review', DATE_SUB(NOW(), INTERVAL 1 DAY), 'Il faudrait revoir certains aspects techniques avant validation.', 1, 'quality', 1),
('approved', DATE_SUB(NOW(), INTERVAL 2 DAY), 'Document conforme aux spécifications requises.', 1, 'process', 2),
('rejected', DATE_SUB(NOW(), INTERVAL 3 DAY), 'Quelques corrections mineures à apporter avant approbation finale.', 1, 'general', 2),
('draft', DATE_SUB(NOW(), INTERVAL 4 DAY), 'Validation technique effectuée avec succès.', 1, 'technical', 3),
('review', DATE_SUB(NOW(), INTERVAL 5 DAY), 'Des tests supplémentaires sont nécessaires.', 1, 'quality', 3),
('approved', DATE_SUB(NOW(), INTERVAL 6 DAY), 'Modifications approuvées, prêt pour la prochaine étape.', 1, 'process', 4),
('rejected', DATE_SUB(NOW(), INTERVAL 7 DAY), 'Révision nécessaire sur les points 3 et 4.', 1, 'general', 4),
('draft', DATE_SUB(NOW(), INTERVAL 8 DAY), 'Conforme aux normes ISO en vigueur.', 1, 'technical', 5),
('review', DATE_SUB(NOW(), INTERVAL 9 DAY), 'Vérification complète effectuée, aucun problème détecté.', 1, 'quality', 5);

-- Impute (utiliser des IDs d'utilisateurs et de codes existants)
INSERT INTO impute (user_id, nb_heures, code_id, created_at) VALUES
(1, 8, 1, CURRENT_DATE()),
(1, 6, 2, DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)),
(1, 4, 3, DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY)),
(1, 8, 4, DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)),
(1, 7, 5, DATE_SUB(CURRENT_DATE(), INTERVAL 4 DAY)),
(1, 5, 6, DATE_SUB(CURRENT_DATE(), INTERVAL 5 DAY)),
(1, 8, 7, DATE_SUB(CURRENT_DATE(), INTERVAL 6 DAY)),
(1, 6, 8, DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)),
(1, 4, 9, DATE_SUB(CURRENT_DATE(), INTERVAL 8 DAY)),
(1, 8, 10, DATE_SUB(CURRENT_DATE(), INTERVAL 9 DAY)),
(1, 7, 11, DATE_SUB(CURRENT_DATE(), INTERVAL 10 DAY)),
(1, 5, 12, DATE_SUB(CURRENT_DATE(), INTERVAL 11 DAY)),
(1, 8, 13, DATE_SUB(CURRENT_DATE(), INTERVAL 12 DAY)),
(1, 6, 14, DATE_SUB(CURRENT_DATE(), INTERVAL 13 DAY)),
(1, 4, 15, DATE_SUB(CURRENT_DATE(), INTERVAL 14 DAY));

-- Associer DMO et ReleasedPackage
INSERT INTO released_package_dmo (released_package_id, dmo_id) VALUES
(1, 1),
(2, 1),
(3, 2),
(4, 3),
(5, 4),
(1, 5),
(2, 5);
