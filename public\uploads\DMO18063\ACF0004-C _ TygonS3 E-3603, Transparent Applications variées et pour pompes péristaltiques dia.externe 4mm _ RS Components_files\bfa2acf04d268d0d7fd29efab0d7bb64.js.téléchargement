Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){var productId=rs.web?rs.web.digitalData.product_page_id:"";if(productId!=="")return productId;else return"error"},transform:function(val){return val?val:""},load:"page",trigger:Bootstrapper.data.immediateTrigger,dataDefName:"Product Page DataLayer Product ID",collection:"Content Pages",
source:"Manage",priv:"false"},{id:"53565"})},53565)},-1,-1);