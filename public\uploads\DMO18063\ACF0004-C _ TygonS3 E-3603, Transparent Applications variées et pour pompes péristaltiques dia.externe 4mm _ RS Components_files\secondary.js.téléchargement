BV.define("_i18n_avatarIconSubmissionForm",["vendor/messageformat","vendor/handlebars/runtime","underscore","framework/util/bvtrackerqueue"],function(e,t,n,r){function i(e){try{return function(e){var t="";return t+="Image d'avatar",t}(e)}catch(t){return r.push(["error",new Error("MF error on `avatarIconSubmissionForm`: "+t.toString())]),""}}return t.registerHelper("_i18n_avatarIconSubmissionForm",function(){var e={},t=[].slice.call(arguments,0,arguments.length-1);return n(t).forEach(function(t){n.extend(e,t)}),i(e)}),i}),BV.define("hbs!submissionForm",["hbs","vendor/handlebars/runtime","hbs!rejectionErrors","hbs!submissionFieldsComment","hbs!submissionFieldsQuestion","hbs!submissionFieldsAnswer","hbs!submissionFieldsReview","hbs!submissionFieldsUserInfo","hbs!submissionFieldCategory","hbs!submissionLegend","hbs!submissionInput","hbs!submissionLabel","hbs!agreementsReviews","hbs!agreementsQuestions","template/helpers/equals","template/helpers/notEqual","template/helpers/ifHasFeature","template/helpers/extmsg","template/helpers/submissionField","_i18n_avatarIconSubmissionForm"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){var n;return l((n=(n=e.subject,n==null||n===!1?n:n.Type),typeof n===f?n.apply(e):n))}function d(e,t){return"True"}function v(e,t){return"False"}function m(e,t){return' <input type="hidden" name="agreedtotermsandconditions" value="false" class="bv-noremember"> '}function g(e,t){var n="",r;return n+=' <input type="hidden" name="ve" value="'+l((r=(r=e.apiConfig,r==null||r===!1?r:r.virtualEnvironment),typeof r===f?r.apply(e):r))+'" class="bv-noremember" /> ',n}function y(e,t){var n="",r;return n+=' <input type="hidden" name="displaycode" value="'+l((r=(r=e.apiConfig,r==null||r===!1?r:r.displaycode),typeof r===f?r.apply(e):r))+'" class="bv-noremember" /> ',n}function b(e,t){var r="",i;return r+=' <input type="hidden" name="hostedauthentication_callbackUrl" value="',(i=n.hostedAuthCallbackUrl)?i=i.call(e,{hash:{}}):(i=e.hostedAuthCallbackUrl,i=typeof i===f?i.apply(e):i),r+=l(i)+'" class="bv-noremember" /> ',r}function w(e,t){var r="",i;return r+=' <input type="hidden" name="user" value="',(i=n.user)?i=i.call(e,{hash:{}}):(i=e.user,i=typeof i===f?i.apply(e):i),r+=l(i)+'" class="bv-noremember" /> ',r}function E(e,t){var r="",i,s;r+=' <div class="bv-author-avatar"> ',s=n["if"].call(e,(i=e.author,i==null||i===!1?i:i._avatarImage),{hash:{},inverse:c.noop,fn:c.program(18,S,t)});if(s||s===0)r+=s;return r+=" </div> ",r}function S(e,t){var r="",i,s;r+=' <img src="'+l((i=(i=e.author,i==null||i===!1?i:i._avatarImage),typeof i===f?i.apply(e):i))+'" alt="',(s=n._i18n_avatarIconSubmissionForm)?s=s.call(e,{hash:{}}):(s=e._i18n_avatarIconSubmissionForm,s=typeof s===f?s.apply(e):s);if(s||s===0)r+=s;return r+='" class="bv-author-thumb"/> ',r}function x(e,t){var r="",i;r+=' <div class="bv-submission-section"> ',i=n["if"].call(e,e.rejectionErrors,{hash:{},inverse:c.noop,fn:c.program(21,T,t)});if(i||i===0)r+=i;r+=" ",i=n["if"].call(e,e.fingerprintScriptError,{hash:{},inverse:c.noop,fn:c.program(23,N,t)});if(i||i===0)r+=i;r+=' <div class="bv-fieldsets bv-input-fieldsets"> ',i=n["if"].call(e,e.comment,{hash:{},inverse:c.noop,fn:c.program(25,C,t)});if(i||i===0)r+=i;r+=" ",i=n["if"].call(e,e.question,{hash:{},inverse:c.noop,fn:c.program(27,k,t)});if(i||i===0)r+=i;r+=" ",i=n["if"].call(e,e.answer,{hash:{},inverse:c.noop,fn:c.program(29,L,t)});if(i||i===0)r+=i;r+=" ",i=n.each.call(e,e.fieldCategoryOrder,{hash:{},inverse:c.noop,fn:c.programWithDepth(A,t,e)});if(i||i===0)r+=i;r+=" </div> ",i=n.unless.call(e,e.shortForm,{hash:{},inverse:c.noop,fn:c.program(51,R,t)});if(i||i===0)r+=i;return r+=" </div> ",r}function T(e,t){var i="",s;i+=" ",s=c.invokePartial(r.rejectionErrors,"rejectionErrors",e,n,r);if(s||s===0)i+=s;return i+=" ",i}function N(e,t){var r="",i,s;return r+=' <div class="bv-submission-rejection-errors bv-focusable"> <span class="bv-submission-rejection-header-text"> ',s={hash:{}},r+=l((i=n.extmsg,i?i.call(e,"error_FAILED_TO_LOAD_FINGERPRINT_BEFORE",e.msgpack,s):h.call(e,"extmsg","error_FAILED_TO_LOAD_FINGERPRINT_BEFORE",e.msgpack,s)))+" </span> </div> ",r}function C(e,t){var i="",s;i+=" ",s=c.invokePartial(r.submissionFieldsComment,"submissionFieldsComment",e,n,r);if(s||s===0)i+=s;return i+=" ",i}function k(e,t){var i="",s;i+=" ",s=c.invokePartial(r.submissionFieldsQuestion,"submissionFieldsQuestion",e,n,r);if(s||s===0)i+=s;return i+=" ",i}function L(e,t){var i="",s;i+=" ",s=c.invokePartial(r.submissionFieldsAnswer,"submissionFieldsAnswer",e,n,r);if(s||s===0)i+=s;return i+=" ",i}function A(e,t,r){var i="",s,o,u;i+=" ",u={hash:{},inverse:c.noop,fn:c.programWithDepth(O,t,r)},o=(s=n.equals,s?s.call(e,e,"baseReviewQuestions",u):h.call(e,"equals",e,"baseReviewQuestions",u));if(o||o===0)i+=o;i+=" ",u={hash:{},inverse:c.noop,fn:c.programWithDepth(M,t,r)},o=(s=n.equals,s?s.call(e,e,"basicUserInfo",u):h.call(e,"equals",e,"basicUserInfo",u));if(o||o===0)i+=o;i+=" ",u={hash:{},inverse:c.noop,fn:c.programWithDepth(_,t,r)},o=(s=n.equals,s?s.call(e,e,"people",u):h.call(e,"equals",e,"people",u));if(o||o===0)i+=o;i+=" ",u={hash:{},inverse:c.noop,fn:c.programWithDepth(D,t,r)},o=(s=n.equals,s?s.call(e,e,"ratings",u):h.call(e,"equals",e,"ratings",u));if(o||o===0)i+=o;i+=" ",u={hash:{},inverse:c.noop,fn:c.programWithDepth(P,t,r)},o=(s=n.equals,s?s.call(e,e,"product",u):h.call(e,"equals",e,"product",u));if(o||o===0)i+=o;i+=" ",u={hash:{},inverse:c.noop,fn:c.programWithDepth(H,t,r)},o=(s=n.equals,s?s.call(e,e,"injected",u):h.call(e,"equals",e,"injected",u));if(o||o===0)i+=o;i+=" ",u={hash:{},inverse:c.noop,fn:c.programWithDepth(B,t,r)},o=(s=n.equals,s?s.call(e,e,"default",u):h.call(e,"equals",e,"default",u));if(o||o===0)i+=o;i+=" ",u={hash:{},inverse:c.noop,fn:c.programWithDepth(j,t,r)},o=(s=n.equals,s?s.call(e,e,"netPromoterScore",u):h.call(e,"equals",e,"netPromoterScore",u));if(o||o===0)i+=o;return i+=" ",i}function O(e,t,i){var s="",o;s+=" ",o=c.invokePartial(r.submissionFieldsReview,"submissionFieldsReview",i,n,r);if(o||o===0)s+=o;return s+=" ",s}function M(e,t,i){var s="",o;s+=" ",o=c.invokePartial(r.submissionFieldsUserInfo,"submissionFieldsUserInfo",i,n,r);if(o||o===0)s+=o;return s+=" ",s}function _(e,t,i){var s="",o,u;s+=" ",u=c.invokePartial(r.submissionFieldCategory,"submissionFieldCategory",(o=i.specialFields,o==null||o===!1?o:o.people),n,r);if(u||u===0)s+=u;return s+=" ",s}function D(e,t,i){var s="",o,u;s+=" ",u=c.invokePartial(r.submissionFieldCategory,"submissionFieldCategory",(o=i.specialFields,o==null||o===!1?o:o.ratings),n,r);if(u||u===0)s+=u;return s+=" ",s}function P(e,t,i){var s="",o,u;s+=" ",u=c.invokePartial(r.submissionFieldCategory,"submissionFieldCategory",(o=i.specialFields,o==null||o===!1?o:o.product),n,r);if(u||u===0)s+=u;return s+=" ",s}function H(e,t,i){var s="",o,u;s+=" ",u=c.invokePartial(r.submissionFieldCategory,"submissionFieldCategory",(o=i.specialFields,o==null||o===!1?o:o.injected),n,r);if(u||u===0)s+=u;return s+=" ",s}function B(e,t,i){var s="",o,u;s+=" ",u=c.invokePartial(r.submissionFieldCategory,"submissionFieldCategory",(o=i.specialFields,o==null||o===!1?o:o["default"]),n,r);if(u||u===0)s+=u;return s+=" ",s}function j(e,t,r){var i="",s;i+=" ",s=n["with"].call(e,r,{hash:{},inverse:c.noop,fn:c.program(47,F,t)});if(s||s===0)i+=s;return i+=" ",i}function F(e,t){var r="",i,s,o;r+=" ",o={hash:{inputClasses:"bv-form-ignore bv-focusable",numHelpers:"2"},inverse:c.noop,fn:c.program(48,I,t)},s=(i=n.submissionField,i?i.call(e,"netpromoterscore",e.formFields,e,o):h.call(e,"submissionField","netpromoterscore",e.formFields,e,o));if(s||s===0)r+=s;return r+=" ",r}function I(e,t){var i="",s,o,u;i+=' <fieldset class="bv-fieldset bv-fieldset-'+l((s=(s=e.schema,s==null||s===!1?s:s.Id),typeof s===f?s.apply(e):s))+" bv-",(o=n.type)?o=o.call(e,{hash:{}}):(o=e.type,o=typeof o===f?o.apply(e):o),i+=l(o)+"-field ",(o=n.fieldsetClasses)?o=o.call(e,{hash:{}}):(o=e.fieldsetClasses,o=typeof o===f?o.apply(e):o),i+=l(o)+'"> ',o=c.invokePartial(r.submissionLegend,"submissionLegend",e,n,r);if(o||o===0)i+=o;i+=' <div class="bv-fieldset-arrowicon"></div> <div class="bv-fieldset-inner"> ',o=c.invokePartial(r.submissionInput,"submissionInput",e,n,r);if(o||o===0)i+=o;i+=" ",u={hash:{},inverse:c.noop,fn:c.program(49,q,t)},o=(s=n.submissionField,s?s.call(e,"netpromotercomment",(s=e.rawData,s==null||s===!1?s:s.formFields),e.rawData,u):h.call(e,"submissionField","netpromotercomment",(s=e.rawData,s==null||s===!1?s:s.formFields),e.rawData,u));if(o||o===0)i+=o;return i+=" </div> </fieldset> ",i}function q(e,t){var i="",s,o;i+=' <fieldset class="bv-fieldset bv-fieldset-'+l((s=(s=e.schema,s==null||s===!1?s:s.Id),typeof s===f?s.apply(e):s))+" bv-",(o=n.type)?o=o.call(e,{hash:{}}):(o=e.type,o=typeof o===f?o.apply(e):o),i+=l(o)+"-field ",(o=n.fieldsetClasses)?o=o.call(e,{hash:{}}):(o=e.fieldsetClasses,o=typeof o===f?o.apply(e):o),i+=l(o)+'"> <div class="bv-'+l((s=(s=e.schema,s==null||s===!1?s:s.Id),typeof s===f?s.apply(e):s))+'-wrapper"> ',o=c.invokePartial(r.submissionLabel,"submissionLabel",e,n,r);if(o||o===0)i+=o;i+=" ",o=c.invokePartial(r.submissionInput,"submissionInput",e,n,r);if(o||o===0)i+=o;return i+=" </div> </fieldset> ",i}function R(e,t){var r="",i,s,o;r+=' <div class="bv-fieldsets bv-fieldsets-actions"> ',o={hash:{},inverse:c.noop,fn:c.programWithDepth(U,t,e)},s=(i=n.equals,i?i.call(e,e.contentType,"review",o):h.call(e,"equals",e.contentType,"review",o));if(s||s===0)r+=s;r+=" ",s=n["if"].call(e,e.question,{hash:{},inverse:c.noop,fn:c.programWithDepth(X,t,e)});if(s||s===0)r+=s;r+=" ",s=n["if"].call(e,e.answer,{hash:{},inverse:c.noop,fn:c.programWithDepth(X,t,e)});if(s||s===0)r+=s;return r+=' <fieldset class="bv-form-actions bv-fieldset"> <div class="bv-fieldset-arrowicon"></div> <div class="bv-fieldset-inner"> <div class="bv-actions-container"> <p id="bv-casltext-',(s=n.contentType)?s=s.call(e,{hash:{}}):(s=e.contentType,s=typeof s===f?s.apply(e):s),r+=l(s)+'" class="bv-fieldset-casltext">',o={hash:{}},r+=l((i=n.extmsg,i?i.call(e,"display_casltext",e.msgpack,e,o):h.call(e,"extmsg","display_casltext",e.msgpack,e,o)))+'</p> <button type="button" aria-describedby="bv-casltext-',(s=n.contentType)?s=s.call(e,{hash:{}}):(s=e.contentType,s=typeof s===f?s.apply(e):s),r+=l(s)+'" class="bv-form-actions-submit bv-submit bv-focusable bv-submission-button-submit" type="submit" name="bv-submit-button">',o={hash:{prefix:"submit_"}},r+=l((i=n.extmsg,i?i.call(e,e.contentType,e.msgpack,e,o):h.call(e,"extmsg",e.contentType,e.msgpack,e,o)))+'</button> <button type="button" class="bv-form-action bv-cancel bv-submission-button-submit">',o={hash:{}},r+=l((i=n.extmsg,i?i.call(e,"cancel",e.msgpack,e,o):h.call(e,"extmsg","cancel",e.msgpack,e,o)))+"</button> </div> </div> </fieldset> </div> ",r}function U(e,t,r){var i="",s,o;i+=" ",o=n.each.call(e,(s=e.agreements,s==null||s===!1?s:s.reviews),{hash:{},inverse:c.noop,fn:c.programWithDepth(z,t,e,r)});if(o||o===0)i+=o;return i+=" ",i}function z(e,t,r,i){var s="",o,u,a;s+=" ",a={hash:{inputType:"checkbox"},inverse:c.noop,fn:c.program(54,W,t)},u=(o=n.submissionField,o?o.call(e,e.id,(o=i.agreements,o==null||o===!1?o:o.reviews),r,a):h.call(e,"submissionField",e.id,(o=i.agreements,o==null||o===!1?o:o.reviews),r,a));if(u||u===0)s+=u;return s+=" ",s}function W(e,t){var i="",s;i+=" ",s=c.invokePartial(r.agreementsReviews,"agreementsReviews",e,n,r);if(s||s===0)i+=s;return i+=" ",i}function X(e,t,r){var i="",s,o;i+=" ",o=n.each.call(e,(s=e.agreements,s==null||s===!1?s:s.questions),{hash:{},inverse:c.noop,fn:c.programWithDepth(V,t,e,r)});if(o||o===0)i+=o;return i+=" ",i}function V(e,t,r,i){var s="",o,u,a;s+=" ",a={hash:{inputType:"checkbox"},inverse:c.noop,fn:c.program(58,$,t)},u=(o=n.submissionField,o?o.call(e,e.id,(o=i.agreements,o==null||o===!1?o:o.questions),r,a):h.call(e,"submissionField",e.id,(o=i.agreements,o==null||o===!1?o:o.questions),r,a));if(u||u===0)s+=u;return s+=" ",s}function $(e,t){var i="",s;i+=" ",s=c.invokePartial(r.agreementsQuestions,"agreementsQuestions",e,n,r);if(s||s===0)i+=s;return i+=" ",i}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s="",o,u,a,f="function",l=this.escapeExpression,c=this,h=n.helperMissing;s+='<div class="bv-compat"> <form id="bv-submit',(o=n.contentType)?o=o.call(t,{hash:{}}):(o=t.contentType,o=typeof o===f?o.apply(t):o),s+=l(o)+"-"+l((o=(o=t.subject,o==null||o===!1?o:o.Type),typeof o===f?o.apply(t):o))+"-"+l((o=(o=t.subject,o==null||o===!1?o:o.SanitizedId),typeof o===f?o.apply(t):o))+"-",(u=n.modelCid)?u=u.call(t,{hash:{}}):(u=t.modelCid,u=typeof u===f?u.apply(t):u),s+=l(u)+'" target="bv-submission-target-',(u=n.channelId)?u=u.call(t,{hash:{}}):(u=t.channelId,u=typeof u===f?u.apply(t):u),s+=l(u)+'" action="https:'+l((o=(o=t.apiConfig,o==null||o===!1?o:o.baseUrl),typeof o===f?o.apply(t):o))+"submit",a={hash:{},inverse:c.noop,fn:c.program(1,p,i)},u=(o=n.equals,o?o.call(t,t.contentType,"comment",a):h.call(t,"equals",t.contentType,"comment",a));if(u||u===0)s+=u;(u=n.contentType)?u=u.call(t,{hash:{}}):(u=t.contentType,u=typeof u===f?u.apply(t):u),s+=l(u)+'.json?" accept-charset="utf-8" method="POST" class="bv-form" onsubmit="return false;"> <input type="hidden" name="passkey" value="'+l((o=(o=t.apiConfig,o==null||o===!1?o:o.passkey),typeof o===f?o.apply(t):o))+'" class="bv-noremember" /> <input type="hidden" name="action" value="submit" class="bv-noremember" /> <input type="hidden" name="apiversion" value="'+l((o=(o=t.apiConfig,o==null||o===!1?o:o.apiversion),typeof o===f?o.apply(t):o))+'" class="bv-noremember" /> <input type="hidden" name="callback" value="',(u=n.postCallback)?u=u.call(t,{hash:{}}):(u=t.postCallback,u=typeof u===f?u.apply(t):u),s+=l(u)+'" class="bv-noremember" /> <input type="hidden" name="'+l((o=(o=t.subject,o==null||o===!1?o:o.Type),typeof o===f?o.apply(t):o))+'id" value="'+l((o=(o=t.subject,o==null||o===!1?o:o.Id),typeof o===f?o.apply(t):o))+'" class="bv-noremember" /> <input type="hidden" name="campaignid" value="',(u=n.campaignId)?u=u.call(t,{hash:{}}):(u=t.campaignId,u=typeof u===f?u.apply(t):u),s+=l(u)+'" class="bv-noremember" /> <input type="hidden" name="fp" value="" class="bv-noremember" /> <input type="hidden" name="forceutf8" value="&#9760;" class="bv-noremember" /> <input type="hidden" name="locale" value="',(u=n.locale)?u=u.call(t,{hash:{}}):(u=t.locale,u=typeof u===f?u.apply(t):u),s+=l(u)+'" class="bv-noremember" /> <input type="hidden" name="contextdatavalue_VerifiedPurchaser" value="',u=n["if"].call(t,t.verifiedPurchaser,{hash:{},inverse:c.program(5,v,i),fn:c.program(3,d,i)});if(u||u===0)s+=u;s+='" class="bv-noremember" /> ',a={hash:{},inverse:c.noop,fn:c.program(7,m,i)},u=(o=n.notEqual,o?o.call(t,t.contentType,"comment",a):h.call(t,"notEqual",t.contentType,"comment",a));if(u||u===0)s+=u;s+=" ",u=n["if"].call(t,(o=t.apiConfig,o==null||o===!1?o:o.virtualEnvironment),{hash:{},inverse:c.noop,fn:c.program(9,g,i)});if(u||u===0)s+=u;s+=" ",u=n["if"].call(t,(o=t.apiConfig,o==null||o===!1?o:o.displaycode),{hash:{},inverse:c.noop,fn:c.program(11,y,i)});if(u||u===0)s+=u;s+=" ",u=n["if"].call(t,t.hostedAuth,{hash:{},inverse:c.noop,fn:c.program(13,b,i)});if(u||u===0)s+=u;s+=" ",u=n["if"].call(t,t.user,{hash:{},inverse:c.noop,fn:c.program(15,w,i)});if(u||u===0)s+=u;s+='  <input type="hidden" name="sendemailalertwhenpublished" class="bv-noremember bv-form-ignore" value="true"/> <input type="hidden" name="sendemailalertwhencommented" class="bv-noremember bv-form-ignore" value="true"/> <input type="hidden" name="sendemailalertwhenanswered" class="bv-noremember bv-form-ignore" value="true"/> ',a={hash:{},inverse:c.noop,fn:c.program(17,E,i)},u=(o=n.ifHasFeature,o?o.call(t,"inlineProfile",t,a):h.call(t,"ifHasFeature","inlineProfile",t,a));if(u||u===0)s+=u;s+=" ",u=n["if"].call(t,t.formFields,{hash:{},inverse:c.noop,fn:c.program(20,x,i)});if(u||u===0)s+=u;return s+=" </form> </div> ",s});return t.registerPartial("submissionForm",n),n.deps=["rejectionErrors","submissionFieldsComment","submissionFieldsQuestion","submissionFieldsAnswer","submissionFieldsReview","submissionFieldsUserInfo","submissionFieldCategory","submissionLegend","submissionInput","submissionLabel","agreementsReviews","agreementsQuestions"],n.tplMountedViews=[],n}),BV.define("hbs!rejectionErrors",["hbs","vendor/handlebars/runtime","template/helpers/extmsg"],function(e,t){var n=t.template(function(e,t,n,r,i){function h(e,t,r){var i="",s,o;return i+=' <li class="bv-submission-rejection-errors-list-item"> ',o={hash:{prefix:"error_ERROR_FORM_REJECTED_"}},i+=l((s=n.extmsg,s?s.call(e,e,r.msgPack,r,o):f.call(e,"extmsg",e,r.msgPack,r,o)))+" </li> ",i}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f=n.helperMissing,l=this.escapeExpression,c=this;s+='<div class="bv-submission-rejection-errors bv-focusable"> <span class="bv-helper-icon" aria-hidden="true"> <span class="bv-helper-icon-negative"> ✘ </span> </span> <span class="bv-submission-rejection-header-text">',a={hash:{prefix:"error_"}},s+=l((o=n.extmsg,o?o.call(t,"ERROR_FORM_REJECTED_HEADER",t.msgPack,t,a):f.call(t,"extmsg","ERROR_FORM_REJECTED_HEADER",t.msgPack,t,a)))+'</span> <ul class="bv-submission-rejection-errors-list"> ',u=n.each.call(t,t.rejectionErrors,{hash:{},inverse:c.noop,fn:c.programWithDepth(h,i,t)});if(u||u===0)s+=u;return s+=" </ul> </div> ",s});return t.registerPartial("rejectionErrors",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("hbs!submissionFieldsComment",["hbs","vendor/handlebars/runtime","hbs!submissionField","template/helpers/submissionField"],function(e,t){var n=t.template(function(e,t,n,r,i){function f(e,t,r){var i="",s,o,f;i+=" ",f={hash:{fieldsetClasses:"bv-nocount",inputType:"textarea"},inverse:u.noop,fn:u.program(2,l,t)},o=(s=n.submissionField,s?s.call(e,"commenttext",e,r,f):a.call(e,"submissionField","commenttext",e,r,f));if(o||o===0)i+=o;return i+=" ",i}function l(e,t){var i="",s;i+=" ",s=u.invokePartial(r.submissionField,"submissionField",e,n,r);if(s||s===0)i+=s;return i+=" ",i}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s="",o,u=this,a=n.helperMissing;o=n["with"].call(t,t.formFields,{hash:{},inverse:u.noop,fn:u.programWithDepth(f,i,t)});if(o||o===0)s+=o;return s+=" ",s});return t.registerPartial("submissionFieldsComment",n),n.deps=["submissionField"],n.tplMountedViews=[],n}),BV.define("hbs!submissionFieldsQuestion",["hbs","vendor/handlebars/runtime","hbs!submissionField","template/helpers/submissionField"],function(e,t){var n=t.template(function(e,t,n,r,i){function f(e,t,r){var i="",s,o,f;i+=" ",f={hash:{fieldsetClasses:"bv-nocount",inputType:"textarea"},inverse:u.noop,fn:u.program(2,l,t)},o=(s=n.submissionField,s?s.call(e,"questionsummary",e,r,f):a.call(e,"submissionField","questionsummary",e,r,f));if(o||o===0)i+=o;return i+=" ",i}function l(e,t){var i="",s;i+=" ",s=u.invokePartial(r.submissionField,"submissionField",e,n,r);if(s||s===0)i+=s;return i+=" ",i}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s="",o,u=this,a=n.helperMissing;o=n["with"].call(t,t.formFields,{hash:{},inverse:u.noop,fn:u.programWithDepth(f,i,t)});if(o||o===0)s+=o;return s+=" ",s});return t.registerPartial("submissionFieldsQuestion",n),n.deps=["submissionField"],n.tplMountedViews=[],n}),BV.define("hbs!submissionFieldsAnswer",["hbs","vendor/handlebars/runtime","hbs!submissionLabel","hbs!submissionInput","template/helpers/submissionField"],function(e,t){var n=t.template(function(e,t,n,r,i){function c(e,t,r){var i="",s,o,u;i+=" ",u={hash:{fieldsetClasses:"bv-nocount",inputType:"textarea"},inverse:f.noop,fn:f.program(2,h,t)},o=(s=n.submissionField,s?s.call(e,"answertext",e,r,u):l.call(e,"submissionField","answertext",e,r,u));if(o||o===0)i+=o;return i+=" ",i}function h(e,t){var i="",s,o;i+=' <fieldset class="bv-fieldset bv-fieldset-'+a((s=(s=e.schema,s==null||s===!1?s:s.Id),typeof s===u?s.apply(e):s))+" bv-",(o=n.type)?o=o.call(e,{hash:{}}):(o=e.type,o=typeof o===u?o.apply(e):o),i+=a(o)+"-field ",(o=n.fieldsetClasses)?o=o.call(e,{hash:{}}):(o=e.fieldsetClasses,o=typeof o===u?o.apply(e):o),i+=a(o)+'"> <div class="bv-fieldset-arrowicon"></div> <div class="bv-fieldset-inner"> ',o=f.invokePartial(r.submissionLabel,"submissionLabel",e,n,r);if(o||o===0)i+=o;i+=" ",o=f.invokePartial(r.submissionInput,"submissionInput",e,n,r);if(o||o===0)i+=o;return i+=" </div> </fieldset> ",i}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s="",o,u="function",a=this.escapeExpression,f=this,l=n.helperMissing;o=n["with"].call(t,t.formFields,{hash:{},inverse:f.noop,fn:f.programWithDepth(c,i,t)});if(o||o===0)s+=o;return s+=" ",s});return t.registerPartial("submissionFieldsAnswer",n),n.deps=["submissionLabel","submissionInput"],n.tplMountedViews=[],n}),BV.define("hbs!submissionLegend",["hbs","vendor/handlebars/runtime","hbs!submissionHelper","template/helpers/extmsg","template/helpers/equals"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){return"*"}function d(e,t){var r="",i,s;return r+=' <span class="bv-off-screen bv-fieldset-label-slider-left">',s={hash:{}},r+=l((i=n.extmsg,i?i.call(e,"offscreen_value_of_one_means",e.msgpack,e,s):f.call(e,"extmsg","offscreen_value_of_one_means",e.msgpack,e,s))),s={hash:{prefix:"slider_dimension_1_"}},r+=l((i=n.extmsg,i?i.call(e,(i=e.schema,i==null||i===!1?i:i.Id),e.msgpack,e,s):f.call(e,"extmsg",(i=e.schema,i==null||i===!1?i:i.Id),e.msgpack,e,s)))+'</span> <span class="bv-off-screen bv-fieldset-label-slider-right">',s={hash:{}},r+=l((i=n.extmsg,i?i.call(e,"offscreen_value_max_means",e.msgpack,e.dimsumField,s):f.call(e,"extmsg","offscreen_value_max_means",e.msgpack,e.dimsumField,s))),s={hash:{prefix:"slider_dimension_2_"}},r+=l((i=n.extmsg,i?i.call(e,(i=e.schema,i==null||i===!1?i:i.Id),e.msgpack,e,s):f.call(e,"extmsg",(i=e.schema,i==null||i===!1?i:i.Id),e.msgpack,e,s)))+"</span> ",r}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s="",o,u,a,f=n.helperMissing,l=this.escapeExpression,c="function",h=this;s+='<legend class="bv-fieldset-label-wrapper" id="bv-fieldset-label-'+l((o=(o=t.schema,o==null||o===!1?o:o.Id),typeof o===c?o.apply(t):o))+'"> <span class="bv-fieldset-label"> <span class="bv-fieldset-label-text"> ',a={hash:{prefix:"display_",defaultDisplay:(o=t.schema,o==null||o===!1?o:o.Label)}},s+=l((o=n.extmsg,o?o.call(t,(o=t.schema,o==null||o===!1?o:o.Id),t.msgpack,t,a):f.call(t,"extmsg",(o=t.schema,o==null||o===!1?o:o.Id),t.msgpack,t,a))),u=n["if"].call(t,(o=t.validation,o==null||o===!1?o:o.required),{hash:{},inverse:h.noop,fn:h.program(1,p,i)});if(u||u===0)s+=u;s+=' </span>  <span aria-hidden="false" class="bv-off-screen bv-field-aria-validation"></span> ',a={hash:{},inverse:h.noop,fn:h.program(3,d,i)},u=(o=n.equals,o?o.call(t,(o=t.dimsumField,o==null||o===!1?o:o.type),"SLIDER",a):f.call(t,"equals",(o=t.dimsumField,o==null||o===!1?o:o.type),"SLIDER",a));if(u||u===0)s+=u;s+=" </span> </legend> ",u=h.invokePartial(r.submissionHelper,"submissionHelper",t,n,r);if(u||u===0)s+=u;return s+=" ",s});return t.registerPartial("submissionLegend",n),n.deps=["submissionHelper"],n.tplMountedViews=[],n}),BV.define("hbs!submissionFieldRadio",["hbs","vendor/handlebars/runtime","hbs!submissionLegend","hbs!submissionInput"],function(e,t){var n=t.template(function(e,t,n,r,i){this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s="",o,u,a="function",f=this.escapeExpression,l=this;s+='<fieldset class="bv-fieldset bv-fieldset-'+f((o=(o=t.schema,o==null||o===!1?o:o.Id),typeof o===a?o.apply(t):o))+" bv-",(u=n.type)?u=u.call(t,{hash:{}}):(u=t.type,u=typeof u===a?u.apply(t):u),s+=f(u)+"-field ",(u=n.fieldsetClasses)?u=u.call(t,{hash:{}}):(u=t.fieldsetClasses,u=typeof u===a?u.apply(t):u),s+=f(u)+'"> ',u=l.invokePartial(r.submissionLegend,"submissionLegend",t,n,r);if(u||u===0)s+=u;s+=' <span class="bv-fieldset-arrowicon"></span> <span class="bv-fieldset-inner"> ',u=l.invokePartial(r.submissionInput,"submissionInput",t,n,r);if(u||u===0)s+=u;return s+=" </span> </fieldset> ",s});return t.registerPartial("submissionFieldRadio",n),n.deps=["submissionLegend","submissionInput"],n.tplMountedViews=[],n}),BV.define("_i18n_reviewPhotoMaxCount",["vendor/messageformat","vendor/handlebars/runtime","underscore","framework/util/bvtrackerqueue"],function(e,t,n,r){function i(t){try{return function(t){var n="";if(!t)throw new Error("MessageFormat: No data passed to function.");var r="maxPhotos",i=t[r],s=0,o={one:function(e){var t="";t+="Ajoutez ";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.maxPhotos,t+=" photo maximum",t},other:function(e){var t="";t+="Ajoutez ";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.maxPhotos,t+=" photos maximum",t}};return o[i+""]?n+=o[i+""](t):n+=(o[e.locale.en(i-s)]||o.other)(t),n}(t)}catch(n){return r.push(["error",new Error("MF error on `reviewPhotoMaxCount`: "+n.toString())]),""}}return t.registerHelper("_i18n_reviewPhotoMaxCount",function(){var e={},t=[].slice.call(arguments,0,arguments.length-1);return n(t).forEach(function(t){n.extend(e,t)}),i(e)}),i}),BV.define("_i18n_reviewPhotoRemainingCount",["vendor/messageformat","vendor/handlebars/runtime","underscore","framework/util/bvtrackerqueue"],function(e,t,n,r){function i(t){try{return function(t){var n="";n+="(";if(!t)throw new Error("MessageFormat: No data passed to function.");var r="remainingPhotos",i=t[r],s=0,o={one:function(e){var t="";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.remainingPhotos,t+=" restante",t},other:function(e){var t="";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.remainingPhotos,t+=" restantes",t}};return o[i+""]?n+=o[i+""](t):n+=(o[e.locale.en(i-s)]||o.other)(t),n+=")",n}(t)}catch(n){return r.push(["error",new Error("MF error on `reviewPhotoRemainingCount`: "+n.toString())]),""}}return t.registerHelper("_i18n_reviewPhotoRemainingCount",function(){var e={},t=[].slice.call(arguments,0,arguments.length-1);return n(t).forEach(function(t){n.extend(e,t)}),i(e)}),i}),BV.define("hbs!reviewPhotoCounts",["hbs","vendor/handlebars/runtime","_i18n_reviewPhotoMaxCount","_i18n_reviewPhotoRemainingCount"],function(e,t){var n=t.template(function(e,t,n,r,i){function c(e,t){var r="",i,s,o;r+="  ",o={hash:{}},s=(i=n._i18n_reviewPhotoRemainingCount,i?i.call(e,e,o):f.call(e,"_i18n_reviewPhotoRemainingCount",e,o));if(s||s===0)r+=s;return r+=" ",r}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f=n.helperMissing,l=this;s+="   ",a={hash:{}},u=(o=n._i18n_reviewPhotoMaxCount,o?o.call(t,t,a):f.call(t,"_i18n_reviewPhotoMaxCount",t,a));if(u||u===0)s+=u;s+=" ",u=n["if"].call(t,t.remainingPhotos,{hash:{},inverse:l.noop,fn:l.program(1,c,i)});if(u||u===0)s+=u;return s+=" ",s});return t.registerPartial("reviewPhotoCounts",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("template/helpers/ifInSchema",["vendor/handlebars/runtime","underscore"],function(e,t){function n(n,r,i){var s=n.split(","),o=t(s).find(function(t){var n=r[t];return!e.Utils.isEmpty(n)&&n.enabled});return t(o).isUndefined()?i.inverse(r):i.fn(r)}return e.registerHelper("ifInSchema",n),n}),BV.define("hbs!submissionFieldsReview",["hbs","vendor/handlebars/runtime","hbs!submissionField","hbs!submissionFieldRadio","hbs!submissionInput","hbs!reviewPhotoCounts","template/helpers/ifHasFeature","template/helpers/submissionField","template/helpers/extmsg","template/helpers/renderIcon","template/helpers/mountView","template/helpers/conditionalContainer","template/helpers/ifInSchema"],function(e,t){var n=t.template(function(e,t,n,r,i){function c(e,t,r){var i="",s,o,f;i+="  ",f={hash:{},inverse:u.program(7,v,t),fn:u.program(2,h,t)},o=(s=n.ifHasFeature,s?s.call(e,"submissionReviewTitleInputFirst",r,f):a.call(e,"ifHasFeature","submissionReviewTitleInputFirst",r,f));if(o||o===0)i+=o;i+=" ",f={hash:{fieldsetClasses:"bv-nocount"},inverse:u.noop,fn:u.programWithDepth(m,t,e,r)},o=(s=n.submissionField,s?s.call(e,"reviewtext",e,r,f):a.call(e,"submissionField","reviewtext",e,r,f));if(o||o===0)i+=o;i+=" ",f={hash:{fieldsetClasses:"bv-nocount",inputClasses:"bv-focusable"},inverse:u.noop,fn:u.program(5,d,t)},o=(s=n.submissionField,s?s.call(e,"isrecommended",e,r,f):a.call(e,"submissionField","isrecommended",e,r,f));if(o||o===0)i+=o;return i+=" ",i}function h(e,t){var r="",i,s,o;r+=" ",o={hash:{fieldsetClasses:"bv-fieldset-active bv-nocount"},inverse:u.noop,fn:u.program(3,p,t)},s=(i=n.submissionField,i?i.call(e,"title",e.formFields,e,o):a.call(e,"submissionField","title",e.formFields,e,o));if(s||s===0)r+=s;r+=" ",o={hash:{fieldsetClasses:"",inputClasses:"bv-required",numHelpers:"1"},inverse:u.noop,fn:u.program(5,d,t)},s=(i=n.submissionField,i?i.call(e,"rating",e.formFields,e,o):a.call(e,"submissionField","rating",e.formFields,e,o));if(s||s===0)r+=s;return r+=" ",r}function p(e,t){var i="",s;i+=" ",s=u.invokePartial(r.submissionField,"submissionField",e,n,r);if(s||s===0)i+=s;return i+=" ",i}function d(e,t){var i="",s;i+=" ",s=u.invokePartial(r.submissionFieldRadio,"submissionFieldRadio",e,n,r);if(s||s===0)i+=s;return i+=" ",i}function v(e,t){var r="",i,s,o;r+=" ",o={hash:{fieldsetClasses:"bv-fieldset-active",inputClasses:"bv-required",numHelpers:"1"},inverse:u.noop,fn:u.program(5,d,t)},s=(i=n.submissionField,i?i.call(e,"rating",e.formFields,e,o):a.call(e,"submissionField","rating",e.formFields,e,o));if(s||s===0)r+=s;r+=" ",o={hash:{fieldsetClasses:"bv-nocount"},inverse:u.noop,fn:u.program(3,p,t)},s=(i=n.submissionField,i?i.call(e,"title",e.formFields,e,o):a.call(e,"submissionField","title",e.formFields,e,o));if(s||s===0)r+=s;return r+=" ",r}function m(e,t,i,s){var o="",c,h,p;o+=' <fieldset class="bv-fieldset bv-focusable bv-fieldset-'+f((c=(c=e.schema,c==null||c===!1?c:c.Id),typeof c===l?c.apply(e):c))+" bv-",(h=n.type)?h=h.call(e,{hash:{}}):(h=e.type,h=typeof h===l?h.apply(e):h),o+=f(h)+"-field ",(h=n.fieldsetClasses)?h=h.call(e,{hash:{}}):(h=e.fieldsetClasses,h=typeof h===l?h.apply(e):h),o+=f(h)+'" tabindex="0"> <div class="bv-fieldset-arrowicon"></div> <div class="bv-fieldset-inner"> <label for="bv-textarea-field-'+f((c=(c=e.schema,c==null||c===!1?c:c.Id),typeof c===l?c.apply(e):c))+'" class="bv-fieldset-label-wrapper"> <span class="bv-fieldset-label" aria-describedby="'+f((c=(c=e.schema,c==null||c===!1?c:c.Id),typeof c===l?c.apply(e):c))+'_validation"> <span class="bv-fieldset-label-text"> ',p={hash:{prefix:"display_",defaultDisplay:(c=e.schema,c==null||c===!1?c:c.Label)}},o+=f((c=n.extmsg,c?c.call(e,(c=e.schema,c==null||c===!1?c:c.Id),e.msgpack,e,p):a.call(e,"extmsg",(c=e.schema,c==null||c===!1?c:c.Id),e.msgpack,e,p))),h=n["if"].call(e,(c=e.validation,c==null||c===!1?c:c.required),{hash:{},inverse:u.noop,fn:u.program(10,g,t)});if(h||h===0)o+=h;o+=' </span>  <span aria-hidden="false" class="bv-off-screen bv-field-aria-validation"></span> </span> </label> <span class="bv-helper"> <label class="bv-helper-label" role="status" aria-live="polite"> </label> <span class="bv-helper-icon" aria-hidden="true"> <span class="bv-helper-icon-positive">',p={hash:{},inverse:u.noop,fn:u.program(12,y,t)},h=(c=n.renderIcon,c?c.call(e,"bv-helper-icon-positive",p):a.call(e,"renderIcon","bv-helper-icon-positive",p));if(h||h===0)o+=h;o+='</span> <span class="bv-helper-icon-negative">',p={hash:{},inverse:u.noop,fn:u.program(14,b,t)},h=(c=n.renderIcon,c?c.call(e,"bv-helper-icon-negative",p):a.call(e,"renderIcon","bv-helper-icon-negative",p));if(h||h===0)o+=h;o+='</span> </span> </span> <div class="bv-review-field-content-wrapper"> ',p={hash:{}},o+=f((c=n.mountView,c?c.call(e,"socialConnectSmall",s,p):a.call(e,"mountView","socialConnectSmall",s,p)))+" ",h=u.invokePartial(r.submissionInput,"submissionInput",e,n,r);if(h||h===0)o+=h;o+='  <div class="bv-review-media bv-thumbnail-strip">   </div> ',p={hash:{tag:"div",classList:"review-media"},inverse:u.noop,fn:u.programWithDepth(w,t,i)},h=(c=n.conditionalContainer,c?c.call(e,e,p):a.call(e,"conditionalContainer",e,p));if(h||h===0)o+=h;return o+=" </div> </div> </fieldset> ",o}function g(e,t){return"*"}function y(e,t){return" &#x2714; "}function b(e,t){return" &#x2718; "}function w(e,t,r){var i="",s,o,f;i+=" ",f={hash:{tag:"div",classList:"review-media-actions"},inverse:u.noop,fn:u.programWithDepth(E,t,r)},o=(s=n.conditionalContainer,s?s.call(e,e,f):a.call(e,"conditionalContainer",e,f));if(o||o===0)i+=o;return i+=" ",i}function E(e,t,r){var i="",s,o,f;i+=" ",f={hash:{},inverse:u.noop,fn:u.programWithDepth(S,t,e)},o=(s=n.ifInSchema,s?s.call(e,"photourl_1",r,f):a.call(e,"ifInSchema","photourl_1",r,f));if(o||o===0)i+=o;i+=" ",f={hash:{},inverse:u.noop,fn:u.programWithDepth(x,t,e)},o=(s=n.ifInSchema,s?s.call(e,"videourl_1",r,f):a.call(e,"ifInSchema","videourl_1",r,f));if(o||o===0)i+=o;i+=" ",f={hash:{},inverse:u.noop,fn:u.programWithDepth(T,t,e)},o=(s=n.ifInSchema,s?s.call(e,"photourl_1",r,f):a.call(e,"ifInSchema","photourl_1",r,f));if(o||o===0)i+=o;return i+=" ",i}function S(e,t,i){var s="",o,l,c;s+=' <button type="button" class="bv-content-btn bv-btn-add-photo bv-popup-target bv-focusable"> <span aria-hidden="true">',c={hash:{prefix:"add_"}},s+=f((o=n.extmsg,o?o.call(e,"photo",i.msgpack,e,c):a.call(e,"extmsg","photo",i.msgpack,e,c)))+'</span> <span class="bv-review-photo-actions-label bv-off-screen"> ',l=u.invokePartial(r.reviewPhotoCounts,"reviewPhotoCounts",i.rawData,n,r);if(l||l===0)s+=l;return s+=" </span> </button> ",s}function x(e,t,r){var i="",s,o;return i+=' <button type="button" class="bv-content-btn bv-btn-add-video bv-focusable"> ',o={hash:{prefix:"add_"}},i+=f((s=n.extmsg,s?s.call(e,"video",r.msgpack,e,o):a.call(e,"extmsg","video",r.msgpack,e,o)))+" </button> ",i}function T(e,t,i){var s="",o;s+=' <span class="bv-review-photo-actions-label"> ',o=u.invokePartial(r.reviewPhotoCounts,"reviewPhotoCounts",i.rawData,n,r);if(o||o===0)s+=o;return s+=" </span> ",s}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s="",o,u=this,a=n.helperMissing,f=this.escapeExpression,l="function";o=n["with"].call(t,t.formFields,{hash:{},inverse:u.noop,fn:u.programWithDepth(c,i,t)});if(o||o===0)s+=o;return s+=" ",s});return t.registerPartial("submissionFieldsReview",n),n.deps=["submissionField","submissionFieldRadio","submissionInput","reviewPhotoCounts"],n.tplMountedViews=["socialConnectSmall"],n}),BV.define("template/helpers/submissionFields",["vendor/handlebars/runtime","underscore","template/helpers/submissionField"],function(e,t,n){function r(e,r){var i="",s=[].slice.call(arguments),o=s[s.length-1],u=t(o.hash).clone(),a=e.split(" "),f=[];return o.hash.fieldsetClasses=o.hash.fieldsetClasses||"",t(a).forEach(function(e){var t=r[e];t&&(t.hidden?(s[0]=e,i+=n.apply({},s)):f.push(e))}),t(f).forEach(function(e,r){var a=r+1===f.length,l=a&&r%2===0;o.hash=t(u).clone(),o.hash.smallClass&&(o.hash.fieldsetClasses+=" "+o.hash.smallClass+(l?"-alone":"")),o.hash[e+"-inputClasses"]&&(o.hash.inputClasses=o.hash[e+"-inputClasses"]),o.hash[e+"-inputType"]&&(o.hash.inputType=o.hash[e+"-inputType"]),s[0]=e,i+=n.apply({},s)}),i}return e.registerHelper("submissionFields",r),r}),BV.define("hbs!submissionFieldsUserInfo",["hbs","vendor/handlebars/runtime","hbs!submissionField","template/helpers/submissionFields"],function(e,t){var n=t.template(function(e,t,n,r,i){function a(e,t,r){var i="",s,a,l;i+=" ",l={hash:{fieldsetClasses:"bv-nocount",smallClass:"bv-fieldset-small","emailField-inputClasses":"bv-email","emailField-inputType":"email"},inverse:o.noop,fn:o.program(2,f,t)},a=(s=n.submissionFields,s?s.call(e,"usernickname userlocation emailField",e,r,l):u.call(e,"submissionFields","usernickname userlocation emailField",e,r,l));if(a||a===0)i+=a;return i+=" ",i}function f(e,t){var i="",s;i+=" ",s=o.invokePartial(r.submissionField,"submissionField",e,n,r);if(s||s===0)i+=s;return i+=" ",i}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s,o=this,u=n.helperMissing;return s=n["with"].call(t,t.formFields,{hash:{},inverse:o.noop,fn:o.programWithDepth(a,i,t)}),s||s===0?s:""});return t.registerPartial("submissionFieldsUserInfo",n),n.deps=["submissionField"],n.tplMountedViews=[],n}),BV.define("hbs!submissionTagGroup",["hbs","vendor/handlebars/runtime","hbs!submissionHelper","template/helpers/extmsg"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){var r="",i,s;return r+=' <li class="bv-radio-container-li" role="checkbox" aria-labelledby="bv-checkbox-',(i=n.parentSchemaId)?i=i.call(e,{hash:{}}):(i=e.parentSchemaId,i=typeof i===f?i.apply(e):i),r+=l(i)+"-",(i=n.tagIndex)?i=i.call(e,{hash:{}}):(i=e.tagIndex,i=typeof i===f?i.apply(e):i),r+=l(i)+'"> <label class="bv-radio-wrapper-label" for="bv-checkbox-',(i=n.parentSchemaId)?i=i.call(e,{hash:{}}):(i=e.parentSchemaId,i=typeof i===f?i.apply(e):i),r+=l(i)+"-",(i=n.tagIndex)?i=i.call(e,{hash:{}}):(i=e.tagIndex,i=typeof i===f?i.apply(e):i),r+=l(i)+'"> <input id="bv-checkbox-',(i=n.parentSchemaId)?i=i.call(e,{hash:{}}):(i=e.parentSchemaId,i=typeof i===f?i.apply(e):i),r+=l(i)+"-",(i=n.tagIndex)?i=i.call(e,{hash:{}}):(i=e.tagIndex,i=typeof i===f?i.apply(e):i),r+=l(i)+'" name="'+l((i=(i=e.schema,i==null||i===!1?i:i.Id),typeof i===f?i.apply(e):i))+'" class="bv-radio-input bv-focusable ',(s=n.inputClasses)?s=s.call(e,{hash:{}}):(s=e.inputClasses,s=typeof s===f?s.apply(e):s),r+=l(s)+'" type="checkbox" tabindex="0" value="true"/>'+l((i=(i=e.schema,i==null||i===!1?i:i.Label),typeof i===f?i.apply(e):i))+" </label> </li> ",r}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s="",o,u,a,f="function",l=this.escapeExpression,c=n.helperMissing,h=this;s+='<fieldset class="bv-fieldset bv-fieldset-',(o=n.id)?o=o.call(t,{hash:{}}):(o=t.id,o=typeof o===f?o.apply(t):o),s+=l(o)+" bv-",(o=n.type)?o=o.call(t,{hash:{}}):(o=t.type,o=typeof o===f?o.apply(t):o),s+=l(o)+'-field bv-fieldset-tags bv-radio-field bv-nocount"> <legend class="bv-fieldset-label-wrapper"> <span class="bv-fieldset-label"> <span class="bv-fieldset-label-text">',a={hash:{prefix:"display_",defaultDisplay:t.label}},s+=l((o=n.extmsg,o?o.call(t,t.id,t.msgpack,t,a):c.call(t,"extmsg",t.id,t.msgpack,t,a)))+"</span> </span> "+' <span aria-hidden="false" class="bv-off-screen bv-field-aria-validation"></span> </legend> <div class="bv-fieldset-arrowicon"></div> <div class="bv-fieldset-inner"> ',u=h.invokePartial(r.submissionHelper,"submissionHelper",t,n,r);if(u||u===0)s+=u;s+=' <div class="bv-fieldset-radio-wrapper"> <ul class="bv-fieldset-tags-group bv-radio-group"> ',u=n.each.call(t,t.tags,{hash:{},inverse:h.noop,fn:h.program(1,p,i)});if(u||u===0)s+=u;return s+=" </ul> </div> </div> </fieldset> ",s});return t.registerPartial("submissionTagGroup",n),n.deps=["submissionHelper"],n.tplMountedViews=[],n}),BV.define("hbs!submissionFieldCategory",["hbs","vendor/handlebars/runtime","hbs!submissionField","hbs!submissionFieldRadio","hbs!submissionLabel","hbs!submissionInput","hbs!submissionTagGroup","template/helpers/foreach","template/helpers/equals","template/helpers/submissionField"],function(e,t){var n=t.template(function(e,t,n,r,i){function c(e,t){var r="",i,s,o;r+=" ",o={hash:{},inverse:u.noop,fn:u.programWithDepth(h,t,e)},s=(i=n.foreach,i?i.call(e,e.orderedFormFields,o):a.call(e,"foreach",e.orderedFormFields,o));if(s||s===0)r+=s;return r+=" ",r}function h(e,t,r){var i="",s,o,f;i+=" ",f={hash:{},inverse:u.noop,fn:u.programWithDepth(p,t,r)},o=(s=n.equals,s?s.call(e,(s=e.schema,s==null||s===!1?s:s.Type),"HiddenInput",f):a.call(e,"equals",(s=e.schema,s==null||s===!1?s:s.Type),"HiddenInput",f));if(o||o===0)i+=o;i+=" ",f={hash:{},inverse:u.noop,fn:u.programWithDepth(v,t,r)},o=(s=n.equals,s?s.call(e,(s=e.schema,s==null||s===!1?s:s.Type),"SelectInput",f):a.call(e,"equals",(s=e.schema,s==null||s===!1?s:s.Type),"SelectInput",f));if(o||o===0)i+=o;i+=" ",f={hash:{},inverse:u.noop,fn:u.programWithDepth(w,t,r)},o=(s=n.equals,s?s.call(e,(s=e.schema,s==null||s===!1?s:s.Type),"TextInput",f):a.call(e,"equals",(s=e.schema,s==null||s===!1?s:s.Type),"TextInput",f));if(o||o===0)i+=o;i+=" ",f={hash:{},inverse:u.noop,fn:u.programWithDepth(p,t,r)},o=(s=n.equals,s?s.call(e,(s=e.schema,s==null||s===!1?s:s.Type),"TextAreaInput",f):a.call(e,"equals",(s=e.schema,s==null||s===!1?s:s.Type),"TextAreaInput",f));if(o||o===0)i+=o;i+=" ",f={hash:{},inverse:u.noop,fn:u.program(17,x,t)},o=(s=n.equals,s?s.call(e,(s=e.schema,s==null||s===!1?s:s.Type),"TagGroup",f):a.call(e,"equals",(s=e.schema,s==null||s===!1?s:s.Type),"TagGroup",f));if(o||o===0)i+=o;i+=" ",f={hash:{},inverse:u.noop,fn:u.programWithDepth(N,t,r)},o=(s=n.equals,s?s.call(e,(s=e.schema,s==null||s===!1?s:s.Type),"IntegerInput",f):a.call(e,"equals",(s=e.schema,s==null||s===!1?s:s.Type),"IntegerInput",f));if(o||o===0)i+=o;return i+=" ",i}function p(e,t,r){var i="",s,o,f;i+=" ",f={hash:{},inverse:u.noop,fn:u.program(4,d,t)},o=(s=n.submissionField,s?s.call(e,(s=e.schema,s==null||s===!1?s:s.Id),r.formFields,r.rawData,f):a.call(e,"submissionField",(s=e.schema,s==null||s===!1?s:s.Id),r.formFields,r.rawData,f));if(o||o===0)i+=o;return i+=" ",i}function d(e,t){var i="",s;i+=" ",s=u.invokePartial(r.submissionField,"submissionField",e,n,r);if(s||s===0)i+=s;return i+=" ",i}function v(e,t,r){var i="",s,o,f;i+=" ",f={hash:{},inverse:u.programWithDepth(b,t,e,r),fn:u.programWithDepth(m,t,e,r)},o=(s=n.equals,s?s.call(e,(s=e.dimsumField,s==null||s===!1?s:s.type),"SLIDER",f):a.call(e,"equals",(s=e.dimsumField,s==null||s===!1?s:s.type),"SLIDER",f));if(o||o===0)i+=o;return i+=" ",i}function m(e,t,r,i){var s="",o,f,l;s+=" ",l={hash:{fieldsetClasses:"bv-fieldset-slider-rating",inputClasses:"bv-radio-input bv-sliderscore-input"},inverse:u.noop,fn:u.program(8,g,t)},f=(o=n.submissionField,o?o.call(e,(o=r.schema,o==null||o===!1?o:o.Id),i.formFields,i.rawData,l):a.call(e,"submissionField",(o=r.schema,o==null||o===!1?o:o.Id),i.formFields,i.rawData,l));if(f||f===0)s+=f;return s+=" ",s}function g(e,t){var r="",i;r+=" ",i=n["if"].call(e,e.radio,{hash:{},inverse:u.noop,fn:u.program(9,y,t)});if(i||i===0)r+=i;return r+=" ",r}function y(e,t){var i="",s;i+=" ",s=u.invokePartial(r.submissionFieldRadio,"submissionFieldRadio",e,n,r);if(s||s===0)i+=s;return i+=" ",i}function b(e,t,r,i){var s="",o,f,l;s+=" ",l={hash:{},inverse:u.noop,fn:u.program(4,d,t)},f=(o=n.submissionField,o?o.call(e,(o=r.schema,o==null||o===!1?o:o.Id),i.formFields,i.rawData,l):a.call(e,"submissionField",(o=r.schema,o==null||o===!1?o:o.Id),i.formFields,i.rawData,l));if(f||f===0)s+=f;return s+=" ",s}function w(e,t,r){var i="",s,o,f;i+=" ",f={hash:{},inverse:u.noop,fn:u.program(14,E,t)},o=(s=n.submissionField,s?s.call(e,(s=e.schema,s==null||s===!1?s:s.Id),r.formFields,r.rawData,f):a.call(e,"submissionField",(s=e.schema,s==null||s===!1?s:s.Id),r.formFields,r.rawData,f));if(o||o===0)i+=o;return i+=" ",i}function E(e,t){var r="",i,s,o;r+=" ",o={hash:{},inverse:u.program(4,d,t),fn:u.program(15,S,t)},s=(i=n.equals,i?i.call(e,(i=e.schema,i==null||i===!1?i:i.MaxLength),35,o):a.call(e,"equals",(i=e.schema,i==null||i===!1?i:i.MaxLength),35,o));if(s||s===0)r+=s;return r+=" ",r}function S(e,t){var i="",s,o;i+=' <fieldset class="bv-fieldset bv-fieldset-'+l((s=(s=e.schema,s==null||s===!1?s:s.Id),typeof s===f?s.apply(e):s))+" bv-",(o=n.type)?o=o.call(e,{hash:{}}):(o=e.type,o=typeof o===f?o.apply(e):o),i+=l(o)+"-field ",(o=n.fieldsetClasses)?o=o.call(e,{hash:{}}):(o=e.fieldsetClasses,o=typeof o===f?o.apply(e):o),i+=l(o)+' bv-nocount bv-fieldset-small-alone"> <div class="bv-fieldset-arrowicon"></div> <div class="bv-fieldset-inner"> ',o=u.invokePartial(r.submissionLabel,"submissionLabel",e,n,r);if(o||o===0)i+=o;i+=" ",o=u.invokePartial(r.submissionInput,"submissionInput",e,n,r);if(o||o===0)i+=o;return i+=" </div> </fieldset> ",i}function x(e,t){var r="",i;r+=" ",i=n.unless.call(e,e.hidden,{hash:{},inverse:u.noop,fn:u.program(18,T,t)});if(i||i===0)r+=i;return r+=" ",r}function T(e,t){var i="",s;i+=" ",s=u.invokePartial(r.submissionTagGroup,"submissionTagGroup",e,n,r);if(s||s===0)i+=s;return i+=" ",i}function N(e,t,r){var i="",s,o,f;i+=" ",f={hash:{fieldsetClasses:"bv-fieldset-secondary-rating",inputClasses:"bv-rating-input bv-secondary-rating-input",numHelpers:"1"},inverse:u.noop,fn:u.program(8,g,t)},o=(s=n.submissionField,s?s.call(e,(s=e.schema,s==null||s===!1?s:s.Id),r.formFields,r.rawData,f):a.call(e,"submissionField",(s=e.schema,s==null||s===!1?s:s.Id),r.formFields,r.rawData,f));if(o||o===0)i+=o;return i+=" ",i}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s="",o,u=this,a=n.helperMissing,f="function",l=this.escapeExpression;o=n["if"].call(t,t,{hash:{},inverse:u.noop,fn:u.program(1,c,i)});if(o||o===0)s+=o;return s+=" ",s});return t.registerPartial("submissionFieldCategory",n),n.deps=["submissionField","submissionFieldRadio","submissionLabel","submissionInput","submissionTagGroup"],n.tplMountedViews=[],n}),BV.define("hbs!agreementsReviews",["hbs","vendor/handlebars/runtime","template/helpers/renderIcon"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){return" &#x2714; "}function d(e,t){return" &#x2718; "}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f="function",l=this.escapeExpression,c=this,h=n.helperMissing;s+='<fieldset class="bv-fieldset bv-fieldset-agreements bv-fieldset-reviews-'+l((o=(o=t.schema,o==null||o===!1?o:o.Id),typeof o===f?o.apply(t):o))+" bv-",(u=n.type)?u=u.call(t,{hash:{}}):(u=t.type,u=typeof u===f?u.apply(t):u),s+=l(u)+"-field ",(u=n.fieldsetClasses)?u=u.call(t,{hash:{}}):(u=t.fieldsetClasses,u=typeof u===f?u.apply(t):u),s+=l(u)+'"> <div class="bv-fieldset-arrowicon"></div> <div class="bv-fieldset-inner"> <div class="bv-checkbox-container"> <input id="bv-checkbox-reviews-'+l((o=(o=t.schema,o==null||o===!1?o:o.Id),typeof o===f?o.apply(t):o))+'" name="agreements_reviews_'+l((o=(o=t.schema,o==null||o===!1?o:o.Id),typeof o===f?o.apply(t):o))+'" class="bv-checkbox bv-focusable ',(u=n.inputClasses)?u=u.call(t,{hash:{}}):(u=t.inputClasses,u=typeof u===f?u.apply(t):u),s+=l(u)+'" type="checkbox" value="true"/> <label class="bv-fieldset-label-checkbox" for="bv-checkbox-reviews-'+l((o=(o=t.schema,o==null||o===!1?o:o.Id),typeof o===f?o.apply(t):o))+'"> <span class="bv-fieldset-label-text">',(u=n.submissionTitle)?u=u.call(t,{hash:{}}):(u=t.submissionTitle,u=typeof u===f?u.apply(t):u);if(u||u===0)s+=u;s+='</span>  <span aria-hidden="false" class="bv-off-screen bv-field-aria-validation"></span> </label> </div> <span class="bv-helper"> <span class="bv-helper-icon" aria-hidden="true"> <span class="bv-helper-icon-positive">',a={hash:{},inverse:c.noop,fn:c.program(1,p,i)},u=(o=n.renderIcon,o?o.call(t,"bv-helper-icon-positive",a):h.call(t,"renderIcon","bv-helper-icon-positive",a));if(u||u===0)s+=u;s+='</span> <span class="bv-helper-icon-negative">',a={hash:{},inverse:c.noop,fn:c.program(3,d,i)},u=(o=n.renderIcon,o?o.call(t,"bv-helper-icon-negative",a):h.call(t,"renderIcon","bv-helper-icon-negative",a));if(u||u===0)s+=u;return s+="</span> </span> </span> </div> </fieldset>",s});return t.registerPartial("agreementsReviews",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("hbs!agreementsQuestions",["hbs","vendor/handlebars/runtime","template/helpers/renderIcon"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){return" &#x2714; "}function d(e,t){return" &#x2718; "}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f="function",l=this.escapeExpression,c=this,h=n.helperMissing;s+='<fieldset class="bv-fieldset bv-fieldset-agreements bv-fieldset-questions-'+l((o=(o=t.schema,o==null||o===!1?o:o.Id),typeof o===f?o.apply(t):o))+" bv-",(u=n.type)?u=u.call(t,{hash:{}}):(u=t.type,u=typeof u===f?u.apply(t):u),s+=l(u)+"-field ",(u=n.fieldsetClasses)?u=u.call(t,{hash:{}}):(u=t.fieldsetClasses,u=typeof u===f?u.apply(t):u),s+=l(u)+'"> <div class="bv-fieldset-arrowicon"></div> <div class="bv-fieldset-inner"> <div class="bv-checkbox-container"> <input id="bv-checkbox-questions-'+l((o=(o=t.schema,o==null||o===!1?o:o.Id),typeof o===f?o.apply(t):o))+'" name="agreements_questions_'+l((o=(o=t.schema,o==null||o===!1?o:o.Id),typeof o===f?o.apply(t):o))+'" class="bv-checkbox bv-focusable ',(u=n.inputClasses)?u=u.call(t,{hash:{}}):(u=t.inputClasses,u=typeof u===f?u.apply(t):u),s+=l(u)+'" type="checkbox" value="true"/> <label class="bv-fieldset-label-checkbox" for="bv-checkbox-questions-'+l((o=(o=t.schema,o==null||o===!1?o:o.Id),typeof o===f?o.apply(t):o))+'"> <span class="bv-fieldset-label-text">',(u=n.submissionTitle)?u=u.call(t,{hash:{}}):(u=t.submissionTitle,u=typeof u===f?u.apply(t):u);if(u||u===0)s+=u;s+='</span>  <span aria-hidden="false" class="bv-off-screen bv-field-aria-validation"></span> </label> </div> <span class="bv-helper"> <span class="bv-helper-icon" aria-hidden="true"> <span class="bv-helper-icon-positive">',a={hash:{},inverse:c.noop,fn:c.program(1,p,i)},u=(o=n.renderIcon,o?o.call(t,"bv-helper-icon-positive",a):h.call(t,"renderIcon","bv-helper-icon-positive",a));if(u||u===0)s+=u;s+='</span> <span class="bv-helper-icon-negative">',a={hash:{},inverse:c.noop,fn:c.program(3,d,i)},u=(o=n.renderIcon,o?o.call(t,"bv-helper-icon-negative",a):h.call(t,"renderIcon","bv-helper-icon-negative",a));if(u||u===0)s+=u;return s+="</span> </span> </span> </div> </fieldset>",s});return t.registerPartial("agreementsQuestions",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("hbs!submissionBase",["hbs","vendor/handlebars/runtime","hbs!submissionForm","template/helpers/view"],function(e,t){var n=t.template(function(e,t,n,r,i){function c(e,t){var i="",s;i+=" ",s=f.invokePartial(r.submissionForm,"submissionForm",e,n,r);if(s||s===0)i+=s;return i+=" ",i}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,r=r||e.partials;var s="",o,u,a,f=this,l=n.helperMissing;s+=" ",a={hash:{tag:"div",classList:"submission"},inverse:f.noop,fn:f.program(1,c,i)},u=(o=n.view,o?o.call(t,t,a):l.call(t,"view",t,a));if(u||u===0)s+=u;return s+=" ",s});return t.registerPartial("submissionBase",n),n.deps=["submissionForm"],n.tplMountedViews=[],n}),BV.define("hbs!slider",["hbs","vendor/handlebars/runtime"],function(e,t){var n=t.template(function(e,t,n,r,i){return this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers,'<span class="bv-sliderdot" aria-hidden="true">&#9679;</span> <span class="bv-sliderbar" aria-hidden="true"></span> '});return t.registerPartial("slider",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("mf!bv/c2013/messages/janrain",["vendor/messageformat","framework/util/bvtrackerqueue"],function(e,t){return{janrain_error_try_again:function(e){try{return function(e){var t="";return t+="Une erreur s'est produite. Veuillez réessayer plus tard.",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `janrain_error_try_again`: "+n.toString())]),""}}}}),BV.define("bv/util/janrain",["ENV","jquery","underscore","window","bv/api/fetch","util/url","mf!bv/c2013/messages/janrain"],function(e,t,n,r,i,s,o){function d(e){return(s.protocol==="https:"?"https://rpxnow.com":"http://widget-cdn.rpxnow.com")+"/js/lib/"+h+"/"+e}function g(e){c&&h&&(v(),e&&u.done(e))}function y(e){var n=i._config;return t.ajax({url:"https:"+n.baseUrl+"janrain.json",dataType:"jsonp",cache:!0,data:{passkey:n.passkey,token:e,apiversion:n.apiversion,ve:n.virtualEnvironment||"",displaycode:n.displaycode||""}})}function b(e,n){var r=t.Deferred(),i=t.Deferred();return n=n||{},g(function(t){t.events.onShareSendComplete.addHandler(function(e){r.resolve(e)}),t.events.onShareLoginError.addHandler(function(e){i.reject(e),r.reject(e)}),t.events.onShareSendError.addHandler(function(e){r.reject(e)}),n.onLogin&&(i.done(n.onLogin),t.events.onShareLoginToken.addHandler(function(e){i.state()==="pending"&&y(e.token).done(function(e){var t=e.Results[0];e.hasErrors?i.reject(t):i.resolve(t)})})),t.engage.share.loginAndSend(e)}),r}function w(e){var n=t.Deferred();return p[e]?n.resolve(p[e]).promise():(g(function(t){t.events.onProviderLoginToken.addHandler(function(t){n.state()==="pending"&&y(t.token).done(function(t){var r=t.Results[0];t.hasErrors?n.reject(r):(p[e]=r,n.resolve(r))})}),t.events.onProviderLoginError.addHandler(function(e){n.state()==="pending"&&(alert(o.janrain_error_try_again()),n.reject(e))}),t.engage.signin.triggerFlow(e)}),n.promise())}var u=t.Deferred(),a=t.Deferred(),f=t.Deferred(),l=e.get("config"),c,h,p={};t.when(a,f).done(function(){u.resolve(r.janrain)});var v=n.once(function(){typeof r.janrain!="object"&&(r.janrain={}),typeof r.janrain.settings!="object"&&(r.janrain.settings={}),typeof r.janrain.settings.share!="object"&&(r.janrain.settings.share={}),typeof r.janrain.settings.packages!="object"?r.janrain.settings.packages=["share"]:r.janrain.settings.packages.push("share"),r.janrain.settings.share.custom=!0,r.janrain.settings.share.providers=["facebook"],r.janrain.settings.tokenUrl="https://"+h,r.janrain.settings.tokenAction="event",r.janrain.settings.appId=c,r.janrain.settings.appUrl="https://"+h,r.janrain.settings.providers=["facebook"],r.janrain.settings.custom=!0,r.janrain.settings.facebookPermissions=janrain.settings.facebookPermissions||[],r.janrain.settings.facebookPermissions.push("user_photos"),r.janrain.ready=!0,r.janrainWidgetOnload=function(){f.resolve()},r.janrainShareOnload=function(){a.resolve()},t.ajax({url:d("engage.js"),dataType:"script",cache:!0}),t.ajax({url:d("widget.js"),dataType:"script",cache:!0})});try{l.vendorConfig.janrain&&(c=e.get("config").vendorConfig.janrain.key,h=e.get("config").vendorConfig.janrain.domain)}catch(m){}return{withAPI:g,login:w,share:b}}),BV.define("util/isodate",[],function(){function e(e){var t=String(e);return t.length===1&&(t="0"+t),t}return function(t){return t=t||new Date,t.getUTCFullYear()+"-"+e(t.getUTCMonth()+1)+"-"+e(t.getUTCDate())+"T"+e(t.getUTCHours())+":"+e(t.getUTCMinutes())+":"+e(t.getUTCSeconds())+"."+String((t.getUTCMilliseconds()/1e3).toFixed(3)).slice(2,5)+"Z"}}),BV.define("mf!bv/c2013/messages/toolegit",["vendor/messageformat","framework/util/bvtrackerqueue"],function(e,t){return{error_message_required:function(e){try{return function(e){var t="";return t+="Requis",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `error_message_required`: "+n.toString())]),""}},error_message_required_aria:function(e){try{return function(e){var t="";return t+="Erreur : Obligatoire",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `error_message_required_aria`: "+n.toString())]),""}},error_message_required_agreements_aria:function(e){try{return function(e){var t="";return t+="Error: Required. You must agree to Agreements",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `error_message_required_agreements_aria`: "+n.toString())]),""}},error_message_minlength:function(n){try{return function(t){var n="";if(!t)throw new Error("MessageFormat: No data passed to function.");var r="difference",i=t[r],s=0,o={one:function(e){var t="";t+="Il manque ";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.difference,t+=" caractère",t},other:function(e){var t="";t+="Il manque ";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.difference,t+=" caractères",t}};return o[i+""]?n+=o[i+""](t):n+=(o[e.locale.en(i-s)]||o.other)(t),n}(n||{})}catch(r){return t.push(["error",new Error("MF error on `error_message_minlength`: "+r.toString())]),""}},error_message_minlength_aria:function(n){try{return function(t){var n="";if(!t)throw new Error("MessageFormat: No data passed to function.");var r="difference",i=t[r],s=0,o={one:function(e){var t="";t+="Erreur : Il manque ";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.difference,t+=" caractère à la valeur",t},other:function(e){var t="";t+="Erreur : Il manque ";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.difference,t+=" caractères à la valeur",t}};return o[i+""]?n+=o[i+""](t):n+=(o[e.locale.en(i-s)]||o.other)(t),n}(n||{})}catch(r){return t.push(["error",new Error("MF error on `error_message_minlength_aria`: "+r.toString())]),""}},error_message_maxlength:function(n){try{return function(t){var n="";if(!t)throw new Error("MessageFormat: No data passed to function.");var r="difference",i=t[r],s=0,o={one:function(e){var t="";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.difference,t+=" caractère de trop",t},other:function(e){var t="";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.difference,t+=" caractères de trop",t}};return o[i+""]?n+=o[i+""](t):n+=(o[e.locale.en(i-s)]||o.other)(t),n}(n||{})}catch(r){return t.push(["error",new Error("MF error on `error_message_maxlength`: "+r.toString())]),""}},error_message_maxlength_aria:function(n){try{return function(t){var n="";if(!t)throw new Error("MessageFormat: No data passed to function.");var r="difference",i=t[r],s=0,o={one:function(e){var t="";t+="Erreur : La valeur contient ";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.difference,t+=" caractère de trop",t},other:function(e){var t="";t+="Erreur : La valeur contient ";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.difference,t+=" caractères de trop",t}};return o[i+""]?n+=o[i+""](t):n+=(o[e.locale.en(i-s)]||o.other)(t),n}(n||{})}catch(r){return t.push(["error",new Error("MF error on `error_message_maxlength_aria`: "+r.toString())]),""}},error_invalid_email:function(e){try{return function(e){var t="";return t+="Adresse e-mail non valide",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `error_invalid_email`: "+n.toString())]),""}},error_invalid_email_aria:function(e){try{return function(e){var t="";return t+="Erreur : L'adresse e-mail n'est pas valide.",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `error_invalid_email_aria`: "+n.toString())]),""}},error_emoji:function(e){try{return function(e){var t="";return t+="Emojis non autorisés",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `error_emoji`: "+n.toString())]),""}},error_emoji_aria:function(e){try{return function(e){var t="";return t+="Erreur : Ce champ ne peut pas contenir de caractères spéciaux tels que des emojis.",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `error_emoji_aria`: "+n.toString())]),""}},message_characters_used_minlength:function(e){try{return function(e){var t="";if(!e)throw new Error("MessageFormat: No data passed to function.");t+=e.count,t+=" caractères utilisés sur ";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.minlength,t+=" caractères minimum",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `message_characters_used_minlength`: "+n.toString())]),""}},message_characters_used_maxlength:function(e){try{return function(e){var t="";if(!e)throw new Error("MessageFormat: No data passed to function.");t+=e.count,t+=" caractères utilisés sur ";if(!e)throw new Error("MessageFormat: No data passed to function.");return t+=e.maxlength,t+=" caractères possibles",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `message_characters_used_maxlength`: "+n.toString())]),""}},valid_field_input:function(e){try{return function(e){var t="";return t+="Les informations du champ sont valides.",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `valid_field_input`: "+n.toString())]),""}}}}),BV.define("vendor/toolegit",["jquery","underscore","bv/util/focusManager","mf!bv/c2013/messages/toolegit","window","util/pixelsDisplayed"],function(e,t,n,r,i,s){var o={};return function(e,o){function u(t,n){if(!t||!t.length)return;t.prop("novalidate","novalidate"),n=e.extend({},f,n);var r=new l(t,n);return new a(n,r)}function a(n,r){this.reporter=r,this.reporter.validator=this,this.cache={elementRules:{}},this.options=n,this.ruleSelectors=e.extend({},h,n.ruleSelectors),this.rules=e.extend({},c,n.rules);var i=this;this.reporter.$inputs.each(function(t,n){i.rulesForElement(e(n))}),t(this.reporter.getElementNames()).each(function(e,t){this.rulesForName(e).minlength&&this.reporter.setCounter(e)},this)}function l(n,r){this.$form=n,n.prop("novalidate","novalidate"),this.options=r,this.options.ignore=this.options.ignore.join(", "),this.$inputs=n.find("input, button, textarea, select").not(this.options.ignore),this.handlers=e.extend({},d,r.handlers),this.cache={shouldShowErrors:{}};var i={},s=this;this.$inputs.each(function(t,n){var r=e(n).prop("name");if(!r)return;i[r]||(i[r]=[]),i[r].push(n)}),this._namesTable=i,this.names=t(i).keys(),this._setupEvents()}var f={ignore:[".ignore"],onError:function(){},counter:function(t,n){return e.trim(t.val()).length}};e.extend(a.prototype,{rulesForName:function(e){return this.cache.elementRules[e]||{}},rulesForElement:function(e){var n=e.prop("name"),r={};return this.cache.elementRules[n]?this.cache.elementRules[n]:(this.cache.elementRules[n]={},t(this.ruleSelectors).each(function(r,i){e.is(i)&&t(r).each(function(e,t){this.cache.elementRules[n][t]=e},this)},this),r)},count:function(e){var t=e.prop("name"),n=this.rulesForElement(e),r=this.options.counter(e,n.minlength||0);return r<(n.minlength||0)?{error:"minlength",count:r,minlength:n.minlength,fieldName:t}:n.maxlength?r>n.maxlength?{error:"maxlength",count:r,maxlength:n.maxlength,fieldName:t}:{success:!0,count:r,fieldName:t}:{success:!0,count:r,fieldName:t}},check:function(e){return this.reporter.check(e)},addRule:function(e,t){return this.rules[e]=t,this},removeRule:function(n){var r=this,i=e.isArray(n)?n:[n];return t(i).each(function(e,t){r.rules[e]=function(){return!1}}),r},addSelectorRules:function(t,n){return e.extend(this.rulesForElement(t),n),this},removeSelectorRules:function(n,r){var i=this.rulesForElement(n);return t(r).each(function(t,n){delete i[e.isArray(r)?t:n]}),this},validate:function(e,n){var r=this.rulesForElement(e),i=[];return typeof n=="undefined"&&(n=e),t(r).each(function(t,s){if(!this.rules[s])return;var o=this.rules[s](n,t,r);o&&(i.push(o),this.options.onError.call(this,e,s))},this),i},showInvalid:function(e){this.reporter.showInvalid(e)}}),e.extend(l.prototype,{_setupEvents:function(){function r(r){var i=e(r.currentTarget),s=i.prop("name");if(!s)return;if(i.is(n.options.ignore))return;var o=e.trim(i.val());(r.type==="paste"||r.type==="keyup"||r.type==="focusin")&&t.delay(function(){n.count(i)},20),(r.type==="focusout"||r.type==="change"||s in n.cache.shouldShowErrors)&&t.delay(function(){n.check(i)},20)}var n=this;this.$form.on("paste focusin focusout keyup","[type=text], input[type=checkbox][name^=agreements], [type=password], [type=file], [type=email], select, textarea",r),this.$form.on("paste change","[type=radio], [type=checkbox], select, option",r),this.$form.submit(function(e){return n.options.preSubmit.call(n,e),n.valid()?n.options.submit?(n.options.submit.call(n,n.$form[0]),!1):!0:!1})},getElementNames:function(){return this.names},getElementsByName:function(e){return this._namesTable[e]},setCounter:function(n,r){t(this.getElementsByName(n)).each(function(t,n){this.count(e(t))},this)},count:function(e){var t=e.prop("name"),n=this.validator.count(e),r=this.validator.rulesForElement(e),i=this.options.counter(e,r.minlength||0);n.error?n.error==="minlength"?(delete this.cache.shouldShowErrors[t],this.handlers.counterMessage(e,n)):this.handlers.counterError(e,n):this.handlers.counterSuccess(e,n)},check:function(t){t=e(t);var n=this,r=t.prop("name"),i=t;if(!r)return!0;t.is("[type=radio], [type=checkbox]")&&(i=this.$form.find('[name="'+r+'"]').filter(":checked"));var s=this.validator.validate(t,i);return n.cache.shouldShowErrors[r]=!0,s.length>0?(n.cache.shouldShowErrors[r]=!0,this.handlers.error(t,s),!1):(delete n.cache.shouldShowErrors[r],this.handlers.success(t),!0)},valid:function(){var t=this,n=!0,r=[];return t.$inputs.each(function(e,i){var s=t.check(i);s||r.push(i),n=s&&n}),n||this.handlers.showInvalid(e(r)),n},showInvalid:function(t){this.handlers.showInvalid(e(t))}});var c={required:function(t,n){var r=t.prop("name"),i=t.is("[type=radio], [type=checkbox]")?!t.length:!e.trim(t.val());return n&&i?{type:"required",fieldName:r}:!1},minlength:function(t,n,r){var i=t.prop("name"),s=e.trim(t.val()).length;if(!r.required&&s===0)return!1;var o=n-s;return o>0?{type:"minlength",difference:o,minlength:n,fieldName:i}:!1},maxlength:function(t,n){var r=t.prop("name"),i=e.trim(t.val()).length-n;return i>0?{type:"maxlength",difference:i,maxlength:n,fieldName:r}:!1}},h={},p={required:function(e){return{message:r.error_message_required(e),aria:r.error_message_required_aria(e),aria_agreements:r.error_message_required_agreements_aria(e)}},minlength:function(e){return{message:r.error_message_minlength(e),aria:r.error_message_minlength_aria(e)}},maxlength:function(e){return{message:r.error_message_maxlength(e),aria:r.error_message_maxlength_aria(e)}},counter_message:function(e){return{message:e.count+" / "+e.minlength,aria:r.message_characters_used_minlength(e)}},server:function(e){return{message:e.message,aria:e.label+": "+e.message}},counter_success:function(e){return{message:"",aria:r.valid_field_input(e)}},success:function(e){return{message:"",aria:r.valid_field_input(e)}}},d={_getLabel:function(t){var n=t.data("toolegit-label");if(typeof n=="undefined"){var r=t.closest(".bv-fieldset").find(".bv-fieldset-label-text");r.length>0&&(n=e.trim(r.html().replace("*",""))),t.data("toolegit-label",n)}return n},_getContainer:function(e){var t=e.data("toolegit-container");return t||(t=e.closest(".bv-fieldset"),e.data("toolegit-container",t)),t},_getMessageElement:function(e){var t=e.data("toolegit-message");if(!t){var n=this._getContainer(e);t=n.find(".bv-helper-label"),e.data("toolegit-message",t)}return t},_getAriaElement:function(e){var t=e.data("toolegit-aria");if(!t){var n=this._getContainer(e);t=n.find(".bv-field-aria-validation"),e.data("toolegit-aria",t)}return t},_updateMessage:function(e,t,n){var r=this._getMessageElement(e),i=this._getAriaElement(e);t.label=this._getLabel(e)||t.fieldName;var s=t.type&&t.type in p?p[t.type](t):t,o=s.message;if(e.attr("name").indexOf("agreements")>-1)var u=s.aria_agreements||o;else var u=s.aria||o;r.html(o),i.html(u)},_hasNoValue:function(t){var n=t.is(":text, textarea, select")&&e.trim(t.val()).length===0;return n||t.length===0},_setValidity:function(e,t){var n=this._getContainer(e),r=t&&!this._hasNoValue(e);n.toggleClass(this.VALID_CLASS,r),t?e.removeAttr("aria-invalid"):e.attr("aria-invalid",!0)},counterError:function(e,t){var n=this._getContainer(e);n.removeClass(this.NO_COUNT_CLASS).addClass(this.COUNT_CLASS),n.toggleClass(this.LONG_CLASS,t.error==="maxlength"),t.type=t.error,this._updateMessage(e,t,!0),n.addClass(this.ERROR_CLASS)},counterMessage:function(e,t){var n=this._getContainer(e);t.type="counter_message",this._updateMessage(e,t,!1),n.removeClass(this.ERROR_CLASS+" "+this.VALID_CLASS+" "+this.LONG_CLASS+" "+this.NO_COUNT_CLASS),n.addClass(this.COUNT_CLASS)},counterSuccess:function(e,t){t.type="counter_success",this._updateMessage(e,t,!1);var n=this._getContainer(e);n.removeClass(this.ERROR_CLASS+" "+this.COUNT_CLASS+" "+this.LONG_CLASS),n.addClass(this.NO_COUNT_CLASS)},error:function(t,n){e.isArray(n)&&(n=n[0]),this._updateMessage(t,n,!0);var r=this._getContainer(t);this._setValidity(t,!1),r.removeClass(this.NO_COUNT_CLASS),r.addClass(this.ERROR_CLASS)},success:function(e,t){this._updateMessage(e,{type:"success"},!1);var n=this._getContainer(e);this._setValidity(e,!0),n.removeClass(this.ERROR_CLASS)},reset:function(e){var t=this._getContainer(e);this._updateMessage(e,{message:""},!1);var n=[this.ERROR_CLASS,this.VALID_CLASS];t.removeClass(n.join(" "))},showInvalid:function(t){var r=t.first(),o=r;r.is('input[type="radio"]')&&(o=r.parent().closest(".bv-focusable")),i.setTimeout(function(){n.moveFocus(o);var t=s(o.get(0))>0;if(!t){var r=o.closest(".bv-fieldset.bv-error");r.length&&e(i).scrollTop(r.offset().top)}o[0].offsetWidth===0&&o[0].focus()},0)},ERROR_CLASS:"bv-error",VALID_CLASS:"bv-valid",LONG_CLASS:"bv-long",COUNT_CLASS:"bv-mincount",NO_COUNT_CLASS:"bv-nocount"};o.TooLegit=u}(e,o),o.TooLegit}),BV.define("util/emailValidationRule",["mf!bv/c2013/messages/toolegit"],function(e){var t=/^.*@.*$/;return function(n){var r=n.prop("name"),i=n.val();if(i==="")return!1;var s=t.test(i);return s?!1:{fieldName:r,message:e.error_invalid_email(),aria:e.error_invalid_email_aria()}}}),BV.define("util/emojiDetector",[],function(){function e(e){return!!/[\uD800-\uDFFF]/.exec(e)}return e}),BV.define("util/emojiValidationRule",["util/emojiDetector","mf!bv/c2013/messages/toolegit"],function(e,t){return function(n){var r=n.prop("name"),i=n.val(),s=e(i);return s?{fieldName:r,message:t.error_emoji(),aria:t.error_emoji_aria()}:!1}}),BV.define("vendor/jquery/formMemory",["jquery","vendor/json2","window"],function(e,t,n){return function(e,t,r){function i(e){var t=[],n;if(!r.sessionStorage||!r.localStorage)return;n=e==="local"?r.localStorage:r.sessionStorage;for(var i in n)i.indexOf("bvformMemory-")===0&&t.push(i);for(var s=0;s<t.length;s++)delete n[t[s]]}e.fn.formMemory=function(){function h(t){return t?e("<div />").text(t).html().replace('"',"&quote;"):""}function p(n,r){var i=[];n.find(f).each(function(){var t=e(this);if(!s.skipSelector||!t.is(s.skipSelector))if(t.is(u)){if(t.is(l)&&!s.persistPasswords)return;i[i.length]={selector:t[0].nodeName.toLowerCase()+'[name="'+h(t.attr("name"))+'"]',val:t.val()}}else t.is(a)&&t.attr("checked")&&(i[i.length]={selector:t[0].nodeName.toLowerCase()+'[name="'+h(t.attr("name"))+'"][value="'+h(t.val())+'"]',val:"checked"})}),db[r]=t.stringify(i)}var i=e(this),s,o,u='input[type="text"],input[type="password"],input[type="email"],input[type="hidden"],input[type="url"],input[type="tel"],input[type="search"],textarea,select',a='input[type="checkbox"],input[type="radio"]',f=u+","+a,l='input[type="password"]',c=!1;if(typeof r.sessionStorage=="undefined"||typeof r.localStorage=="undefined")return i;if(i.data("bvformMemory-defined")){s=i.data("bvformMemory-config");if(arguments.length>0&&e.isPlainObject(arguments[0]))return s=e.extend(s,arguments[0]),i;if(!(arguments.length>0&&arguments[0]==="remove"))return i;c=!0}else i.data("bvformMemory-defined",!0),s={persistPasswords:!1,skipSelector:null,persistLocal:!1,autoPersist:!0},arguments.length>0&&e.isPlainObject(arguments[0])&&(s=e.extend(s,arguments[0])),i.data("bvformMemory-config",s);return db=s.persistLocal?r.localStorage:r.sessionStorage,i.each(function(){var t=e(this),r="bvformMemory-"+n.location.pathname+"-"+t.prop("id"),i=db[r],o=null,u;if(t[0].nodeName!=="FORM")throw"formMemory - must be called on form elements only";if(c){t.unbind("blur.bvformMemory focus.bvformMemory click.bvformMemory keyup.bvformMemory submit.bvformMemory change.bvformMemory"),delete db[r];return}if(i){i=e.parseJSON(i);for(var f=0;f<i.length;f++){u=t.find(i[f].selector);if(u.length===0){s.noElementCallback&&s.noElementCallback(i[f]);continue}u.each(function(){var t=e(this);if(t.is(a))t.attr("checked",!0);else try{t.val(i[f].val)}catch(n){}s.callback&&s.callback(t[0])})}}t.bind("submit.bvformMemory",function(e){p(t,r)}),s.autoPersist&&t.bind("blur.bvformMemory focus.bvformMemory click.bvformMemory keyup.bvformMemory change.bvformMemory formMemory.bvformMemory",function(){o!==null&&(n.clearTimeout(o),o=null),o=n.setTimeout(function(){p(t,r)},250)})}),i},e.fn.formMemory.removeSession=function(){i("session")},e.fn.formMemory.removeLocal=function(){i("local")},e.fn.formMemory.removeAll=function(){e.fn.formMemory.removeSession(),e.fn.formMemory.removeLocal()}}(e,t,n),e}),BV.define("bv/util/formMemory",["bv/util/user","vendor/jquery/formMemory","window"],function(e,t,n){var r=n.sessionStorage,i="bvformMemory-userchksm";return{remember:function(t,n,s){if(r){var o=r[i],u=e.checksum()+"";o&&u!==o&&this.forget();if(u)try{r[i]=u}catch(a){if(a.code!==22)throw a}}t.formMemory({persistLocal:!1,persistPasswords:!1,autoPersist:!0,skipSelector:".bv-noremember",callback:n,noElementCallback:s.noElementCallback})},forget:function(){t.fn.formMemory.removeAll()}}}),BV.define("bv/c2013/model/upload",["framework/bmodel","bv/util/contentType","bv/strings","underscore","ENV"],function(e,t,n,r,i){return function(s){var o=s.mediaType||"photo",u=s.source||"";return s=r.defaults(s,{mediaType:o,contentType:"review",source:"",name:u+o+"upload",captionFieldName:o+"caption_1",urlFieldName:o+"url_1",hasDropZone:o==="photo",inputType:o==="photo"?"file":"text",locale:i.get("config").locale}),s.postCallback=n.rpc.postCallback,s.contentType=t.noun(s.contentType),new e(s)}}),BV.define("bv/c2013/view/submission/errorMessages",["underscore","framework/util/bvreporter","mf!bv/c2013/messages/submission"],function(e,t,n){return{defaultMessage:function(e){e.Code||t.warn("error should have a Code property to determine the error message");if(e.Code==="ERROR_FORM_REJECTED")return this.ERRMessage(e);var r=n["error_"+e.Code]||n.error_unknown,i=r();return i},ERRMessage:function(e){t.assert(!!e.Code,"error needs a Code property to determine the error message"),t.assert(!!e.SubCodes,"error needs SubCode property to determine the error message");var r=e.SubCodes.split(" "),i,s;return r.length===1?(s=n["error_ERROR_FORM_REJECTED_"+r[0]](),i=n.error_ERROR_FORM_REJECTED_SENTENCE({errorCodeMessage:s})):i=n.error_ERROR_FORM_REJECTED(),i}}}),BV.define("bv/c2013/view/submission/tagHelpers",[],function(){return{parse:function(e){var t=/tagid_(.*)\/(.*)$/,n=t.exec(e),r=3;if(n.length===r)return{id:n[1],childId:n[2]}},updateElEnabled:function(e,t){var n=e.closest(".bv-radio-container-li");e.prop("checked",t),n.toggleClass("bv-radio-container-li-active",t),n.attr("aria-checked",t)}}}),BV.define("hbs!submissionStar",["hbs","vendor/handlebars/runtime","template/helpers/renderIcon"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){var r="",i;return r+='id="',(i=n.id)?i=i.call(e,{hash:{}}):(i=e.id,i=typeof i===f?i.apply(e):i),r+=l(i)+'"',r}function d(e,t){var r;return(r=n.value)?r=r.call(e,{hash:{}}):(r=e.value,r=typeof r===f?r.apply(e):r),r||r===0?r:""}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f="function",l=this.escapeExpression,c=this,h=n.helperMissing;s+='<span class="bv-submission-star-rating bv-submission-rater-',(o=n.serial)?o=o.call(t,{hash:{}}):(o=t.serial,o=typeof o===f?o.apply(t):o),s+=l(o)+'"> <a class="bv-rating-link bv-focusable" ',o=n["if"].call(t,t.id,{hash:{},inverse:c.noop,fn:c.program(1,p,i)});if(o||o===0)s+=o;s+=' role="radio" href="javascript:void(0)" title="',(o=n.title)?o=o.call(t,{hash:{}}):(o=t.title,o=typeof o===f?o.apply(t):o);if(o||o===0)s+=o;s+='" aria-checked="false"> ',a={hash:{ariaHidden:"true"},inverse:c.noop,fn:c.program(3,d,i)},u=(o=n.renderIcon,o?o.call(t,"bv-sprite-submission-star",a):h.call(t,"renderIcon","bv-sprite-submission-star",a));if(u||u===0)s+=u;s+=' <span class="bv-off-screen">',(u=n.title)?u=u.call(t,{hash:{}}):(u=t.title,u=typeof u===f?u.apply(t):u);if(u||u===0)s+=u;return s+="</span> </a> </span> ",s});return t.registerPartial("submissionStar",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("vendor/jquery/rating",["jquery","hbs!submissionStar"],function(e,t){return e&&function(e){e.fn.rating=function(n){if(this.length===0)return this;if(typeof arguments[0]=="string"){if(this.length>1){var r=arguments;return this.each(function(){e.fn.rating.apply(e(this),r)})}return e.fn.rating[arguments[0]].apply(this,e.makeArray(arguments).slice(1)||[]),this}var n=e.extend({},e.fn.rating.options,n||{});return e.fn.rating.calls++,this.not(".bv-submission-star-rating-applied").addClass("bv-submission-star-rating-applied").each(function(){var r,i=e(this),s=(this.name||"unnamed-rating").replace(/\[|\]/g,"_").replace(/^\_+|\_+$/g,""),o=e(this.form||document.body),u=o.data("rating");if(!u||u.call!==e.fn.rating.calls)u={count:0,call:e.fn.rating.calls};var a=u[s],f;a&&(r=a.data("rating"),f=a.data("group"));if(a&&r)r.count++;else{r=e.extend({},n||{},(e.metadata?i.metadata():e.meta?i.data():null)||{},{count:0,stars:[],inputs:[]}),r.serial=u.count++,a=e('<span class="bv-submission-star-rating-control"></span>'),n.withHeatMap||a.addClass("bv-heat-map-off"),i.parents("ul").before(a),f=a.closest("[role=radiogroup]"),f.length&&a.data("group",f),a.addClass("bv-submission-rating-to-be-drawn");if(i.attr("disabled")||i.hasClass("bv-submission-disabled"))r.readOnly=!0;i.hasClass("bv-submission-required")&&(r.required=!0)}var l="&#x2605;",c={serial:r.serial,title:this.title||this.value,value:l};this.id&&(c.id=this.id,i.removeAttr("id"));var h=e(t(c));this.className&&h.addClass(this.className),i.attr("role","presentation"),i.attr("aria-hidden","true"),a.append(h),r.half&&(r.split=2);if(typeof r.split=="number"&&r.split>0){var p=(e.fn.width?h.width():0)||r.starWidth,d=r.count%r.split,v=Math.floor(p/r.split);h.width(v).find("a").css({"margin-left":"-"+d*v+"px"})}r.readOnly?h.addClass("bv-submission-star-rating-readonly"):h.addClass("bv-submission-star-rating-live").mouseover(function(){var t=e(this);t.rating("fill"),t.rating("focus"),t.addClass("bv-focused")}).mouseout(function(){var t=e(this);t.rating("draw"),t.rating("blur"),t.removeClass("bv-focused")}).on("click touchstart",function(t){t.preventDefault(),t.stopPropagation();var n=e(this);n.closest("[role=radiogroup]").find("[role=radio]").attr("aria-checked","false"),n.find("[role=radio]").attr("aria-checked","true"),n.rating("select")}).focusout(function(){var t=e(r.current?r.current.data("rating.input"):null);r.focusout.call(t[0])}),h.find(".bv-rating-link").click(function(e){h.click()}),this.checked&&(r.current=h,h.find("[role=radio]").attr("aria-checked","true")),this.nodeName==="A"&&i.hasClass("bv-submission-selected")&&(r.current=h),i.hide(),i.addClass("bv-hidden"),i.change(function(){e(this).rating("select")}),h.data("rating.input",i.data("rating.star",h)),r.stars[r.stars.length]=h[0],r.inputs[r.inputs.length]=i[0],r.rater=u[s]=a,r.context=o,i.data("rating",r),a.data("rating",r),h.data("rating",r),o.data("rating",u)}),e(".bv-submission-rating-to-be-drawn").rating("draw").removeClass("bv-submission-rating-to-be-drawn"),this},e.extend(e.fn.rating,{calls:0,focus:function(){var t=this.data("rating");if(!t)return this;if(!t.focus)return this;var n=e(this).data("rating.input")||e(this.tagName=="INPUT"?this:null);t.focus&&t.focus.apply(n[0],[n.val(),e("a",n.data("rating.star"))[0]])},blur:function(){var t=this.data("rating");if(!t)return this;if(!t.blur)return this;var n=e(this).data("rating.input")||e(this.tagName=="INPUT"?this:null);t.blur&&t.blur.apply(n[0],[n.val(),e("a",n.data("rating.star"))[0]])},fill:function(){var e=this.data("rating");if(!e)return this;if(e.readOnly)return;this.rating("drain"),this.prevAll().addBack().filter(".bv-submission-rater-"+e.serial).addClass("bv-submission-star-rating-hover")},drain:function(){var e=this.data("rating");if(!e)return this;if(e.readOnly)return;e.rater.children().filter(".bv-submission-rater-"+e.serial).removeClass("bv-submission-star-rating-on").removeClass("bv-submission-star-rating-hover")},draw:function(){var t=this.data("rating");if(!t)return this;this.rating("drain"),t.current?(e(t.current).find("a").attr("aria-checked",!0),t.current.data("rating.input").prop("checked",!0),t.current.prevAll().addBack().filter(".bv-submission-rater-"+t.serial).addClass("bv-submission-star-rating-on")):e(t.inputs).prop("checked",!1),this.siblings()[t.readOnly?"addClass":"removeClass"]("bv-submission-star-rating-readonly")},select:function(t,n){var r=this.data("rating");if(!r)return this;if(r.readOnly)return;r.current=null;if(typeof t!="undefined"){if(typeof t=="number")return e(r.stars[t]).rating("select",undefined,n);typeof t=="string"&&e.each(r.stars,function(){e(this).data("rating.input").val()==t&&e(this).rating("select",undefined,n)})}else r.current=this[0].tagName=="INPUT"?this.data("rating.star"):this.is(".bv-submission-rater-"+r.serial)?this:null;this.data("rating",r),this.rating("draw");var i=e(r.current?r.current.data("rating.input"):null);(n||n==undefined)&&r.callback&&r.callback.apply(i[0],[i.val(),e("a",r.current)[0]])},readOnly:function(t,n){var r=this.data("rating");if(!r)return this;r.readOnly=t||t==undefined?!0:!1,n?e(r.inputs).attr("disabled","disabled"):e(r.inputs).removeAttr("disabled"),this.data("rating",r),this.rating("draw")},disable:function(){this.rating("readOnly",!0,!0)},enable:function(){this.rating("readOnly",!1,!1)}}),e.fn.rating.options={cancel:"Cancel Rating",cancelValue:"",split:0,starWidth:16},e(function(){e("input[type=radio].bv-submission-star").rating()})}(e),e}),BV.define("vendor/jquery/placeholder",["jquery"],function(e){return function(e,t,n,r,i,s,o){var u=function(){var e=n(this);return e.find("["+i+"],"+"["+o+"]")},a=function(){var e=n(this);return e.attr(i)||e.attr(o)},f=function(){var e=n(this);if(typeof this.selectionStart=="number")this.selectionStart=0,this.selectionEnd=0;else if(typeof this.createTextRange!="undefined"){var t=this.createTextRange();t.collapse(!0),t.moveStart("character",0),t.moveEnd("character",0),t.select()}},l=function(){var e=n(this);if(e.hasClass(r))return;e[s]()||(e.addClass(r),e.attr("type")==="password"&&(e.attr("type","text"),e.data(r+"-pwd",!0)),e[s](a.call(this)),e.on("mouseup",f).on("selectstart",!1),e.is(":focus")&&f.call(this))},c=function(){var e=n(this);e.removeClass(r),e.data(r+"-pwd")&&e.attr("type","password"),e[s]()===a.call(this)&&e[s](""),e.off("mouseup",f).off("selectstart",!1)};n.fn.clearPlaceholdersInForm=function(){u.call(this).each(function(){n(this).data(r)&&c.call(this)})},n.fn.placeholdr=function(e){e||(e={});var s=e.overrideNative||!1;if(i in t.createElement("input")&&!s){e.isSafari&&n(this).find("textarea").each(function(){var e=n(this);e.on("input",function(t){e[0].value===""&&e.blur().focus()})});return}n(this).find("["+i+"]").each(function(){var t=n(this);if(t.data(r))return;t.data(r,!0),s&&(t.attr(o,t.attr(i)),t.attr(i,"")),l.call(this);var u=function(e){var t=this;setTimeout(function(){l.call(t,e)},25)},a=c;e&&e.preserveOnFocus?t.on("keypress keydown paste",a).on("blur change mouseup keyup",u):(t.focus(a),t.blur(u))})},n.fn.getPlaceholderText=function(){return a.call(this)},n.fn[s]=n.fn.val,n.fn.val=function(e){var t=n(this);return n.type(e)==="undefined"&&t.data(r)&&t[s]()===a.call(this)?"":(n.type(e)==="string"&&c.call(this),n.fn[s].apply(this,arguments))}}(window,document,e,"placeholdr","placeholder","placeholdrVal","placeholdr","clearPlaceholdersInForm"),e}),BV.define("bv/util/jquery/radiosilence",["ENV","underscore","jquery","util/specialKeys"],function(e,t,n,r){var i=function(e,r){var i=this.$element=n(e),s;this.options=t.extend({},n.fn.bvradiosilence.defaults,r),this.options.wrapper="<"+this.options.wrapperType+"></"+this.options.wrapperType+">",this.$inputs=i.find("input").before(this.options.wrapper),this.$wrappers=i.find(this.options.wrapperType),this.$containers=i.find("."+this.options.containerClass),this.options.value&&this.$inputs.filter('[value="'+this.options.value+'"]').prop("checked",!0),this.options.inputClass&&this.$inputs.addClass(this.options.inputClass),this.options.wrapperClass&&this.$wrappers.addClass(this.options.wrapperClass),s=this,this.$wrappers.each(function(e,r){var i,o=n(this),u=o.next("input"),a=u.attr("id"),f;o.attr("for",a),o.attr("id",a+"-label"),s.options.displayAttr?i=u.attr(s.options.displayAttr):i=u.val(),s.options.labelInnerHtml?o.html(s.options.labelInnerHtml||i):o.text(i),f=u.attr("aria-label"),f||u.attr("aria-label",i),o.hasClass("bv-focusable")&&(o.attr("aria-label",i),u.removeClass("bv-focusable"),u.attr("role","presentation")),t(s.options.wrapperAttrs).isObject()&&o.attr(s.options.wrapperAttrs),t(s.options.inputAttrs).isObject()&&u.attr(s.options.inputAttrs)}),this.inputFocusHandler=this.options.inputFocusHandler||this.inputFocusHandler,this.inputKeydownHandler=this.options.inputKeydownHandler||this.inputKeydownHandler,this.inputClickHandler=this.options.inputClickHandler||this.inputClickHandler,this.wrapperClickHandler=this.options.wrapperClickHandler||this.wrapperClickHandler,this.blur=this.options.blur||n.noop,this.render=this.options.render||this.render,this.render(),this.listen()};return i.prototype={constructor:i,render:function(e){var t=e||this.$element.find("input:checked");this.$containers.removeClass(this.options.activeClass),t.closest("."+this.options.containerClass).addClass(this.options.activeClass),this.$wrappers.removeClass(this.options.focusedClass),t.prev(this.options.wrapperType).addClass(this.options.focusedClass),this.$element.trigger("rendered")},listen:function(){this.$inputs.off("keydown",this.inputKeydownHandler).on("keydown",this,this.inputKeydownHandler),this.$inputs.off("click",this.inputClickHandler).on("click",this,this.inputClickHandler).on("focus",this,this.inputFocusHandler).on("focus",this.addFocusClass).on("blur",this.removeFocusClass).on("blur",this,this.invokeBlurCallback),this.$wrappers.off("click",this.wrapperClickHandler).on("click",this,this.wrapperClickHandler)},wrapperClickHandler:function(){n(this).next("input").click().focus()},addFocusClass:function(e){n(this).prev("label").addClass("bv-radio-wrapper-label-focused")},invokeBlurCallback:function(e){var t=e.data;t.blur.call(t.$element)},removeFocusClass:function(e){n(this).prev("label").removeClass("bv-radio-wrapper-label-focused")},inputClickHandler:function(e){var t=e.data;t.render(n(this))},inputFocusHandler:n.noop,inputKeydownHandler:function(t){if(e.get("keyboardMode")&&t.keyCode===r.ENTER)return n(this).click(),!1}},n.fn.bvradiosilence=function(e){return this.each(function(){var r=n(this),s=r.data("bvradiosilence");e=e||{},s||r.data("bvradiosilence",s=new i(this,e)),t.isString(e)&&s[e]()})},n.fn.bvradiosilence.defaults={wrapperType:"label",wrapperClass:"bv-radio-wrapper-label",focusedClass:"bv-radio-wrapper-label-focused",containerClass:"bv-radio-container-li",activeClass:"bv-radio-container-li-active",inputClass:"bv-radio-input"},n}),BV.define("bv/util/jquery/nps",["ENV","underscore","bv/util/focusManager","util/specialKeys","bv/util/jquery/radiosilence"],function(e,t,n,r,i){var s=function(e,t){this.$element=i(e),this.options=i.extend({},i.fn.nps.defaults,t),this.options.$npsComment=this.options.$npsComment||this.$element.closest(".bv-fieldset").find(".bv-netpromotercomment-wrapper"),this.options.wrapperClickHandler=this.wrapperClickHandler,this.options.inputFocusHandler=this.inputFocusHandler,this.options.inputKeydownHandler=this.inputKeydownHandler,this.options.renderComments=this.renderComments,this.$element.bvradiosilence(this.options),this.renderComments(this)};return s.prototype={constructor:s,renderComments:function(e,r){var i=e.$element.find("input:checked");e.options.$npsComment.toggleClass("bv-hidden",!e.options.expanded&&t.isUndefined(i.val()));if(r){var s=e.options.$npsComment.find("textarea");n.moveFocus(s)}},wrapperClickHandler:function(e){var t=e.data;t.$element.off("rendered").on("rendered",function(){i(this).next("input").click(),t.options.renderComments(t,!0)})},inputFocusHandler:function(e){var t=e.data;t.$element.off("rendered").on("rendered",function(){t.options.renderComments(t)})},inputKeydownHandler:function(t){var n=t.data;if(e.get("keyboardMode")&&t.keyCode===r.ENTER)return i(this).click(),n.options.renderComments(n,!0),!1}},i.fn.nps=function(e){return this.each(function(){var n=i(this),r=n.data("bvnps");e=e||{},r||n.data("bvnps",r=new s(this,e)),t.isString(e)&&r[e]()})},i}),BV.define("vendor/jquery/autosize",["jquery","body"],function(e,t){var n={className:"autosizejs",append:"",callback:!1},r="hidden",i="border-box",s="lineHeight",o='<textarea tabindex="-1" aria-hidden="true" style="position:absolute; top:-9999px; left:-9999px; right:auto; bottom:auto; -moz-box-sizing:content-box; -webkit-box-sizing:content-box; box-sizing:content-box; word-wrap:break-word; height:0 !important; min-height:0 !important; overflow:hidden;"/>',u=["fontFamily","fontSize","fontWeight","fontStyle","letterSpacing","textTransform","wordSpacing","textIndent"],a="oninput",f="onpropertychange",l=e(o)[0];return l.setAttribute(a,"return"),e.isFunction(l[a])||f in l?(e(l).css(s,"99px"),e(l).css(s)==="99px"&&u.push(s),e.fn.autosize=function(s){return s=e.extend({},n,s||{}),this.each(function(){function b(){var e,t,i;d||(d=!0,c.value=n.value+s.append,c.style.overflowY=n.style.overflowY,i=parseInt(n.style.height,10),c.style.width=l.css("width"),c.scrollTop=0,c.scrollTop=9e4,e=c.scrollTop,t=r,e>p?(e=p,t="scroll"):e<h&&(e=h),e+=m,l.css("overflowY",t),i!==e&&(l.css("height",e+"px"),y&&s.callback.call(n)),setTimeout(function(){d=!1},1))}var n=this,l=e(n),c,h=l.height(),p=parseInt(l.css("maxHeight"),10),d,v=u.length,m=0,g=n.value,y=e.isFunction(s.callback);if(l.css("box-sizing")===i||l.css("-moz-box-sizing")===i||l.css("-webkit-box-sizing")===i)m=l.outerHeight()-l.height();if(l.data("mirror")||l.data("ismirror"))return;c=e(o).data("ismirror",!0).addClass(s.className)[0],l.data("mirror",e(c)).css({overflow:r,overflowY:r,wordWrap:"break-word"}),p=p&&p>0?p:9e4;while(v--)c.style[u[v]]=l.css(u[v]);e(t()).append(c),f in n?a in n?n[a]=n.onkeyup=b:n[f]=b:(n[a]=b,n.value="",n.value=g),e(window).resize(b),l.bind("autosize",b),b()})}):e.fn.autosize=function(e){return this},e}),BV.define("bv/c2013/view/submission",["ENV","window","framework/bview","bv/ui-core/focusableview","bv/util/focusManager","framework/bmodel","framework/clientEvents","hbs!submissionBase","hbs!reviewPhotoCounts","hbs!slider","bv/util/submissionSubviews","underscore","util/specialKeys","bv/util/user","bv/util/contentType","bv/util/janrain","bv/util/withRPC","bv/util/session","bv/util/productInfo","bv/strings","util/isodate","bv/ui-core/modestbox","vendor/toolegit","util/emailValidationRule","util/emojiValidationRule","bv/util/formMemory","bv/util/loadingOverlay","bv/util/ariaStatus","mf!bv/c2013/messages/submission","mf!bv/c2013/messages/common","framework/util/bvreporter","framework/util/bvtracker","framework/util/ie","jquery","bv/c2013/model/upload","bv/c2013/view/submission/errorMessages","framework/video/youtubeVideo","bv/c2013/view/submission/tagHelpers","vendor/jquery/rating","vendor/jquery/placeholder","bv/util/jquery/nps","vendor/jquery/autosize"],function(t,n,r,i,s,o,u,a,f,l,c,h,p,d,v,m,g,y,b,w,E,S,x,T,N,C,k,L,A,O,M,_,D,P,H,B,j,F){function U(e){return P(e).closest(".bv-fieldset")}var I=t.get("config"),q=r.extend(i),R="-server";return q.extend({name:"submission",rpcReady:P.Deferred(),events:{"click .bv-submit":"onSubmit","click .bv-cancel":"cancelForm","click .bv-btn-add-video":"videoUpload","click .bv-btn-add-photo":"photoUploadChoice","click .bv-fieldset-agreements a":"goToAgreements","click .bv-fieldset-netpromoterscore .bv-radio-container-li input":"clickEventNP","focus .bv-fieldsets input, .bv-fieldsets textarea, .bv-fieldsets select":"focusEvent","focus .bv-fieldsets .bv-radio-input, .bv-fieldsets .bv-content-btn":"focusEvent","change .bv-fieldset-tags .bv-radio-container-li input":"tagChange","focus .bv-fieldset-tags .bv-radio-container-li":"focusTag","blur .bv-fieldset-tags .bv-radio-container-li":"blurTag","keydown .bv-fieldset-tags .bv-radio-container-li":"keydownTag","keydown .bv-thumbnail-close":"keydownRemoveButton","change .bv-fieldset select":"selectChange","keypress input[maxlength].bv-text":"checkMaxlength"},template:a,msgpacks:[A,O],init:function(t){var n,r=[],i=[],s=I.submission.rating.range,o=h.last(s),a=h.first(s);this.inline=t.inline,this.isGenericSubmission=t.isGenericSubmission,this.closeAllPostSubmission=t.closeAllPostSubmission,this.ModestBoxId=t.inline?this.model.get("componentId"):"lightbox",this.reviewPhotoCountsTemplate=f,this.thumbnailViews={},this.on("viewready",this.setupUIElements,this),this.on("addmedia",this.addMedia,this),this.on("removemedia",this.removeMedia,this),this.on("showupload",this.showUploadView,this),this.on("updateupload",this.updateButtons,this),this.on("updateupload",this.setRemainingPhotoCount,this),I.vendorConfig.janrain&&m.withAPI(),a=a===o?0:a;while(a<=o)n=a++,r.push("bv-fieldset-r"+n),i.push("bv-fieldset-h"+n);this.ratingClasses=r.join(" "),this.ratingHoverClasses=i.join(" "),this.once("viewshown",function(){var t=this.getForm();!t.closest(".bv-mbox-search").length&&!this.model.get("formErrors")&&this.moveInitialFocus(),this._setupERRErrors()}),this.on("viewshown",function(){var t,n;this.hasFeature("thankYouPage")&&this.getFeatureView("thankYouPage").hasFeature("localSocialSharing")&&(t=this.getFeatureView("thankYouPage"),n=t.getFeatureView("localSocialSharing"),n.model.trigger("submissionLoad",this.model.get("clientAPIConfig"),{contentType:this.model.get("contentType")})),u.trigger("submissionLoad",this.model.get("clientAPIConfig"),{contentType:this.model.get("contentType")})}),this.on("viewclosed",function(){u.trigger("submissionClose",this.model.get("clientAPIConfig"),{contentType:this.model.get("contentType")})}),this.on("viewready",this._registerFocusables,this)},_registerFocusables:function(){this.$(".bv-focusable").each(function(t,n){n.tabIndex=0})},render:function(){this.model.get("pageview")&&this.triggerPageView(),r.prototype.render.apply(this,arguments)},attach:function(t){var n;r.prototype.attach.call(this,t),n=this.$viewEl.find(".bv-fieldset-radio-wrapper, .bv-fieldset-reviewtext"),n.length>0&&(n.data("receiveFocusManagerEvents",!0),n.on("focusManagerFocus",function(t){if(t.keyEvent.keyCode!==p.TAB)return;s[t.keyEvent.shiftKey?"focusPrev":"focusNext"]()})),_.feature({type:"Shown",name:"Submission",bvProduct:b.getType(this),source:"firebird",notificationId:this.model.get("bvNotificationId"),contentId:this.model.get("bvQuestionId"),productId:b.getId(this),clientName:I.clientname.toLowerCase(),deploymentZone:I.siteId,dc:I.displaycode,messageType:this.model.get("bvMessageType"),recipientDomain:this.model.get("bvRecipientDomain"),userLocale:I.locale,campaignId:this.model.get("campaignId")})},ieCompat:function(){var t=D();t&&(this.$el.find(".bv-compat").addClass("bvie bvie"+t),t<8&&this.$el.find(".bv-compat").addClass("bvie-lt8"))},photoUploadChoice:function(t){var n=this;if(!this.hasFeature("addSocialPhoto"))return this.photoUpload(t);this.photouploadPopup||(this.photouploadPopup=new c.PhotoUploadPopupView({parent:{$targetEl:n.$(".bv-popup-target"),options:{positionPopupRelativeToLink:!0},photoUpload:P.proxy(n.photoUpload,n),facebookPhotoUpload:P.proxy(n.facebookPhotoUpload,n)},aboveModestBox:!0,model:new o(n.model.attributes)}),S.get(this.ModestBoxId).once("beforeClose",function(){n.photouploadPopup.rendered&&n.photouploadPopup.detach(),delete n.photouploadPopup})),this.photouploadPopup.rendered?this.photouploadPopup.detach():this.photouploadPopup.render();return},photoUpload:function(t){var n=this.model.get("photos"),r=this.$viewEl.find(".bv-btn-add-photo");if(n&&n.length>=this.maxMedia.photo)return;this.showUploadView("photo",null,null,null,r)},facebookPhotoUpload:function(t){var n=this.model.get("photos"),r=this.$viewEl.find(".bv-btn-add-photo"),i=this;if(n&&n.length>=this.maxMedia.photo)return;m.withAPI(function(){k.show(),m.login("facebook").done(function(t){var n=t&&t.accessCredentials&&t.accessCredentials.accessToken;n&&i.showUploadView("photo",undefined,"facebook",{token:n},r)}).fail(k.hide)})},videoUpload:function(t){var n=P(t.target);this.showUploadView("video",null,null,null,n)},showUploadView:function(t,n,r,i,o){var u=r||"",a=this.model.toJSON(),f={photo:c.PhotoUploadView,video:c.VideoUploadView,facebookphoto:c.FacebookPhotoUploadView},l=H({source:u,mediaType:t,contentType:a.contentType,msgpack:A,parent:this.model,formFields:a.formFields,clientName:a.clientName,componentId:this.model.get("componentId"),apiConfig:a.apiConfig}),p=h.extend({parent:this,contentType:v.noun(a.contentType),media:n,msgpack:A,mediaType:t,componentId:this.model.get("componentId"),model:l},i),d=new f[u+t](p),m=d.getFeatureView("dropdownable"),g,y=S.get(this.ModestBoxId),b=y.$viewEl.find(".bv-mbox-wrapper");y.push({title:A["add_"+u+t](),view:d,beforeShow:function(){d.setupView()},afterShow:function(){g=L.add(A["add_"+t+"_dialog"]()),m&&h.delay(function(){m.popIn()},300),b.addClass("bv-mbox-media-upload")},beforeHide:function(){g.remove(),m&&m.popOut(),b.removeClass("bv-mbox-media-upload")},afterDetach:function(){s.rescanFocusLayer(),s.moveFocus(o||P(document.activeElement))}})},addMedia:function(t,n){var r,i;this._addMedia(t,n),n.url&&y.set(escape(n.url),n,P.noop),S.get(this.ModestBoxId).pop(),r=D(),r&&r<9&&this.$form.find("textarea").resize(),i=L.add(A["submission_"+t+"_added"]()),setTimeout(function(){i.remove()},1e3),this.$form.trigger("formMemory")},_addMedia:function(t,n){this.model.addMedia(t,n),this._updateThumbnails(t)},removeMedia:function(t,n){this.model.removeMedia(t,n),this._updateThumbnails(n)},_updateThumbnails:function(t){var n=this.thumbnailViews[t];n||(n=new c.ThumbnailView({parent:this,mediaType:t,componentId:this.model.get("componentId")}),this.thumbnailViews[t]=n),n.render()},updateButtons:function(t,n){var r=this.$el.find(".bv-btn-add-"+t);n<=0?r.attr("disabled",!0):r.removeAttr("disabled")},setRemainingPhotoCount:function(t,n){var r,i,s;if(t==="video")return;r={maxPhotos:this.model.getMaxNumByMediaType(t)},h.isNumber(n)&&n!==this.maxMedia.photo&&(r.remainingPhotos=String(n)),i=this.$el.find(".bv-review-photo-actions-label"),this.maxMedia.photo===1?s="":s=this.reviewPhotoCountsTemplate(r),i.html(s)},cancelForm:function(t){S.get(this.ModestBoxId).close()},scrollForm:function(t){this.trigger("scroll")},focusEvent:function(t){var n=P(t.currentTarget);this.updateActiveElementCSS(n)},clickEventNP:function(t){var n=P(t.currentTarget);n&&n.attr("name")&&n.attr("name")==="netpromoterscore"&&n.prop("checked")&&this.validator.check(n)},updateActiveElementCSS:function(t){var n,r,i;this.fieldsets&&(n=this.fieldsets.filter(".bv-fieldset-active"),r=t.closest(".bv-fieldset"),this.doneFocusing&&n.is(".bv-radio-field")&&!n.is(r)&&this.validator.check(n.find("input:first")),this.fieldsets.removeClass("bv-fieldset-active").removeClass("bv-hidden"),r.is(".bv-fieldset-netpromotercomment")&&(i=this.fieldsets.filter(".bv-fieldset-netpromoterscore"),i.addClass("bv-fieldset-active")),r.addClass("bv-fieldset-active"))},focusTag:function(t){var n=P(t.currentTarget);n.find(".bv-radio-wrapper-label").addClass("bv-radio-wrapper-label-focused")},blurTag:function(t){var n=P(t.currentTarget);n.find(".bv-radio-wrapper-label").removeClass("bv-radio-wrapper-label-focused")},keydownTag:function(t){t.keyCode===p.ENTER&&(t.preventDefault(),P(t.currentTarget).find("input").click())},keydownRemoveButton:function(t){var n;if(t.keyCode===p.ENTER||t.keyCode===p.SPACE)t.preventDefault(),n=P(t.currentTarget),h.defer(function(){n.click()})},checkMaxlength:function(t){function u(){if(!n._$ariaAlert)return;n._$ariaAlert.remove(),n._$ariaAlert=null}var n=this,r,i,s=P(t.currentTarget),o=Number(s.attr("maxlength"));if(o===0)return;r=s.val(),r.length>=o?(i=A.max_length_reached_message({maxlength:o}),u(),this._$ariaAlert=this.showAriaAlert(i)):u()},tagChange:function(t){var n=P(t.currentTarget),r=n.closest(".bv-radio-container-li"),i=n.is(":checked");r.toggleClass("bv-radio-container-li-active",i),r.attr("aria-checked",i)},selectChange:function(t){var n=P(t.currentTarget),r;n.is('[name="contextdatavalue_Age"]')&&(r=n.val()==="17orUnder",r?_.optOut():this.optedOut&&_.optIn(),this.optedOut=r)},onSubmit:function(t){var n,r,i,s=this,o=s.getAgreementsType(),u=this.model.attributes.agreements[o],a=!1,f,l="agreements_";t.preventDefault(),n=this.model.get("formFields").emailField,n&&(r=this.$("#bv-email-field-"+n.schema.Id).val(),d.save({email:P.trim(r).toLowerCase()})),i=this.getForm(),h(u).forEach(function(e,t){var n;f=l+o+"_"+t,n=P("[name='"+f+"']")[0],n.checked?a=!0:a=!1}),a&&(P("[name='agreedtotermsandconditions']")[0].value=!0),this.model.get("fingerprint").preSubmitAction(i,function(t){t?s.trigger("showcannotsubmitpage",{view:s,message:A.error_FAILED_TO_LOAD_FINGERPRINT_AFTER(),isRecoverable:!0}):(i.find("[placeholdr]").length>0&&i.clearPlaceholdersInForm(),i.submit())})},setupRPC:function(){var t=this;t.rpc=g(t.rpcReady,function(n){k.hide(),n.SubmissionId?t.postSubmission(n):t.formErrorsPage(n)}),t.model.set("channelId",t.rpc.channel)},starHelper:function(t){return U(t).find(".bv-rating-helper")},ratingFieldHasError:function(t){return t.hasClass("bv-error")},ratingFieldHadError:function(t){return t.hasClass("bv-had-error")},ratingSetLabel:function(t,n){t.html(n)},ratingDisplayError:function(t){t.addClass("bv-error"),t.removeClass("bv-had-error")},ratingRemoveError:function(t){t.removeClass("bv-error"),t.addClass("bv-had-error")},ratingFocus:function(){var t,n,r=this;return function(i,s){t=t||r.starHelper(this),n=n||U(t),r.ratingFieldHasError(n)&&r.ratingRemoveError(n),t.data("helper",t.data("helper")||t.html()),r.ratingSetLabel(t,s.title),n.removeClass(r.ratingHoverClasses).addClass("bv-fieldset-h"+i)}},ratingBlur:function(){var t,n,r=this;return function(i,s){t=t||r.starHelper(this),n=n||U(t),t.data("helper")===A.helper_rating_1()&&r.ratingFieldHadError(n)&&r.ratingDisplayError(n),r.ratingSetLabel(t,t.data("helper")||""),n.removeClass(r.ratingHoverClasses)}},getForm:function(t){var n=t||this.model.toJSON(),r=[n.contentType,n.subject.Type,n.subject.SanitizedId,n.modelCid],i="#bv-submit"+r.join("-"),s=P(i);return s},formSetup:function(t){function b(e,t){var r=P(t).prop("title");n.starHelper(this).data("helper",r).html(r),U(this).removeClass(n.ratingClasses+" "+n.ratingHoverClasses),U(this).addClass("bv-fieldset-r"+e),n.validator.check(P(this)),n.focusEvent(P(this))}function w(e){var t,n,s,o;t=e.selector.match(i);if(!t)return;n=t[1],s=Number(t[2])-1,o=r[n],o[s]=e.val}function E(e){var t=P.Deferred();return y.get(escape(e),function(n){t.resolve(n)}),t}function S(){var e,t,i,s;e=h(r).keys(),t=h(r).chain().values().flatten().value();if(t.length===0)return;i=h(t).map(E),s={},P.when.apply(P,i).done(function(){h(arguments).forEach(function(t){if(!t)return;s[t.url]=t}),h(e).forEach(function(t){h(r[t]).forEach(function(r){var i=s[r];if(!i)return;n._addMedia(t,i)})})})}function T(e){var t=P(e[0])&&P(e[0]).attr("name");return s[t]}function N(){var e=P(this);n.validator.check(e.find("input:checked"))}function L(e,t){var r,i,o,u=s[e+"url_"+t];if(!u||!u.schema.Value)return;r=u.schema.Value,i=s[e+"caption_"+t],e==="video"?o=(new j(r)).getVideoData():o=P.Deferred().resolve(),o.done(function(s){var o;e==="video"?o=s&&s.thumbUrl:o=r.replace("photo.jpg","photoThumb.jpg"),n._addMedia(e,{id:u.schema.Id,caption:i&&i.schema.Value,url:r,thumbUrl:o})})}var n=this,r,i,s,o,u,a,f,c=this.model.toJSON(),p=this.getForm(c),d=t||{},v=navigator.userAgent.match(/(?:\b(MS)?IE\s+|\bTrident\/7\.0;.*\s)(\d+)/i),m=navigator.userAgent.match(/(iPhone|iPod)/),g=navigator.userAgent.match(/Version\/[\d\.]+.*Safari/i);return n.$form=p,n.fieldsets=p.find(".bv-input-fieldsets .bv-fieldset"),n.serverErrors=[],n.publish("formSetup",{}),n.ieCompat(),n.maxMedia={photo:this.model.getMaxNumByMediaType("photo"),video:this.model.getMaxNumByMediaType("video")},n.validator=x(p,{ruleSelectors:c.validationRules.rules,ignore:[".bv-form-ignore"],onError:function(t,r){var i=t.prop("name")+R;r!==i&&n.validator.removeSelectorRules(t,[i])},preSubmit:function(){n.validator.removeRule(n.serverErrors)},submit:h.debounce(function(t){n.scrollForm(),k.show(),p.find(".bv-submit").prop("disabled",!0),n.rpcReady.done(function(){p.trigger("submit.placeholder"),t.submit()}).fail(function(){n.trigger("showcannotsubmitpage",{view:n,isRecoverable:!0}),n.triggerPageView({value:"UnknownError"})})},500,!0)}),r={photo:[],video:[]},i=/name="(photo|video)url_(\d+)"/,g&&(d.isSafari=!0,m&&p.find("button.bv-form-actions-submit.bv-submit").attr("style","margin-bottom: 60px !important")),v&&(d.overrideNative=!0,d.preserveOnFocus=!0),p.placeholdr(d),setTimeout(function(){p.find("textarea").autosize()},0),s=n.model.getFields(),C.remember(p,function(t){var r=P(t);r.is(".bv-rating-input")?b.call(r[0],r.val(),r):r.is('[name="contextdatavalue_Age"]')&&(n.optedOut=r.val()==="17orUnder"),n.validator.count(r)},{noElementCallback:function(t){s.PreviousSubmissionId||w(t)}}),S(),n.$(".bv-fieldset-tags-group").each(function(){function o(e){var t=F.parse(e.schema.Id),r=n.find('input[name="tagid_'+t.id+"/"+t.childId+'"]'),i=e.schema.Value==="true";F.updateElEnabled(r,i)}var t,n=P(this),r=n.find("input:first").attr("name"),i=F.parse(r);i&&(t=s["tag_"+i.id],t&&t.tags&&h(t.tags).each(o))}),n.$(".bv-fieldset-rating, .bv-fieldset-secondary-rating").each(function(){var t=P(this),r=t.find(".bv-radio-input"),i=T(r);r.rating({callback:b,focus:n.ratingFocus(),blur:n.ratingBlur(),focusout:function(){n.validator.check(P(this))},withHeatMap:n.hasFeature("starHoverHeatMap")}),i&&i.schema.Value&&(r.rating("select",i&&i.schema.Value),n.validator.reporter.handlers.reset(r.first()))}),d.shortForm&&(n.fieldsets.addClass("bv-hidden"),n.fieldsets.first().removeClass("bv-hidden")),o=s.isrecommended,n.$(".bv-fieldset-isrecommended-group").bvradiosilence({displayAttr:"data-label",value:o&&o.schema.Value,inputAttrs:{role:"radio"},blur:N}),n.$(".bv-fieldset-slider-group").each(function(){var t=P(this),n=t.find("input"),r=T(n);t.bvradiosilence({labelInnerHtml:l,value:r&&r.schema.Value,inputAttrs:{role:"radio"},blur:N})}),u=s.netpromoterscore,a=s.netpromoterexpanded,n.$(".bv-fieldset-netpromoterscore-group").nps({value:u&&u.schema.Value,expanded:a&&a.schema.Value,inputAttrs:{role:"radio"},blur:N}),f=h.range(1,h.size(s)),h(f).forEach(function(t){L("photo",t),L("video",t)}),this._setupEmailValidation(),this._setupEmojiValidation(),this},_setupEmailValidation:function(){var t=this.$form.find(".bv-email-field"),n=t.find("input");this.validator.addRule("emailValidation",T),this.validator.addSelectorRules(n,{emailValidation:!0})},_setupEmojiValidation:function(){var t=this.validator,n=this.$form.find("input[type=text], input[type=email], textarea");this.validator.addRule("emojiValidation",N),n.each(function(n,r){t.addSelectorRules(P(r),{emojiValidation:!0})})},_setupERRErrors:function(){var t,r=this.model.get("formErrors");r&&(this.formErrorsPage({FormErrors:r}),t=P(".bv-error").first(),t.length>0?this.updateActiveElementCSS(t):(t=P(".bv-submission-rejection-errors"),n.setTimeout(function(){s.moveFocus(t)},0)))},_openTermsAndConditionsURL:function(t){var r=t.remoteUrl,i=n.open(r);i.focus()},getAgreementsType:function(){var t=this.model.get("contentType");return t==="answer"&&(t="question"),t=v.nouns(t),t},goToAgreements:function(n){var r=this,i,s,o,u,a,f=r.getAgreementsType(),l,h=P(n.currentTarget).closest("fieldset").find("input:first").attr("name"),p="agreements_"+f+"_";h=h.replace(p,""),l=this.model.attributes.agreements[f][h],n.preventDefault();if(l.displayType==="REMOTE_URL"){this._openTermsAndConditionsURL(l);return}i=new c.TermsConditionsView({parent:this,componentId:this.model.get("componentId"),fields:l}),s={title:l.agreementTitle,view:i,inline:!1,ariaDialog:!0,overlay:!1};if(this.inline){s.wrapClass="bv-core-container-"+t.get("config").version,S.get("lightbox").open(s);return}o=S.get(this.modestBoxId),o.ignoreKeys=!0,o.skipResizing=!0,u=S.get("terms-and-conditions"),u.once("afterOpen",function(){var t;if(o.inline||o.over)return;a=o.$layerList.height(),t=u.$layerList.height(),o.$layerList.height(t)}),u.open(s),u.once("afterClose",function(){o.$layerList.height(a),o.ignoreKeys=!1,o.skipResizing=!1})},setupUIElements:function(){function i(){try{t._setFocusOnForm()}catch(e){}}var t=this,n=this.model.get("clientAPIConfig"),r=this.inline?(n||{}).doShowContent||P.noop:P.noop;r(null,null,null,null,i,null)!==!1&&i()},_setFocusOnForm:function(){},_sendCollectOnSubmitEvent:function(){var t,n;if(!this.hasFeature("collectOnSubmit"))return;t=this.model.get("clientAPIConfig")||{},n=t.catalogData||{},_.feature({type:"Used",name:"Submission",detail1:"CollectOnSubmit",bvProduct:b.getType(this),locale:n.locale||null,catalogProducts:n.catalogProducts||null,catalogBrands:n.catalogBrands||null,catalogCategories:n.catalogCategories||null,deploymentZone:I.siteId})},_sendGRSEvent:function(){if(!this.isGenericSubmission)return;_.feature({type:"Used",name:"Submission",detail1:"GenericReviewSubmission",bvProduct:b.RATINGSANDREVIEWS,productId:b.getId(this),categoryId:b.getCategoryId(this)})},_sendNotificationsSubmissionEvent:function(){_.feature({type:"Used",name:"Submission",detail1:"submissionFormSubmit",source:"firebird",notificationId:this.model.get("bvNotificationId"),messageType:this.model.get("bvMessageType"),recipientDomain:this.model.get("bvRecipientDomain"),contentId:this.model.get("bvQuestionId"),clientName:I.clientname.toLowerCase(),bvProduct:b.getType(this),productId:b.getId(this),campaignId:this.model.get("campaignId"),userLocale:I.locale,dc:I.displaycode,deploymentZone:I.siteId})},postSubmission:function(t){var n=this,r=this.saveSubmission(t),i={view:this,response:r},s=i.response.SubmissionId;this._sendCollectOnSubmitEvent(),this._sendGRSEvent(),this._sendNotificationsSubmissionEvent(),u.trigger("submissionSubmitted",this.model.get("clientAPIConfig"),{contentType:this.model.get("contentType")}),this.model.get("fingerprint").postSubmitAction(s,function(){n.model.publish("contentSubmitted",i);if(!n.hasFeature("thankYouPage"))return n.previewSubmission(r);n.off("ModestBox:beforeClose"),n.trigger("showthankyoupage",i),n.triggerPageView({label:"Lightbox",value:"ThankYou",submissionId:s})})},saveSubmission:function(t){var n,r,i=this.model.getSubject(),s=t[v.Noun(i.contentType)];return s.RatingRange=I.submission.rating.range,s._contentType=i.contentType,s.SubmissionTime=E(),s.SubmissionId=t.SubmissionId,v.noun(this.model.get("contentType"))==="review"&&this.model.set("submissionResponse",s),t.AuthorSubmissionToken&&d.save({token:t.AuthorSubmissionToken}),n="hasSubmitted-"+i.id+"-"+i.contentType,r={},r[n]=!0,d.save(r),s},previewSubmission:function(t){var n;M.assert(h(t).isObject(),"preview submission requires a response object"),t.Author=t.Author||{},t.Author.ModerationStatus="APPROVED",this.model&&this.model.hasFeature("submissionPreview")&&(n=S.get(this.ModestBoxId),n.opened&&n.close(!0),this.model.get("contentType")==="comment"?this.model.trigger("previewsecondarycontent",this.model.get("subjectId"),t):this.model.trigger("previewcontent",t))},formErrorsPage:function(t){var n=this,r,i,s,o,u=n.$el.find("form"),a=[],f=[],l=[],c=t.FormErrors.FieldErrors;c&&!h.isEmpty(c)&&(h(t.FormErrors.FieldErrors).forEach(function(t){var r,i,s,o,a="[name="+t.Field+"]";l.push(a),r=u.find(a),i=t.Field+R,s={},o=t.Field.indexOf("photo")>=0||t.Field.indexOf("video")>=0;if(o)return;if(r.attr("type")==="hidden"){f.push(t);return}s[i]=!0,n.serverErrors.push(i),n.validator.addRule(i,function(){var n=!0;return function(r,i,s){var o;return n&&P.trim(r.val()).length===0&&(n=!1),n?(o=B.defaultMessage(t),{type:"server",code:t.Code,message:o}):!1}}()),n.validator.addSelectorRules(r,s),n.validator.check(r)}),f.length===0&&(r=u.find(l.join(", ")),r.length>0&&n.validator.showInvalid(r))),t.Errors&&t.Errors.length&&(this.model.trigger("queryError",t.Errors),a.push(t.Errors[0])),f.length?n.trigger("showcannotsubmitpage",{view:n}):a.length&&(i=a[0].Code,s=A["error_"+i],o=h.isFunction(s)?s():null,n.trigger("showcannotsubmitpage",{view:n,message:o,isRecoverable:!0})),n._reportErrors(f,!0),n._reportErrors(a),u.find(".bv-submit").removeProp("disabled")},cleanup:function(){var t;t=this.getFeatureView("dropdownable"),t&&t.popOut()},triggerPageView:function(t){var n=this.model.get("pageview");if(!n)return;_.pageview(h(t||{}).defaults({type:"Product",label:"Lightbox",context:"Write",bvProduct:n.bvProduct,productId:b.getId(this)}),this.model)},_reportErrors:function(t,n){var r;if(!t.length)return;r=h.map(t,function(t){return{name:n?w.errors.FORM:w.errors.API,detail1:t.Code,detail2:t.Message}}),_.error(r)},moveInitialFocus:function(){var t=this.getForm(),n=["input.bv-focusable:visible","textarea.bv-focusable:visible",".bv-submission-star-rating-control .bv-rating-link:visible"],r=n.join(", "),s=t.find(r).eq(0);s.length&&h.delay(function(){i.moveFocus(s)},0)}})}),BV.define("hbs!thankYou",["hbs","vendor/handlebars/runtime","template/helpers/view","template/helpers/renderIcon","template/helpers/unlessHasFeature","template/helpers/mountView"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){var r="",i,s,o;r+=' <hgroup class="bv-submission-message" role="presentaion"> ',o={hash:{classList:"bv-submission-icon",ariaHidden:"true"},inverse:h.noop,fn:h.program(2,d,t)},s=(i=n.renderIcon,i?i.call(e,"bv-sprite-thank-you",o):l.call(e,"renderIcon","bv-sprite-thank-you",o));if(s||s===0)r+=s;r+=' <span id="bv-mbox-label-text" class="bv-submission-text">',(s=n.message)?s=s.call(e,{hash:{}}):(s=e.message,s=typeof s===f?s.apply(e):s),r+=c(s)+"</span> </hgroup> ",o={hash:{},inverse:h.noop,fn:h.program(4,v,t)},s=(i=n.unlessHasFeature,i?i.call(e,"socialConnect",o):l.call(e,"unlessHasFeature","socialConnect",o));if(s||s===0)r+=s;return r+=" ",o={hash:{}},r+=c((i=n.mountView,i?i.call(e,"socialConnect",e,o):l.call(e,"mountView","socialConnect",e,o)))+" ",o={hash:{}},r+=c((i=n.mountView,i?i.call(e,"searchContentList",e,o):l.call(e,"mountView","searchContentList",e,o)))+" ",r}function d(e,t){var r;return(r=n.icon)?r=r.call(e,{hash:{}}):(r=e.icon,r=typeof r===f?r.apply(e):r),r||r===0?r:""}function v(e,t){var r="",i,s;return r+=" ",s={hash:{}},r+=c((i=n.mountView,i?i.call(e,"localSocialSharing",e,s):l.call(e,"mountView","localSocialSharing",e,s)))+" ",r}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f="function",l=n.helperMissing,c=this.escapeExpression,h=this;s+=" ",a={hash:{tag:"div",classList:"submission submission-thankyou submission-message-container focusable",ariaLabelledBy:"bv-mbox-label-text",tabindex:"0"},inverse:h.noop,fn:h.program(1,p,i)},u=(o=n.view,o?o.call(t,t,a):l.call(t,"view",t,a));if(u||u===0)s+=u;return s+=" ",s});return t.registerPartial("thankYou",n),n.deps=[],n.tplMountedViews=["localSocialSharing","socialConnect","searchContentList"],n}),BV.define("bv/c2013/view/thankYouPage",["framework/bview","hbs!thankYou"],function(e,t){return e.extend({name:"thankYouPage",classList:["thankyou"],template:t})}),BV.define("hbs!textDropdown",["hbs","vendor/handlebars/runtime","template/helpers/renderIcon"],function(e,t){var n=t.template(function(e,t,n,r,i){function c(e,t){var r,i,s;return s={hash:{classList:"bv-close-btn",ariaHidden:"true"},inverse:u.noop,fn:u.program(2,h,t)},i=(r=n.renderIcon,r?r.call(e,"bv-sprite-close",s):a.call(e,"renderIcon","bv-sprite-close",s)),i||i===0?i:""}function h(e,t){return" &#x2718; "}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u=this,a=n.helperMissing,f="function",l=this.escapeExpression;s+=' <div class="bv-cleanslate bv-cv2-cleanslate bv-absolute-top-container"> <div class="bv-core-container-',(o=n.version)?o=o.call(t,{hash:{}}):(o=t.version,o=typeof o===f?o.apply(t):o),s+=l(o)+'"> <div class="bv-shared bv-popup-container"> <!--[if lt IE 7]> <div class="bv-compat bvie6 bvie-lt8 bvie"> <![endif]--> <!--[if IE 7]> <div class="bv-compat bvie7 bvie-lt8 bvie"> <![endif]--> <!--[if IE 8]> <div class="bv-compat bvie8 bvie"> <![endif]--> <!--[if IE 9]> <div class="bv-compat bvie9 bvie"> <![endif]--> <!--[if gt IE 9]> <!--><div class="bv-compat"> <!--<![endif]--> <div id="',(o=n.dropdownId)?o=o.call(t,{hash:{}}):(o=t.dropdownId,o=typeof o===f?o.apply(t):o),s+=l(o)+'" class="bv-popup bv-textdropdown" role="tooltip" aria-hidden="true"> ',(o=n.text)?o=o.call(t,{hash:{}}):(o=t.text,o=typeof o===f?o.apply(t):o);if(o||o===0)s+=o;s+=" ",o=n["if"].call(t,t.autoOpen,{hash:{},inverse:u.noop,fn:u.program(1,c,i)});if(o||o===0)s+=o;return s+=" </div> </div> </div> </div> </div> ",s});return t.registerPartial("textDropdown",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("bv/ui-core/textDropdown",["ENV","window","body","backbone","underscore","jquery","hbs!textDropdown","framework/util/bvreporter","util/specialKeys"],function(e,t,n,r,i,s,o,u,a){return r.View.extend({options:{alwaysShow:!0,stylize:!0,attachDelay:50,detachDelay:500,popinClass:"bv-popup-in",popoutClass:"bv-popup-out",dimensionStyle:"containerOverflow",inModestBox:!1,inModestBoxClass:"bv-dropdown-inmbox",popInEvent:"mouseenter",popOutEvent:"mouseleave"},initialize:function(e){u.assert(e,"options are provided to textDropdown initialize method"),this.text=e.text,i(this.options).extend(e),this.id="bv-textdropdown-"+Math.floor(Math.random()*1e10),this.options.dimensionStyle=this.options.dimensionStyle||"containerOverflow",this.escKeyEventName="keyup."+this.id,this.options.dimensionStyle==="helperTextOverflow"&&(this.options.popInEvent="focus",this.options.popOutEvent="blur"),this.isElFocusable=this.$el.hasClass("bv-focusable")||this.$el.find(".bv-focusable").length>0,u.assert(this.isElFocusable||this.options.inModestBox&&s("#bv-mbox-lightbox-list").length>0,"textDropdowns need a focusable element or a modestbox to target"),this.isElFocusable?this.$target=this.$el:this.$target=s("#bv-mbox-lightbox-list"),this.options.autoOpen?this.popIn():this.attach(),this.options.inModestBox&&(this.options.attachDelay=0)},attach:function(){var e=this;if(this.options.disableEvents)return;this.$el.on(this.options.popInEvent,function(){e.popIn()}),this.$el.on(this.options.popOutEvent,function(){e.popOut()})},stylize:function(e){return'<div class="bv-dropdown-style-'+this.options.dimensionStyle+'-inner" >'+e+"</div>"},setDropdownEvents:function(){var e=this;this.$dropdownContainer.find(".bv-close-btn").click(function(){e.popOut()})},dimensionFilters:{containerOverflow:function(e,t){var n={position:"absolute",opacity:0},r=s(e),i=s(t),o=r.offset(),u=i.offset();n.width=i.outerWidth()+20,n.left=u.left-10,n.top=o.top+r.outerHeight(!0)+30;var a={width:n.width-22},f={left:o.left-n.left+r.outerWidth(!0)/2};return{outer:n,inner:a,arrow:f}},helperTextOverflow:function(e,t){var n={position:"absolute",opacity:0},r=s(e),i=s(t),o=r.offset(),u=i.offset();n.width=i.outerWidth()/3-20,n.left=u.left-n.width,n.top=o.top-30;var a={width:n.width-21},f={left:u.left-n.left-10,top:10};return{outer:n,inner:a,arrow:f}},sliderTextOverflow:function(e,t){var n={position:"absolute",opacity:0},r=s(e),i=s(t),o=r.offset(),u=i.offset();n.width=i.outerWidth()/4,n.left=o.left+r.outerWidth(!0)/2-n.width/2+10,n.top=o.top-10-r.outerHeight(!0);var a={width:n.width-22},f={left:o.left-n.left+r.outerWidth(!0)/2-2};return{outer:n,inner:a,arrow:f}}},adjustFilters:{sliderTextOverflow:function(){var e=parseInt(this.$innerContainer.css("lineHeight").replace("px",""),10)+25,t=this.$innerContainer.outerHeight();if(t>e){var n=parseInt(this.$dropdownContainer.css("top").replace("px",""),10)+e-t;this.$dropdownContainer.css("top",n+"px !important;")}if(this.injectedArrayDimension&&this.injectedStyle){this.injectedArrayDimension.top=t-2;var r=this.generateArrowStyles(this.injectedArrayDimension);this.injectedStyle.textContent=r}}},generateArrowStyles:function(e){var t="";return i(e).forEach(function(e,n){t+=n+":"+e+"px !important; "}),"#"+this.id+":before, #"+this.id+":after{"+t+"} "},injectArrowStyles:function(e){var t=document.head||document.getElementsByTagName("head")[0]||document.body||document.getElementsByTagName("body")[0],n=document.createElement("style"),r=this.generateArrowStyles(e);n.type="text/css",n.styleSheet?n.styleSheet.cssText=r:n.appendChild(document.createTextNode(r)),t.appendChild(n),this.injectedStyle&&s(this.injectedStyle).remove(),this.injectedArrayDimension=e,this.injectedStyle=n},addStyles:function(){var e=this.dimensionFilters[this.options.dimensionStyle].call(this,this.options.positionTarget,this.options.outerContainer);this.$dropdownContainer.css(e.outer),this.$innerContainer.css(e.inner),this.injectArrowStyles(e.arrow)},popIn:function(){var e=this,r=this.options.attachDelay;s(function(){e.render(),s(n()).append(e.$dropdownContainer),e.$target.attr({"aria-describedby":e.id});var o=function(){e.addStyles()};o(),s(t).bind("resize."+e.id,i.debounce(o,200)),e.$compatContainer.outerHeight()<=0&&e.$compatContainer.css("height",e.$innerContainer.outerHeight()),e.$target.off(e.escKeyEventName).on(e.escKeyEventName,function(t){t.keyCode===a.ESCAPE&&(t.preventDefault(),t.stopImmediatePropagation(),e.popOut())});if(!e.options.alwaysShow){var u=e.$dropdownContainer.offset();if(u.top<0||u.left<0)return}setTimeout(function(){e.$dropdownContainer.addClass(e.options.popinClass),e.$dropdownContainer.addClass(e.options.popinClass+"-"+e.options.dimensionStyle);var t=e.adjustFilters[e.options.dimensionStyle];t&&t.apply(e)},r)})},popOut:function(){var e=this,n=this.options.detachDelay;this.$dropdownContainer&&this.$dropdownContainer.addClass(this.options.popoutClass),this.$target.attr({"aria-describedby":null}).off(this.escKeyEventName),s(t).unbind("resize."+e.id);var r=this.$dropdownContainer,i=s(this.injectedStyle);setTimeout(function(){r&&r.remove(),e.injectedArrayDimension=null,i.remove()},n)},render:function(){var t=o({text:this.options.stylize?this.stylize(this.text):this.text,version:e.get("config").version,dropdownId:this.id,autoOpen:this.options.autoOpen});this.$dropdownContainer=s(t),this.$innerContainer=this.$dropdownContainer.find(".bv-popup"),this.$compatContainer=this.$dropdownContainer.find(".bv-compat"),this.$innerContainer.addClass("bv-dropdown-style-"+this.options.dimensionStyle),this.addStyles(),this.setDropdownEvents(),this.options.inModestBox&&this.$dropdownContainer.addClass(this.options.inModestBoxClass)},close:function(){this.$dropdownContainer&&this.popOut()}})}),BV.define("bv/c2013/view/dropdownable",["framework/bview","jquery","underscore","bv/ui-core/textDropdown"],function(e,t,n,r){return e.extend({name:"dropdownable",options:{inModestBox:"data-bv-dropdown-inmbox",autoOpen:"data-bv-dropdown-autoopen",positionTarget:"data-bv-dropdown-position-target",disableEvents:"data-bv-dropdown-disable-events",alwaysShow:"data-bv-dropdown-always-show",style:"data-bv-dropdown-style",text:"data-bv-dropdown-text"},dataAttributes:"inModestBox autoOpen positionTarget disableEvents alwaysShow style text".split(" "),template:function(){return""},render:function(){},dropdowns:{},getHashId:function(){var e=this.parent;return[e.name,e.uniq,this.name,this.uniq].join("_")},_createDropdown:function(e,t){var n=new r({autoOpen:t.autoOpen,el:e,text:t.text,outerContainer:this.parent.$el[0],inModestBox:t.inModestBox,dimensionStyle:t.style,positionTarget:t.positionTarget,disableEvents:t.disableEvents,alwaysShow:t.alwaysShow});return this.dropdowns[this.getHashId()]=n,n},_deleteDropdown:function(){delete this.dropdowns[this.getHashId()]},attach:function(r){var i=this;return this.$base=this.parent.$el,this.targetEls=this.$base.find("["+this.options.text+"]"),this.targetEls.each(function(){var e=t(this),r={};n(i.dataAttributes).each(function(t){var n=e.attr(i.options[t]);r[t]=n===""||n==="true"||n,r[t]==="false"&&(r[t]=!1)}),r.alwaysShow!==!1&&(r.alwaysShow=!0),r.positionTarget?r.positionTarget="#"+r.positionTarget:r.positionTarget=this,i._createDropdown.call(i,this,r)}),e.prototype.attach.apply(this,arguments)},_getDropdown:function(){return this.dropdowns[this.getHashId()]},popIn:function(){var e=this._getDropdown();e&&e.popIn()},popOut:function(){var e=this._getDropdown();e&&e.popOut(),this._deleteDropdown()}})}),BV.define("mappings!submission/views",["bv/c2013/view/submission","bv/c2013/view/thankYouPage","bv/c2013/view/dropdownable"],function(e,t,n){var r={submission:{name:"submission",view:e},thankYouPage:{name:"thankYouPage",view:t},dropdownable:{name:"dropdownable",view:n},starHoverHeatMap:{name:"starHoverHeatMap",has:{name:"starHoverHeatMap"}},submissionPreview:{name:"submissionPreview",has:{name:"submissionPreview"}},reviewHelperText:{name:"reviewHelperText",has:{name:"reviewHelperText"}},mbox:{name:"mbox",has:{name:"mbox"}}};return r}),BV.define("bv/util/xdmPost",["ENV","underscore","jquery","bv/util/withRPC","bv/api","util/url","body","bv/strings"],function(e,t,n,r,i,s,o,u){function a(e,t){var i=n.Deferred(),s=n.Deferred(),o;t.callback=u.rpc.postCallback;var a=r(s,function(e){var t;c(a,o);if(e&&e.HasErrors===!1)i.resolve(e);else{if(e&&e.HasErrors){t=e.Errors[0].Code,i.reject(t,e);return}i.reject("APIERROR",e)}});return s.then(function(){o=l(e,t,a.channel),o[0].submit()},function(){c(a,o),i.reject("RPCERROR")}),i.promise()}function f(n,r){var s=i._config,o="https:"+s.baseUrl+n,u=e.get("config").locale,f={passkey:s.passkey,apiversion:s.apiversion,displaycode:s.displaycode||"",ve:s.virtualEnvironment||""};return u&&(f.locale=u),a(o,t.extend(f,r))}function l(e,r,i){var u=n('<form action="'+s.caboose(e)+'" target="bv-submission-target-'+t.escape(i)+'" method="POST"/>');return t(r).forEach(function(e,n){u.append('<input type="hidden" name="'+t.escape(n)+'" value="'+t.escape(e)+'"/>')}),u.appendTo(o()),u}function c(e,t){try{e.destroy(),t.remove()}catch(n){}}return{raw:a,api:f}}),BV.define("bv/ugc/feedback",["ENV","underscore","bv/util/session","bv/util/loadingOverlay","bv/api","bv/util/xdmPost","window","util/url","jquery"],function(e,t,n,r,i,s,o,u,a){function l(e){var t=a.Deferred(),r=t.promise(),i=e.contentType+"-"+e.feedbackType.charAt(0)+"-"+e.contentId;return n.get("feedbacks",function(s){s=s||{},r.done(function(){s[i]=e.feedbackType==="inappropriate"?!0:{c:e.newCount,v:e.vote.charAt(0)},n.set("feedbacks",s)});var o=s[i];o?t.resolve("DUPLICATE",o):c(t,e)}),r}function c(e,n){var r=t.clone(n);delete r.newCount,s.api("submitfeedback.json",r).then(function(t){e.resolve(!1,t)},function(t,n){e.reject(t,n)})}var f=e.get("config");return{positive:function(e,t,n){return l({contentType:e,contentId:t,feedbackType:"helpfulness",vote:"positive",newCount:n})},negative:function(e,t,n){return l({contentType:e,contentId:t,feedbackType:"helpfulness",vote:"negative",newCount:n})},inappropriate:function(e,t){return l({contentType:e,contentId:t,feedbackType:"inappropriate"})}}}),BV.define("mf!bv/c2013/messages/postSubmission",["vendor/messageformat","framework/util/bvtrackerqueue"],function(e,t){return{contentSubmitted_review:function(e){try{return function(e){var t="";return t+="Votre avis a été soumis !",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `contentSubmitted_review`: "+n.toString())]),""}},contentSubmitted_comment:function(e){try{return function(e){var t="";return t+="Votre commentaire a été soumis !",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `contentSubmitted_comment`: "+n.toString())]),""}},contentSubmitted_question:function(e){try{return function(e){var t="";return t+="Votre question a été soumise !",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `contentSubmitted_question`: "+n.toString())]),""}},contentSubmitted_answer:function(e){try{return function(e){var t="";return t+="Votre réponse a été soumise !",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `contentSubmitted_answer`: "+n.toString())]),""}},postSubmission_unknownError:function(e){try{return function(e){var t="";return t+="Une erreur s'est produite. Veuillez réessayer plus tard.",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `postSubmission_unknownError`: "+n.toString())]),""}},postSubmission_errorOffScreenText:function(e){try{return function(e){var t="";return t+="Appuyez sur Échap pour revenir au formulaire.",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `postSubmission_errorOffScreenText`: "+n.toString())]),""}},postSubmission_unrecoverableErrorOffScreenText:function(e){try{return function(e){var t="";return t+="Appuyez sur Échap pour fermer ce message",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `postSubmission_unrecoverableErrorOffScreenText`: "+n.toString())]),""}},duplicateContent:function(e){try{return function(e){var t="";if(!e)throw new Error("MessageFormat: No data passed to function.");var n="Gender",r=e[n],i=0,s={other:function(e){var t="";return t+="Vous avez déjà soumis un avis. Merci !",t}};return t+=(s[r]||s.other)(e),t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `duplicateContent`: "+n.toString())]),""}},unavailableSubmission:function(e){try{return function(e){var t="";return t+="La fonction de soumission n'est actuellement pas disponible.",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `unavailableSubmission`: "+n.toString())]),""}},userVerified:function(e){try{return function(e){var t="";if(!e)throw new Error("MessageFormat: No data passed to function.");var n="Gender",r=e[n],i=0,s={other:function(e){var t="";return t+="Merci ! Votre contenu a bien été authentifié.",t}};return t+=(s[r]||s.other)(e),t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `userVerified`: "+n.toString())]),""}},userAlreadyVerified:function(e){try{return function(e){var t="";return t+="Cette adresse email a déjà été vérifiée.",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `userAlreadyVerified`: "+n.toString())]),""}},ariaTitle_answer:function(e){try{return function(e){var t="";return t+="Répondre à cette question",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `ariaTitle_answer`: "+n.toString())]),""}}}}),BV.define("bv/ugc/submission",["ENV","mappings!submission/views","bv/util/submissionSubviews","underscore","bv/api/fetch","bv/util/user","bv/util/contentType","bv/util/session","bv/ui-core/modestbox","bv/ui-core/focusableview","bv/ugc/feedback","bv/util/formMemory","bv/util/loadingOverlay","bv/util/productInfo","mf!bv/c2013/messages/postSubmission","mf!bv/c2013/messages/submission","jquery","framework/util/bvtracker","bv/c2013/view/contentItem","bv/c2013/model/contentItem","BV"],function(e,t,n,r,i,s,o,u,a,f,l,c,h,p,d,v,m,g,y,b,w){function T(e){var t=e.view,n=e.response,i,u,l,h,p,v;if(!t)return;i=t.getFeatureView("thankYouPage"),u=d["contentSubmitted_"+o.noun(t.model.get("contentType"))](),i.model.set("message",u),i.afterClose=function(){n.Author=s.getAuthor(),t.previewSubmission(n)},p=!0,t&&"closeAllPostSubmission"in t&&(p=t.closeAllPostSubmission),i&&(l=a.get("lightbox"),l.once("afterClose",i.afterClose),h=r.extend({},S,{view:i,breadcrumbs:this.breadcrumbs,titleIcon:i.model.get("icon"),closeAll:p,afterShow:function(){var e=this;setTimeout(function(){var t,n="#"+e.box.injectionId,r=[n+" .bv-mbox-close.bv-focusable",n+" .bv-mbox-current .bv-focusable"].join(", ");f.registerFocusableLayer(m(document),r),t=m(".bv-submission-message-container"),f.moveFocus(t)},300)}}),l.replaceOrOpen(h),i.hasFeature("localSocialSharing")&&(v=t.getFeatureView("localSocialSharing"),v.model.set("reviewContent",n))),c.forget()}function N(e){var t=e.view,i=e.message||d.postSubmission_unknownError(),s=!!e.isRecoverable,o=a.get(t.ModestBoxId),u=d[s?"postSubmission_errorOffScreenText":"postSubmission_unrecoverableErrorOffScreenText"],l=new n.SubmissionMessageView({componentId:t.componentId,message:i,offScreenText:u(),classList:["unknown-error"],icon:"&#x2718;"}),c=r.extend({},S,{view:l,afterShow:function(){var e=m(".bv-submission-message-container");setTimeout(function(){f.moveFocus(e)},0)},afterHide:function(e){var t=e.box.layers,n=r(t).last(),i;t.length>0&&(n===e&&t.length>1&&(n=t[t.length-2]),i=n.$target.find(".bv-focusable"),setTimeout(function(){f.moveFocus(i)},500))}});o[s?"pushOrOpen":"replaceOrOpen"](c)}function C(e){var t=a.get("lightbox"),i=new n.SubmissionMessageView({componentId:e.get("componentId"),message:d.duplicateContent(),offScreenText:d.postSubmission_errorOffScreenText(),classList:["duplicate"],icon:"&#x2718;"}),s=r.extend({},S,{view:i,closeUrl:e.closeUrl||null});t.pushOrOpen(s),g.pageview({type:"Product",label:"Lightbox",value:"AlreadySubmitted",context:"Write",productId:p.getId(this)})}function k(e,t){var i,s,o;if(t==="duplicate")return C(e);i=new n.SubmissionMessageView({componentId:e.get("componentId"),message:d.unavailableSubmission(),offScreenText:d.postSubmission_errorOffScreenText(),classList:["unavailable"],icon:"&#x2718;"}),r(e.get("clientAPIConfig").onShow).isFunction()?e.get("clientAPIConfig").onShow(i):(s=a.get("lightbox"),o=r.extend({},S,{view:i,closeUrl:e.closeUrl||null}),s.pushOrOpen(o)),g.pageview({type:"Product",label:"Lightbox",value:"Unavailable",context:"Write",productId:p.getId(this)})}function L(t,n){var i=e.get("componentManager").find(t),s=o.noun(i.contentType),u=new b(r.extend({component:i,name:"dummy"},n)),a=new y({model:u,config:u,classList:["content-top-"+s],tag:"div",views:[]});return a}function A(i,s){var u=i.toJSON().subject||{},f=e.get("componentManager").find(i.get("componentId")),l,c,h,d,y,b,E,x,C,k,A,O,M,D,P,H;if(!f)return;l="closeAllPostSubmission"in s?s.closeAllPostSubmission:!0,c=new t.submission.view({model:i,contentType:f.contentType,componentId:f.componentId,parent:{name:f.type,containerId:s&&s.containerId?s.containerId:f.container},inline:s&&s.inline||f.inline&&m("#"+f.container).length,classList:[u.ImageUrl?"submission-image":"submission-noimage"],isGenericSubmission:!!s.isGenericSubmission,closeAllPostSubmission:l}),c.on("showthankyoupage",T,s),c.on("showcannotsubmitpage",N),c.rpc||c.setupRPC(),e.isContainer()&&f.contentType==="Answers"&&(h=L("questionContentList1",i.get("subjectData"))),d=!0,y=i.get("clientAPIConfig").onShow,r(y).isFunction()&&(d=y(c)),d&&(b=a.get(c.ModestBoxId),E=o.noun(i.get("contentType")),v["aria_header_"+E]&&u.Name&&(x=v["aria_header_"+E]({productName:u.Name})),C=w._internal.container&&!e.get("config").siteAuth[E],k=window.history.length-1,b.once("afterClose",function(e){if(E==="answer"){e.goBackUponClose=C;return}if(C&&k>1)try{window.history.go(k-window.history.length)}catch(t){window.location.href=s.returnUrl||"about:blank"}else!e.opened&&s.returnUrl&&(window.location.href=s.returnUrl)}),A=null,u.ImageUrl&&!c.inline&&(A=new n.SidebarView({model:i})),O=null,c.parent.containerId&&(O=m("#"+c.parent.containerId)),M=r(c.formSetup).chain().once().bind(c).value(),D=[c],h&&D.unshift(h),P=u.Name||"",H=r.extend({},S,{title:v["header_"+E]({productNameAvailable:!!P,productName:P}),ariaTitle:x,classList:["content-submission-"+E],views:D,sidebar:A,inline:c.inline,autoFocus:!1,container:O,closeUrl:w._internal.container?u.Url||e.get("config").homePageUrl:null,ariaDialog:!0,showCloseButton:s&&typeof s.closable!="undefined"?s.closable:!0,breadcrumbs:s.breadcrumbs,beforeShow:function(){M(),c.trigger("viewready")},afterShow:function(){c.trigger("viewshown")},afterHide:function(){c.cleanup(),g.feature({type:"Used",name:"Close",detail1:"Submission",detail2:"Lightbox",bvProduct:p.getTypeByContentType(E),productId:s.productId,categoryId:p.getCategoryId({model:i})})}}),b.once("beforeClose",function(){c.trigger("viewclosed")}),s.push?b.pushOrOpen(H):b.replaceOrOpen(H))}var E,S,x=e.get("mappings");return e.set("mappings",m.extend(!0,{},x,t)),S={},E=r.debounce(function(e,t){u.setup(),e.off("loadsucceeded").on("loadsucceeded",function(e){A(e,t)}),e.off("loadfailed").on("loadfailed",function(e,t){var n=i.get("products",e.attributes.subjectId).limitTo(1);n.fetch("subject").done(function(r){e.closeUrl=r.data.Results[0].Url,k(e,t)})}),e.trigger("load")},300,!0),{createContentItemView:L,feedback:l,thankYouPage:T,lightbox:function(e,t){t&&!t.silentMode&&h.show(),m(function(){E(e,t)})}}}),BV.define("bv/ugc/hostedAuthLink",["ENV","underscore","jquery","BV","$BV","bv/ui-core/modestbox","mf!bv/c2013/messages/postSubmission","bv/c2013/view/submissionMessage","bv/util/user","util/url","window","framework/util/bvtracker","bv/util/domainPolice"],function(t,n,r,i,s,o,u,a,f,l,c,h,p){function v(e){return e==="DUPLICATE"?u.userAlreadyVerified:u.postSubmission_unknownError}function m(){var e=t.get("config"),n=l.getParam(c.location.href,"bvnotificationId")||l.getBvParam(c.location.href,"notificationId"),r=l.getParam(c.location.href,"bvmessageType")||l.getBvParam(c.location.href,"messageType"),i=l.getParam(c.location.href,"bvrecipientDomain")||l.getBvParam(c.location.href,"recipientDomain"),s=t.get("appRouter").clientAPIConfig.campaignId||"";h.feature({type:"Shown",name:"Confirmation",bvProduct:"RatingsAndReviews",notificationId:n,messageType:r,recipientDomain:i,userLocale:e.locale,campaignId:s})}var d;return d={lightbox:function(h,d,g){var y=t.get("componentManager"),b=y.find(function(t){return t.type==="submission"}),w=h?"&#x2714;":"&#x2718;",E=h?u.userVerified:v(d),S=h?["thankyou"]:["unknown-error"];r(function(){var t,r;h&&g&&(f.clear({silent:!0}),f.save({token:g}),n(s.setUser).isFunction()&&p.thirdPartyCookieEnabled()&&s.setUser(g)),t=new a({componentId:b.componentId,message:E(),classList:S,icon:w}),r=o.get("lightbox"),r.once("afterClose",function(){i._internal.container||(c.location=l.splice(c.location.href,"bv_authtoken"))}),r.open({view:t}),m()})}},d}),BV.define("bv/ugc/authUser",["bv/util/xdmPost","bv/ugc/hostedAuthLink","bv/util/loadingOverlay"],function(e,t,n){function r(r){function s(e){var n=e.Authentication.User;t.lightbox(!0,null,n)}function o(e){var n={ERROR_BAD_REQUEST:"DUPLICATE"},r=n[e]||e;t.lightbox(!1,r)}var i=r.authToken;n.show(),e.api("authenticateuser.json",{authtoken:i}).then(s,o)}return r}),BV.define("bv/util/unsubscribePost",["ENV","BV","underscore","jquery","bv/util/withRPC","bv/api","util/url","bv/strings","framework/util/bvtracker"],function(t,n,r,i,s,o,u,a,f){function l(e,r){var i,s,f=t.get("config"),l=n._options.notifications.passkey,c=f.clientname,h=f.unsubscribeAllMessageTypes,p;return h?p="ALL":p=r||"PIE",i=o._config.notificationsUrl,s={passkey:l,userToken:e,emailType:p,callback:a.rpc.postCallback},u.join("https:"+i,c,"subscriptions/unsubscribe?"+u.stringifyObject(s))}function c(){var e=t.get("config"),n=u.getParam(window.location.href,"bvnotificationId")||u.getBvParam(window.location.href,"notificationId"),r=u.getParam(window.location.href,"bvmessageType")||u.getBvParam(window.location.href,"messageType"),i=u.getParam(window.location.href,"bvrecipientDomain")||u.getBvParam(window.location.href,"recipientDomain"),s=t.get("appRouter").clientAPIConfig.campaignId||"";f.feature({type:"Shown",name:"Unsubscribe",bvProduct:"RatingsAndReviews",notificationId:n,messageType:r,recipientDomain:i,userLocale:e.locale,campaignId:s})}function h(e,t){var n=i('<form action="'+u.caboose(e)+'" target="bv-submission-target-'+r.escape(t)+'" method="POST" />');return n.appendTo("body"),n}function p(e,t){try{e.destroy(),t.remove()}catch(n){}}function d(e,t){var n,r,o=i.Deferred(),u=i.Deferred(),a;return c(),n=l(e,t),r=s(u,function(t){p(r,a),t?o.resolve(t):o.reject("APIERROR")}),u.then(function(){a=h(n,r.channel),a[0].submit()},function(){p(r,a),o.reject("RPCERROR")}),o.promise()}return d}),BV.define("mf!bv/c2013/messages/unsubscribe",["vendor/messageformat","framework/util/bvtrackerqueue"],function(e,t){return{userUnsubscribed:function(e){try{return function(e){var t="";return t+="Votre e-mail a été supprimé de la liste de distribution.",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `userUnsubscribed`: "+n.toString())]),""}},unsubscribe_unknownError:function(e){try{return function(e){var t="";return t+="Une erreur s'est produite. Veuillez réessayer plus tard.",t}(e||{})}catch(n){return t.push(["error",new Error("MF error on `unsubscribe_unknownError`: "+n.toString())]),""}}}}),BV.define("hbs!unsubscribeMessage",["hbs","vendor/handlebars/runtime","template/helpers/view"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){var r="",i;r+=' <hgroup class="bv-submission-message"> <h1 class="bv-submission-icon" aria-hidden="true">',(i=n.icon)?i=i.call(e,{hash:{}}):(i=e.icon,i=typeof i===f?i.apply(e):i);if(i||i===0)r+=i;return r+='</h1> <h2 id="bv-mbox-label-text" class="bv-submission-text bv-focusable" tabindex="-1">',(i=n.message)?i=i.call(e,{hash:{}}):(i=e.message,i=typeof i===f?i.apply(e):i),r+=l(i)+"</h2> </hgroup> ",r}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f="function",l=this.escapeExpression,c=this,h=n.helperMissing;a={hash:{tag:"div",classList:"submission submission-message-container"},inverse:c.noop,fn:c.program(1,p,i)},u=(o=n.view,o?o.call(t,t,a):h.call(t,"view",t,a));if(u||u===0)s+=u;return s+=" ",s});return t.registerPartial("unsubscribeMessage",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("bv/c2013/view/unsubscribeMessage",["ENV","framework/bmodel","framework/bview","hbs!unsubscribeMessage"],function(e,t,n,r){return n.extend({name:"UnsubscribeMessage",template:r,init:function(e){var n=this.getComponent();this.model=new t({name:"UnsubscribeMessage",message:e.message,icon:e.icon,componentId:this.componentId,component:n,contentType:n.contentType})}})}),BV.define("bv/ugc/unsubscribeView",["ENV","underscore","jquery","bv/ui-core/modestbox","mf!bv/c2013/messages/unsubscribe","bv/c2013/view/unsubscribeMessage","bv/util/user","util/url","window"],function(e,t,n,r,i,s,o,u,a){function f(e,t){return e?i.userUnsubscribed:i.unsubscribe_unknownError}function l(t,i){function h(){t;var e=new s({icon:a,message:l(),classList:c,componentId:u.componentId});r.get("lightbox").open({view:e,showCloseButton:!1})}var o=e.get("componentManager"),u=o.find(function(e){return e.type==="submission"}),a=t?"&#x2714;":"&#x2718;",l=f(t,i),c=t?["thankyou"]:["unknown-error"];n(h)}var c={success:function(){l(!0)},failure:function(e){l(!1,e)}};return c}),BV.define("bv/ugc/unsubscribeUser",["ENV","BV","jquery","underscore","util/url","bv/api","bv/util/unsubscribePost","bv/ugc/unsubscribeView","bv/util/loadingOverlay","bv/strings","framework/util/bvtracker"],function(e,t,n,r,i,s,o,u,a,f,l){function c(e){var t=e.unsubscribeToken,n=e.emailType;a.show(),p(t,n).then(function(e){u.success()},function(e){u.failure(e)})}function h(e,t){function n(){e.state()!=="resolved"&&(l.error({name:f.errors.TIMEOUT,detail1:"Unsubscribe",detail2:"Token: "+t}),e.reject("timeout"))}return r.delay(n,1e4)}function p(e,t){var r=n.Deferred(),i=h(r,e);return o(e,t).then(function(e){clearTimeout(i),r.resolve(e)},function(e){r.reject(e)}),r.promise()}return c}),BV.define("bv/ugc/submit/urlForClientContainer",["util/url"],function(e){function t(t,n,r){var i=e.hash(t),s=e.withoutHash(t);i&&(i=i.replace(/(?:\?|&)$/,""));var o=/(?:\?|&)$/.exec(s);o||(s+=s.indexOf("?")>-1?"&":"?");var u=s+n.substr(1);return u+=i.length>0?i:r,u}return t}),BV.define("bv/ugc/container",["ENV","jquery","underscore","util/url","window","body","framework/util/bvtracker","BV","bv/api","bv/util/urlstate","bv/util/domainPolice","bv/util/session","bv/util/normalizeParams","bv/util/loadingOverlay","bv/ui-core/modestbox","bv/ugc/submit/urlForClientContainer","bv/strings"],function(e,t,n,r,i,s,o,u,a,f,l,c,h,p,d,v,m){function S(e){return e.replace(/.*\//,"")}function x(){var e=g.match("(/bvstaging)?/[^/]+/([^/]+)/[^/]+.htm"),t=e?decodeURIComponent(e[2]):undefined;return{config:{productId:t},action:"rr_submit_review"}}function T(e,n){var r=new Error(n);o.error({name:m.errors[e],detail1:n}),t(s()).addClass("bv-container-error"),i.console&&console.error&&console.error("BV: Error: ",n)}function N(){function r(r){var s=r.closeUrl||e.get("config").homePageUrl;n.delay(function(){if(document.referrer!==""&&r.goBackUponClose&&t>1)try{i.history.go(t-i.history.length)}catch(e){i.location.href=s||"about:blank"}else!r.opened&&s&&(i.location.href=s)},500)}var t=i.history.length-1;d.get("lightbox").on("afterClose",r).on("changeTitle",function(e,t,n){document.title=t})}function C(){var e=g.match("(/bvstaging)?/answers/[^/]+/([^/]+)/([^/]+)/askquestion.htm"),t=e?decodeURIComponent(e[2]):undefined,n=e?decodeURIComponent(e[3]):undefined,r={config:{subjectType:t},action:"qa_submit_question"};return r.config[t+"Id"]=n,r}function k(e){var t=w[y]();t.config.campaignId=f.get("campaignid")||"",$BV.ui("",t.action,n.extend({},e,t.config))}function L(t){var s=f.bvParams("bv"),o=s.action||t.action,u=n.extend({},t,h(s)),a=r.caboose(u.containerPageUrl),c;delete u.action;if(!a||!l.allowedDomain(a))a=e.get("config").container.url;E[o]&&(u.containerId="BVQAContainer");if(u.hosted&&a){c=v(a,i.location.search,i.location.hash),i.location.replace(c);return}o?$BV.ui("",o,u):T("IMPLEMENTATION","The 'bvaction' parameter is not present in the URL, so we can't determine which action to take. Please ensure that URL parameters exist and were not stripped by an HTTP redirect.")}function A(t){var n=e.get("config").container,s=n.url,o=r.origin+i.location.pathname;if(s&&l.allowedDomain(s)&&s.indexOf(o)===-1&&i.location.href.indexOf("bvnotificationId")===-1){i.location.replace(s+i.location.search.substr(1)+i.location.hash);return}$BV.ui("global","auth_user",{authToken:t})}function O(e,t){$BV.ui("global","unsubscribe_user",{unsubscribeToken:e,emailType:t})}var g=i.location.pathname.toLowerCase(),y=S(g),b,w={"askquestion.htm":C,"submission.htm":x,"writereview.htm":x},E={qa_submit_question:!0,qa_submit_answer:!0};return b={init:function(e){var o=e.containerId,a=f.get("bv_authtoken"),l=f.get("bv_unsubtoken"),h=e.userToken||f.get("userToken","bv");e.containerId="BVContainer";if(f.get("mobile","bv")||navigator.userAgent.match(/(iPhone|iPod|Android|BlackBerry)/))u._internal.mobile=!0,t("head").append('<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">');t(function(){var d,v;t(s()).html('<div id="BVContainer"><div class="bv-inline-form-container bv-cleanslate bv-cv2-cleanslate"/></div>').addClass("bv-standalone-container"),p.show(),N(),d=f.get("eventid","bv"),d&&!h&&c.get(d,function(e){var t;if(e){t=e.returnUrl,c.remove(d),i.location.replace(t);return}});if(a){A(a);return}if(l){v=f.get("emailtype"),O(l,v);return}if(h){e.containerId=o;if(d){c.get(d,function(t){var s,o,a;t&&(s=n.extend({},e,t),c.remove(d),u._internal.mobile?L(s):(o={action:s.action,campaignId:s.campaignId,userToken:h},n(s).forEach(function(e,t){t.match(/Id$/)&&s[t]&&(o[t]=s[t])}),t.isContainer?a=t.returnUrl+"&bvuserToken="+h:a=r.caboose(t.returnUrl)+"bvdata="+encodeURIComponent(r.stringifyObject(o))+r.hash(t.returnUrl),i.location.replace(a)))});return}}e.hosted&&w[y]?k(e):L(e)})}},b}),BV.define("hbs!deeplink",["hbs","vendor/handlebars/runtime"],function(e,t){var n=t.template(function(e,t,n,r,i){this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u="function",a=this.escapeExpression;return s+='<div class="bv-deeplink"> <span id="bv-mbox-label-text" class="bv-off-screen">',(o=n.offscreenText)?o=o.call(t,{hash:{}}):(o=t.offscreenText,o=typeof o===u?o.apply(t):o),s+=a(o)+'</span> <div class="bv-compat"> <ol></ol> <div></div> </div> </div> ',s});return t.registerPartial("deeplink",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("bv/ugc/deeplink",["ENV","framework/bview","underscore","bv/ui-core/modestbox","jquery","mf!bv/c2013/messages/common","hbs!deeplink","util/url","framework/util/bvtracker"],function(t,n,r,i,s,o,u,a,f){function c(){var e=t.get("config"),n=a.getParam(window.location.href,"bvnotificationId")||a.getBvParam(window.location.href,"notificationId"),r=a.getParam(window.location.href,"bvmessageType")||a.getBvParam(window.location.href,"messageType"),i=a.getParam(window.location.href,"bvrecipientDomain")||a.getBvParam(window.location.href,"recipientDomain"),s=t.get("appRouter").clientAPIConfig.campaignId||"";f.feature({type:"Shown",name:"DeepLink",bvProduct:"RatingsAndReviews",notificationId:n,messageType:r,recipientDomain:i,userLocale:e.locale,campaignId:s})}var l;return l={lightbox:function(t){s(function(){var n=i.get("lightbox");n.opened&&n.close(),n.open({view:t,beforeShow:function(t){var n=t.box.$layerList,i=o.deeplinkModalTitle(),a=s(u({offscreenText:i}));n.append(a),t.$target.appendTo(a.find("ol")),r(this.views).invoke("formSetup")},afterShow:function(){c()}})})}},l}),BV.define("bv/c2013/_extensions/collection/contentItemCollectionExt",["underscore","bv/api/fetch","ENV"],function(e,t,n){var r=50;return{searchContentExt:function(i,s,o,u,a){e(i).isObject()&&(i=this._normalizeSubmissionResponse(i).Title||"");var f=this,l=n.get("config").page.size,c=n.get("config").page.size2n;f.subjectId=u.Id;var h=e(a).map(function(n){var s=t.get(t.nouns(n)).include(["Authors","Products"]).withStatsOn(f.getStatsTypes()).limit(r).searchFor(i);return t.Nouns(n)==="Questions"&&(s=s.include(["Answers"])),!e(u).isUndefined()&&!e(u.Id).isUndefined()&&(t.nouns(u.Type)===t.nouns(n)?s=s.filterBy("id",u.Id):s=s.forProduct(u.Id)),s}),p={Results:[],TotalResults:0},d=t.get("batch",null,h);try{d.fetch("search").done(function(n){var r=[],s=0;e(a).forEach(function(i){var o=e(n).find(function(e){return e.contentType===t.nouns(i)}),u=o.data.Results;r=Array.prototype.concat(r,u),s+=o.data.TotalResults}),r=e(r).sortBy(function(e){return-1*new Date(e.SubmissionTime)}),p={Results:r,TotalResults:s},f.trigger("newresulttotal",p.TotalResults),f.getTopModel().trigger("searchresponse",p,i)})}catch(v){f.trigger("newresulttotal",p.TotalResults),f.getTopModel().trigger("searchresponse",p,i)}},requestRelatedExt:function(n,i,s,o){var u=this;u.subjectId=o.Id;var a=t.get("relatedquestions").include(["Products"]).limit(r).withUser(n._user).withContentType(n._contentType).withSubject(t.Noun(o.Type)+"id",o.Id),f={Results:[],TotalResults:0};try{a.fetch("relatedquestions").done(function(t){var n=e(t.Results).map(function(e){return e.contentType="Questions",e.secondaryContentType="Answers",e.subjectType="Products",e.isRelatedQuestion=!0,e});f={Results:n,TotalResults:t.TotalResults},u.trigger("newresulttotal",f.TotalResults),u.getTopModel().trigger("requestresponse",f,null)})}catch(l){u.trigger("newresulttotal",f.TotalResults),u.getTopModel().trigger("requestresponse",f,null)}},fakeSubmissionSchemaExt:function(e){var n=e.get("secondaryContentType");if(!n)return;var r={Answers:{answertext:{enabled:!0,schema:{Id:"answertext",Label:null,Default:null,MaxLength:1e4,MinLength:0,Options:[],Required:!0,Type:"TextAreaInput",Value:null}}}};e.set({formFields:r[n],shortForm:!0}),e.set(t.noun(n),!0)},fetchPageFromAuthorExt:function(r,i,s){var o=this,u=t.nouns(o.contentType),a={Reviews:"Comments",Questions:"Answers",Answers:"Questions"},f=n.get("config"),l=f.page.details[t.noun(u)]||f.page,c=l.size,h=l.size2n,p=r>1?h:c,d=r>1?c+h*(r-2):0,v=t.get(u).include(["Products","Authors"]).limit(p).offset(d).filterBy("AuthorId",s.Id),m=a[t.Nouns(u)];if(!e(m).isEmpty()){var g=f.page.details[t.noun(m)]||f.page,y=g.size;v=v.include([m]).limitIncluded(m).to(y)}var b={Results:[],TotalResults:0};try{var w=this._getChecksum();v.fetch("author"+u,w).done(function(e){var t=e.data.Results;b={Results:t,TotalResults:e.data.TotalResults},o.reset(b.Results)})}catch(E){o.reset(b.Results)}}}}),BV.define("bv/c2013/_extensions/view/contentItemExt",["ENV","jquery","underscore","bv/ui-core/modestbox","bv/util/contentType","bv/util/loadingOverlay","bv/util/productInfo","mf!bv/c2013/messages/submission","mf!bv/c2013/messages/postSubmission","framework/util/bvtracker"],function(t,n,r,i,s,o,u,a,f,l){return{showSecondaryContentExt:function(t,n){var r,i,o,a=this.getTopModel(),f=this.model.get("isRelatedQuestion")||!1;if(this.hasSubmissionView&&f)return;l.feature({type:"Used",name:"Click",detail1:"SecondaryContentLink",detail2:this.model.get("AuthorId"),contentType:this.model.get("Type"),contentId:this.model.get("Id"),bvProduct:u.getType(this),productId:u.getId(this),categoryId:u.getCategoryId(this)}),this.model.set("isSearchResult",!1),r=this.model.clone(),r.set("lazyLoad",!1),i=new t({model:r,tag:"div"});if(this._shouldShowMbWithoutSubmission(n)){this._showModestBox([i]);return}this.model.set("hasSubmission",!0),o=this._getSecondarySubmissionConfig(i),a.trigger("show"+s.noun(this.secondaryContentType)+"submission",o)},onShow:function(t,n){var r=this,i=[t],s=this.model.get("isRelatedQuestion")||!1;return n&&(n.inline&&(i=[]),n.classList.push("fake-inline"),this.contentType==="Answers"&&(n.classList.push("content-item-avatar-offset"),n.classList.push(t.hasFeature("socialAvatar")?"content-item-avatar-offset-on":"content-item-avatar-offset-off"))),i.push(n),r.hasSubmissionView=!0,s?r._showAnswerRequestModule(n):r._showModestBox(i),!1},_getSecondarySubmissionConfig:function(t){var n=this,i=this.model.get("clientAPIConfig"),o=r.extend({},i,{campaignId:this.model.get("CampaignId")||i.campaignId||""});return o[s.noun(this.model.get("contentType"))+"Id"]=this.model.get("Id"),o.contentType=o.secondaryContentType||this.model.get("secondaryContentType"),delete o.secondaryContentType,o.onShow=r.bind(n.onShow,n,t),o},_showModestBox:function(n){var c=this,h,p,d,v,m,g,y,b,w,E=!1,S="lightbox",x=!1,T=r(n).last();T.name==="submission"&&(E=!0,S=T.ModestBoxId||S,x=T.inline||x),h=t.get("config").siteAuth[s.noun(this.secondaryContentType)]&&!E,p=i.get(S),d=s.noun(this.model.get("secondaryContentType")),p.opened&&p.inline&&p.close(),v="ariaTitle_"+d,m=a["submit_"+d],g=E?m():"",y=f[v]?f[v]():g,b=["secondary-content","content-submission-"+d],h&&b.push("need-user"),w={title:g,views:n,overElement:this.$viewEl,container:this.$viewEl,classList:b,contentType:d,inline:x,autoFocus:!1,showCloseButton:!0,ariaTitle:y,showBreadcrumb:E,beforeShow:function(){r(this.views).forEach(function(n){n.formSetup({shortForm:!0});if(E)return;if(n.contentType!=="Questions")return;n.model.get("TotalSecondaryContentCount")>=t.get("config").submission.maxAnswers&&n.$viewEl.append('<div class="bv-question-closed">'+c.msgpacks[0].questionCloseForAnswers()+"</div>")}),r(this.views).invoke("trigger","viewready")},afterShow:function(){r(this.views).invoke("trigger","viewshown")},afterHide:function(){r(this.views).each(function(t){t.cleanup&&t.cleanup()}),l.feature({type:"Used",name:"Close",detail1:"SecondaryContent",detail2:"Lightbox",bvProduct:u.getType(c),productId:u.getId(c),categoryId:u.getCategoryId(c)})}},E&&p.once("beforeClose",function(){T.trigger("viewclosed")}),p.pushOrOpen(w),o.hide()},_showAnswerRequestModule:function(t){var n=this,i,s;t.model.subscribe("contentSubmitted",function(t){n._remove({view:n,message:f.contentSubmitted_answer()})},n),o.hide(),i=n.$viewEl.find(".bv-secondary-submission textarea").val(),t.setElement(n.$viewEl.find(".bv-secondary-submission").empty()),t.render(),s=[n,t],r(s).forEach(function(t){t.formSetup()}),t.$viewEl.find(".bv-fieldset-inner textarea").focus().val(i)},_shouldShowMbWithoutSubmission:function(n){var i=this.getTopModel();if(this.model.get("IsSyndicated"))return!0;if(this.contentType==="Questions"){if(!this.hasFeature("secondarySubmission"))return!0;if(this.model.get("Answers")&&this.model.get("Answers").Statistics.TotalCount>=t.get("config").submission.maxAnswers)return!0}return!this.model.get("clientAPIConfig").userToken&&t.get("config").siteAuth[s.noun(this.secondaryContentType)]&&n?!0:this.getComponent().componentId!=="contentSearch1"||!r(i.get("submissionContentTypes")).isEmpty()&&r(i.get("submissionContentTypes")).indexOf(this.secondaryContentType)!==-1?!1:!0}}}),BV.define("bv/c2013/_extensions/model/searchContentListExt",["ENV","underscore","bv/util/pageInfo","bv/util/contentType","jquery"],function(e,t,n,r,i){return{getRequestFn:function(e){var t={Products:this.getContent().searchContent,Reviews:this.getContent().requestRelated,Questions:this.getContent().searchContent,Answers:this.getContent().requestRelated};return t[r.Nouns(e)]},getProcessFn:function(e){var t={Products:this.processContentSearch,Reviews:this.processRelatedQuestions,Questions:this.processContentSearch,Answers:this.processRelatedQuestions};return t[r.Nouns(e)]},fetchPageExt:function(e){var t=this.get("requestModule"),n=this.get("keyword");if(!n&&t!=="answerRequestModule")return;this.componentSet("currentPage",e);var r=this.get("fetchResults"),i={Results:r,TotalResults:r.length};this.getProcessFn(this.get("contentType")).apply(this,[i,n])},_processContent:function(n,r){var i=this.get("requestModule"),s=i==="answerRequestModule"?e.get("config").submission.questionsPageSize:e.get("config").page.size,o=this.componentGet("currentPage")||1,u=s,a=o>1?s*(o-1):0,f=Array.prototype.slice.apply(n.Results,[a,a+u]),l=this._createSearchDisplay(f,r),c=this.updatePageInfo(f);this.set("_noResults",t(l).isEmpty()),this.getContent().reset(l,r),this.trigger("pageinfo",c),this.dataReady(n)},_createSearchDisplay:function(e,n){var s=this;return t(e).map(function(e){e.contentType=r.Nouns(e.Type),e.secondaryContentType=r.Nouns(e.SecondaryType),e.subjectType=s.get("subjectType")||r.Nouns(e.Reference.Type),e.isSearchResult=!0;if(e.Text){var t=e.Text,o=Math.max(t.indexOf(n),0),u=t.length,a=!0,f=!0;o>20?(o-=20,a=!1):o=0,u>o+100&&(u=o+100,f=!1);var l=t.substring(o,u),c=a?0:Math.max(l.indexOf(" "),0),h=f?l.length:Math.min(l.lastIndexOf(" "),l.length);l=i.trim(l.substring(c,h)),a=c===0,a||(l="..."+l),f||(l+="..."),e.shortText=l}return e})},updatePageInfo:function(t){var r=e.get("config").submission.questionsPageSize,i=this.componentGet("currentPage")||1,s=this.get("requestModule")==="answerRequestModule"?n(undefined,i,t.length,this.get("TotalResults"),r):n(undefined,i,t.length,this.get("TotalResults"));return this.set({TotalResults:this.get("TotalResults"),numPages:s.numPages,startResult:s.start},{silent:!0}),s},fetchContent:function(e,t,n){var i=this.getContent(),s=this.get("clientAPIConfig"),o={Type:this.get("subjectType"),Id:s[r.noun(this.get("subjectType"))+"Id"]},u=this.get("coverageContentTypes"),a=[n,t,s,o,u];this.componentSet("currentPage",t),this.unsubscribe("pageto",this.fetchPage),this.subscribe("pageto",this.fetchPage),this.getRequestFn(e).apply(i,a)},appendRelatedQuestion:function(t,n,r){var i=this.get("fetchResults"),s=this.componentGet("currentPage")||1,o=e.get("config").submission.questionsPageSize,u=(s-1)*o+r.index,a=this.getContent(),f=s*o-1;i.splice(u,1),f<i.length&&a.add(i[f],r),this.set("TotalResults",i.length);var l=this.updatePageInfo(a.models);this.trigger("pageinfo",l)}}}),BV.define("bv/c2013/_extensions/view/searchContentListExt",["ENV","jquery","underscore","bv/ui-core/modestbox","bv/util/loadingOverlay","bv/util/productInfo","framework/util/bvtracker","bv/util/focusManager"],function(e,t,n,r,i,s,o,u){return{_focusSearchResultsBox:function(){var e=this,t=e.$viewEl.find(".bv-secondary-content-link.bv-focusable").eq(0);t&&setTimeout(function(){u.rescanFocusLayer(),u.moveFocus(t)},0)},showContentSearchExt:function(t,u){function l(e){n(e.views).invoke("formSetup",{searchFor:f,shortForm:!0})}function h(e){n(e.views).each(function(e){e.isSearchRender&&(e.isSearchRender=!1,e.$el.detach())});var t=a.model.get("coverageContentTypes"),r={Reviews:s.RATINGSANDREVIEWS,Questions:s.ASKANDANSWER};n(t).each(function(e){var t=r[e];if(!t)return;o.feature({type:"Used",name:"Close",detail1:"Search",detail2:"Lightbox",bvProduct:t,productId:s.getId(a),categoryId:s.getCategoryId(a)})})}var a=this,f=u?u.split(" "):"";this.$viewEl=this.viewEl=null,this.isSearchRender=!0;var c={keyboardEnabled:!1,classList:["narrow","search"],container:null,inline:!1,targetClass:"bv-mbox-search-content",beforeShow:l,afterShow:n.bind(a._focusSearchResultsBox,a)};a.mb=r.get(this.ModestBoxId),t||n(a.classList).indexOf("no-submission")===-1&&a.classList.push("no-submission");if(a.mb.opened){a.detach(),a.render(),a._focusSearchResultsBox(),l([a]),i.hide();return}var p;if(t&&(this.clientAPIConfig.userToken||!e.get("config").siteAuth.question)){var d=n.extend({},this.clientAPIConfig,{campaignId:"BV_PRODUCT_DISPLAY"});d.onShow=function(e){return e&&(e.classList.push("fake-inline"),a.mb.once("afterClose",h),p=n.extend({},c,{views:[a,e],beforeShow:l}),a.mb.replaceOrOpen(p)),!1},this.model.publish("submission",d)}else e.get("config").siteAuth.question&&c.classList.push("need-user"),a.mb.once("afterClose",h),p=n.extend({},c,{views:[a]}),a.mb.replaceOrOpen(p)}}}),BV.define("bv/c2013/_extensions/model/localSocialSharingExt",["bv/strings","framework/util/bvtracker","underscore"],function(e,t,n){return{fetchSocialSharingConfig:function(n,r){if(!r||r.contentType!=="review")return;var i=this;this.api.get(n.productId).fetch().done(function(e){e.socialSharingInfo?i.set("sharingConfig",e.socialSharingInfo):i._disable()}).fail(function(){i._disable(),t.error({name:e.errors.BV_LOCAL_API_FAILURE,detail1:this})})},_disable:function(){this.set("active",!1)}}}),function(e){function P(e){return e.replace(/,/g,".").replace(/[^0-9\.]/g,"")}function H(e){return parseFloat(P(e))>=10}var t,n={bridge:null,version:"0.0.0",disabled:null,outdated:null,ready:null},r={},i=0,s={},o=0,u={},a=null,f=null,l=function(){var e,t,n,r,i="ZeroClipboard.swf";if(!document.currentScript||!(r=document.currentScript.src)){var s=document.getElementsByTagName("script");if("readyState"in s[0]){for(e=s.length;e--;)if(s[e].readyState==="interactive"&&(r=s[e].src))break}else if(document.readyState==="loading")r=s[s.length-1].src;else{for(e=s.length;e--;){n=s[e].src;if(!n){t=null;break}n=n.split("#")[0].split("?")[0],n=n.slice(0,n.lastIndexOf("/")+1);if(t==null)t=n;else if(t!==n){t=null;break}}t!==null&&(r=t)}}return r&&(r=r.split("#")[0].split("?")[0],i=r.slice(0,r.lastIndexOf("/")+1)+i),i}(),c=function(){var e=/\-([a-z])/g,t=function(e,t){return t.toUpperCase()};return function(n){return n.replace(e,t)}}(),h=function(t,n){var r,i,s,o,u,a;e.getComputedStyle?r=e.getComputedStyle(t,null).getPropertyValue(n):(i=c(n),t.currentStyle?r=t.currentStyle[i]:r=t.style[i]);if(n==="cursor")if(!r||r==="auto"){s=t.tagName.toLowerCase();if(s==="a")return"pointer"}return r},p=function(t){t||(t=e.event);var n;this!==e?n=this:t.target?n=t.target:t.srcElement&&(n=t.srcElement),B.activate(n)},d=function(e,t,n){if(!e||e.nodeType!==1)return;e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent&&e.attachEvent("on"+t,n)},v=function(e,t,n){if(!e||e.nodeType!==1)return;e.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent&&e.detachEvent("on"+t,n)},m=function(e,t){if(!e||e.nodeType!==1)return e;if(e.classList)return e.classList.contains(t)||e.classList.add(t),e;if(t&&typeof t=="string"){var n=(t||"").split(/\s+/);if(e.nodeType===1)if(!e.className)e.className=t;else{var r=" "+e.className+" ",i=e.className;for(var s=0,o=n.length;s<o;s++)r.indexOf(" "+n[s]+" ")<0&&(i+=" "+n[s]);e.className=i.replace(/^\s+|\s+$/g,"")}}return e},g=function(e,t){if(!e||e.nodeType!==1)return e;if(e.classList)return e.classList.contains(t)&&e.classList.remove(t),e;if(t&&typeof t=="string"||t===undefined){var n=(t||"").split(/\s+/);if(e.nodeType===1&&e.className)if(t){var r=(" "+e.className+" ").replace(/[\n\t]/g," ");for(var i=0,s=n.length;i<s;i++)r=r.replace(" "+n[i]+" "," ");e.className=r.replace(/^\s+|\s+$/g,"")}else e.className=""}return e},y=function(){var e,t,n,r=1;return typeof document.body.getBoundingClientRect=="function"&&(e=document.body.getBoundingClientRect(),t=e.right-e.left,n=document.body.offsetWidth,r=Math.round(t/n*100)/100),r},b=function(t,n){var r={left:0,top:0,width:0,height:0,zIndex:N(n)-1};if(t.getBoundingClientRect){var i=t.getBoundingClientRect(),s,o,u;"pageXOffset"in e&&"pageYOffset"in e?(s=e.pageXOffset,o=e.pageYOffset):(u=y(),s=Math.round(document.documentElement.scrollLeft/u),o=Math.round(document.documentElement.scrollTop/u));var a=document.documentElement.clientLeft||0,f=document.documentElement.clientTop||0;r.left=i.left+s-a,r.top=i.top+o-f,r.width="width"in i?i.width:i.right-i.left,r.height="height"in i?i.height:i.bottom-i.top}return r},w=function(e,t){var n=t==null||t&&t.cacheBust===!0&&t.useNoCache===!0;return n?(e.indexOf("?")===-1?"?":"&")+"noCache="+(new Date).getTime():""},E=function(t){var n,r,i,s=[],o=[],u=[];t.trustedOrigins&&(typeof t.trustedOrigins=="string"?o.push(t.trustedOrigins):typeof t.trustedOrigins=="object"&&"length"in t.trustedOrigins&&(o=o.concat(t.trustedOrigins))),t.trustedDomains&&(typeof t.trustedDomains=="string"?o.push(t.trustedDomains):typeof t.trustedDomains=="object"&&"length"in t.trustedDomains&&(o=o.concat(t.trustedDomains)));if(o.length)for(n=0,r=o.length;n<r;n++)if(o.hasOwnProperty(n)&&o[n]&&typeof o[n]=="string"){i=L(o[n]);if(!i)continue;if(i==="*"){u=[i];break}u.push.apply(u,[i,"//"+i,e.location.protocol+"//"+i])}return u.length&&s.push("trustedOrigins="+encodeURIComponent(u.join(","))),typeof t.jsModuleId=="string"&&t.jsModuleId&&s.push("jsModuleId="+encodeURIComponent(t.jsModuleId)),s.join("&")},S=function(e,t,n){if(typeof t.indexOf=="function")return t.indexOf(e,n);var r,i=t.length;typeof n=="undefined"?n=0:n<0&&(n=i+n);for(r=n;r<i;r++)if(t.hasOwnProperty(r)&&t[r]===e)return r;return-1},x=function(e){if(typeof e=="string")throw new TypeError("ZeroClipboard doesn't accept query strings.");return e.length?e:[e]},T=function(t,n,r,i){i?e.setTimeout(function(){t.apply(n,r)},0):t.apply(n,r)},N=function(e){var t,n;return e&&(typeof e=="number"&&e>0?t=e:typeof e=="string"&&(n=parseInt(e,10))&&!isNaN(n)&&n>0&&(t=n)),t||(typeof I.zIndex=="number"&&I.zIndex>0?t=I.zIndex:typeof I.zIndex=="string"&&(n=parseInt(I.zIndex,10))&&!isNaN(n)&&n>0&&(t=n)),t||0},C=function(e,t){if(e&&t!==!1&&typeof console!="undefined"&&console&&(console.warn||console.log)){var n="`"+e+"` is deprecated. See docs for more info:\n"+"    https://github.com/zeroclipboard/zeroclipboard/blob/master/docs/instructions.md#deprecations";console.warn?console.warn(n):console.log(n)}},k=function(){var e,t,n,r,i,s,o=arguments[0]||{};for(e=1,t=arguments.length;e<t;e++)if((n=arguments[e])!=null)for(r in n)if(n.hasOwnProperty(r)){i=o[r],s=n[r];if(o===s)continue;s!==undefined&&(o[r]=s)}return o},L=function(e){if(e==null||e==="")return null;e=e.replace(/^\s+|\s+$/g,"");if(e==="")return null;var t=e.indexOf("//");e=t===-1?e:e.slice(t+2);var n=e.indexOf("/");return e=n===-1?e:t===-1||n===0?null:e.slice(0,n),e&&e.slice(-4).toLowerCase()===".swf"?null:e||null},A=function(){var e=function(e,t){var n,r,i;if(e!=null&&t[0]!=="*"){typeof e=="string"&&(e=[e]);if(typeof e=="object"&&"length"in e)for(n=0,r=e.length;n<r;n++)if(e.hasOwnProperty(n)){i=L(e[n]);if(i){if(i==="*"){t.length=0,t.push("*");break}S(i,t)===-1&&t.push(i)}}}},t={always:"always",samedomain:"sameDomain",never:"never"};return function(n,r){var i,s=r.allowScriptAccess;if(typeof s=="string"&&(i=s.toLowerCase())&&/^always|samedomain|never$/.test(i))return t[i];var o=L(r.moviePath);o===null&&(o=n);var u=[];e(r.trustedOrigins,u),e(r.trustedDomains,u);var a=u.length;if(a>0){if(a===1&&u[0]==="*")return"always";if(S(n,u)!==-1)return a===1&&n===o?"sameDomain":"always"}return"never"}}(),O=function(e){if(e==null)return[];if(Object.keys)return Object.keys(e);var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t},M=function(e){if(e)for(var t in e)e.hasOwnProperty(t)&&delete e[t];return e},_=function(){try{return document.activeElement}catch(e){}return null},D=function(){var e=!1;if(typeof n.disabled=="boolean")e=n.disabled===!1;else{if(typeof ActiveXObject=="function")try{new ActiveXObject("ShockwaveFlash.ShockwaveFlash")&&(e=!0)}catch(t){}!e&&navigator.mimeTypes["application/x-shockwave-flash"]&&(e=!0)}return e},B=function(e,t){if(!(this instanceof B))return new B(e,t);this.id=""+i++,s[this.id]={instance:this,elements:[],handlers:{}},e&&this.clip(e),typeof t!="undefined"&&(C("new ZeroClipboard(elements, options)",I.debug),B.config(t)),this.options=B.config(),typeof n.disabled!="boolean"&&(n.disabled=!D()),n.disabled===!1&&n.outdated!==!0&&n.bridge===null&&(n.outdated=!1,n.ready=!1,q())};B.prototype.setText=function(e){return e&&e!==""&&(r["text/plain"]=e,n.ready===!0&&n.bridge&&typeof n.bridge.setText=="function"?n.bridge.setText(e):n.ready=!1),this},B.prototype.setSize=function(e,t){return n.ready===!0&&n.bridge&&typeof n.bridge.setSize=="function"?n.bridge.setSize(e,t):n.ready=!1,this};var j=function(e){n.ready===!0&&n.bridge&&typeof n.bridge.setHandCursor=="function"?n.bridge.setHandCursor(e):n.ready=!1};B.prototype.destroy=function(){this.unclip(),this.off(),delete s[this.id]};var F=function(){var e,t,n,r=[],i=O(s);for(e=0,t=i.length;e<t;e++)n=s[i[e]].instance,n&&n instanceof B&&r.push(n);return r};B.version="1.3.5";var I={swfPath:l,trustedDomains:e.location.host?[e.location.host]:[],cacheBust:!0,forceHandCursor:!1,zIndex:999999999,debug:!0,title:null,autoActivate:!0,containerId:"global-zeroclipboard-html-bridge",containerClass:"global-zeroclipboard-container",flashBridgeName:"global-zeroclipboard-flash-bridge"};B.config=function(e){typeof e=="object"&&e!==null&&k(I,e);if(typeof e=="string"&&e){if(I.hasOwnProperty(e))return I[e];return}var t={};for(var n in I)I.hasOwnProperty(n)&&(typeof I[n]=="object"&&I[n]!==null?"length"in I[n]?t[n]=I[n].slice(0):t[n]=k({},I[n]):t[n]=I[n]);return t},B.destroy=function(){B.deactivate();for(var e in s)if(s.hasOwnProperty(e)&&s[e]){var t=s[e].instance;t&&typeof t.destroy=="function"&&t.destroy()}var r=R(n.bridge);r&&r.parentNode&&(r.parentNode.removeChild(r),n.ready=null,n.bridge=null)},B.activate=function(e){t&&(g(t,I.hoverClass),g(t,I.activeClass)),t=e,m(e,I.hoverClass),U();var r=I.title||e.getAttribute("title");if(r){var i=R(n.bridge);i&&i.setAttribute("title",r)}var s=I.forceHandCursor===!0||h(e,"cursor")==="pointer";j(s)},B.deactivate=function(){var e=R(n.bridge);e&&(e.style.left="0px",e.style.top="-9999px",e.removeAttribute("title")),t&&(g(t,I.hoverClass),g(t,I.activeClass),t=null)};var q=function(){var t,r,i=document.getElementById(I.containerId);if(!i){var s=B.config();s.jsModuleId=typeof a=="string"&&a||typeof f=="string"&&f||null;var o=A(e.location.host,I),u=E(s),l=I.moviePath+w(I.moviePath,I),c='      <object classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000" id="'+I.flashBridgeName+'" width="100%" height="100%">         <param name="movie" value="'+l+'"/>         <param name="allowScriptAccess" value="'+o+'"/>         <param name="scale" value="exactfit"/>         <param name="loop" value="false"/>         <param name="menu" value="false"/>         <param name="quality" value="best" />         <param name="bgcolor" value="#ffffff"/>         <param name="wmode" value="transparent"/>         <param name="flashvars" value="'+u+'"/>         <embed src="'+l+'"           loop="false" menu="false"           quality="best" bgcolor="#ffffff"           width="100%" height="100%"           name="'+I.flashBridgeName+'"           allowScriptAccess="'+o+'"           allowFullScreen="false"           type="application/x-shockwave-flash"           wmode="transparent"           pluginspage="http://www.macromedia.com/go/getflashplayer"           flashvars="'+u+'"           scale="exactfit">         </embed>       </object>';i=document.createElement("div"),i.id=I.containerId,i.setAttribute("class",I.containerClass),i.style.position="absolute",i.style.left="0px",i.style.top="-9999px",i.style.width="15px",i.style.height="15px",i.style.zIndex=""+N(I.zIndex),document.body.appendChild(i),i.innerHTML=c}t=document[I.flashBridgeName],t&&(r=t.length)&&(t=t[r-1]),n.bridge=t||i.children[0].lastElementChild},R=function(e){var t=/^OBJECT|EMBED$/,n=e&&e.parentNode;while(n&&t.test(n.nodeName)&&n.parentNode)n=n.parentNode;return n||null},U=function(){if(t){var e=b(t,I.zIndex),r=R(n.bridge);r&&(r.style.top=e.top+"px",r.style.left=e.left+"px",r.style.width=e.width+"px",r.style.height=e.height+"px",r.style.zIndex=e.zIndex+1),n.ready===!0&&n.bridge&&typeof n.bridge.setSize=="function"?n.bridge.setSize(e.width,e.height):n.ready=!1}return this};B.prototype.on=function(e,t){var r,i,o,u={},a=s[this.id]&&s[this.id].handlers;if(typeof e=="string"&&e)o=e.toLowerCase().split(/\s+/);else if(typeof e=="object"&&e&&typeof t=="undefined")for(r in e)e.hasOwnProperty(r)&&typeof r=="string"&&r&&typeof e[r]=="function"&&this.on(r,e[r]);if(o&&o.length){for(r=0,i=o.length;r<i;r++)e=o[r].replace(/^on/,""),u[e]=!0,a[e]||(a[e]=[]),a[e].push(t);u.noflash&&n.disabled&&X.call(this,"noflash",{}),u.wrongflash&&n.outdated&&X.call(this,"wrongflash",{flashVersion:n.version}),u.load&&n.ready&&X.call(this,"load",{flashVersion:n.version})}return this},B.prototype.off=function(e,t){var n,r,i,o,u,a=s[this.id]&&s[this.id].handlers;if(arguments.length===0)o=O(a);else if(typeof e=="string"&&e)o=e.split(/\s+/);else if(typeof e=="object"&&e&&typeof t=="undefined")for(n in e)e.hasOwnProperty(n)&&typeof n=="string"&&n&&typeof e[n]=="function"&&this.off(n,e[n]);if(o&&o.length)for(n=0,r=o.length;n<r;n++){e=o[n].toLowerCase().replace(/^on/,""),u=a[e];if(u&&u.length)if(t){i=S(t,u);while(i!==-1)u.splice(i,1),i=S(t,u,i)}else a[e].length=0}return this},B.prototype.handlers=function(e){var t,n=null,r=s[this.id]&&s[this.id].handlers;if(r){if(typeof e=="string"&&e)return r[e]?r[e].slice(0):null;n={};for(t in r)r.hasOwnProperty(t)&&r[t]&&(n[t]=r[t].slice(0))}return n};var z=function(t,n,r,i){var o=s[this.id]&&s[this.id].handlers[t];if(o&&o.length){var u,a,f,l=n||this;for(u=0,a=o.length;u<a;u++)f=o[u],n=l,typeof f=="string"&&typeof e[f]=="function"&&(f=e[f]),typeof f=="object"&&f&&typeof f.handleEvent=="function"&&(n=f,f=f.handleEvent),typeof f=="function"&&T(f,n,r,i)}return this};B.prototype.clip=function(e){e=x(e);for(var t=0;t<e.length;t++)if(e.hasOwnProperty(t)&&e[t]&&e[t].nodeType===1){e[t].zcClippingId?S(this.id,u[e[t].zcClippingId])===-1&&u[e[t].zcClippingId].push(this.id):(e[t].zcClippingId="zcClippingId_"+o++,u[e[t].zcClippingId]=[this.id],I.autoActivate===!0&&d(e[t],"mouseover",p));var n=s[this.id].elements;S(e[t],n)===-1&&n.push(e[t])}return this},B.prototype.unclip=function(e){var t=s[this.id];if(t){var n=t.elements,r;typeof e=="undefined"?e=n.slice(0):e=x(e);for(var i=e.length;i--;)if(e.hasOwnProperty(i)&&e[i]&&e[i].nodeType===1){r=0;while((r=S(e[i],n,r))!==-1)n.splice(r,1);var o=u[e[i].zcClippingId];if(o){r=0;while((r=S(this.id,o,r))!==-1)o.splice(r,1);o.length===0&&(I.autoActivate===!0&&v(e[i],"mouseover",p),delete e[i].zcClippingId)}}}return this},B.prototype.elements=function(){var e=s[this.id];return e&&e.elements?e.elements.slice(0):[]};var W=function(e){var t,n,r,i,o,a=[];if(e&&e.nodeType===1&&(t=e.zcClippingId)&&u.hasOwnProperty(t)){n=u[t];if(n&&n.length)for(r=0,i=n.length;r<i;r++)o=s[n[r]].instance,o&&o instanceof B&&a.push(o)}return a};I.hoverClass="zeroclipboard-is-hover",I.activeClass="zeroclipboard-is-active",I.trustedOrigins=null,I.allowScriptAccess=null,I.useNoCache=!0,I.moviePath="ZeroClipboard.swf",B.detectFlashSupport=function(){return C("ZeroClipboard.detectFlashSupport",I.debug),D()},B.dispatch=function(e,n){if(typeof e=="string"&&e){var r=e.toLowerCase().replace(/^on/,"");if(r){var i=t&&I.autoActivate===!0?W(t):F();for(var s=0,o=i.length;s<o;s++)X.call(i[s],r,n)}}},B.prototype.setHandCursor=function(e){return C("ZeroClipboard.prototype.setHandCursor",I.debug),e=typeof e=="boolean"?e:!!e,j(e),I.forceHandCursor=e,this},B.prototype.reposition=function(){return C("ZeroClipboard.prototype.reposition",I.debug),U()},B.prototype.receiveEvent=function(e,t){C("ZeroClipboard.prototype.receiveEvent",I.debug);if(typeof e=="string"&&e){var n=e.toLowerCase().replace(/^on/,"");n&&X.call(this,n,t)}},B.prototype.setCurrent=function(e){return C("ZeroClipboard.prototype.setCurrent",I.debug),B.activate(e),this},B.prototype.resetBridge=function(){return C("ZeroClipboard.prototype.resetBridge",I.debug),B.deactivate(),this},B.prototype.setTitle=function(e){C("ZeroClipboard.prototype.setTitle",I.debug),e=e||I.title||t&&t.getAttribute("title");if(e){var r=R(n.bridge);r&&r.setAttribute("title",e)}return this},B.setDefaults=function(e){C("ZeroClipboard.setDefaults",I.debug),B.config(e)},B.prototype.addEventListener=function(e,t){return C("ZeroClipboard.prototype.addEventListener",I.debug),this.on(e,t)},B.prototype.removeEventListener=function(e,t){return C("ZeroClipboard.prototype.removeEventListener",I.debug),this.off(e,t)},B.prototype.ready=function(){return C("ZeroClipboard.prototype.ready",I.debug),n.ready===!0};var X=function(e,i){e=e.toLowerCase().replace(/^on/,"");var s=i&&i.flashVersion&&P(i.flashVersion)||null,o=t,u=!0;switch(e){case"load":if(s){if(!H(s)){X.call(this,"onWrongFlash",{flashVersion:s});return}n.outdated=!1,n.ready=!0,n.version=s}break;case"wrongflash":s&&!H(s)&&(n.outdated=!0,n.ready=!1,n.version=s);break;case"mouseover":m(o,I.hoverClass);break;case"mouseout":I.autoActivate===!0&&B.deactivate();break;case"mousedown":m(o,I.activeClass);break;case"mouseup":g(o,I.activeClass);break;case"datarequested":if(o){var a=o.getAttribute("data-clipboard-target"),f=a?document.getElementById(a):null;if(f){var l=f.value||f.textContent||f.innerText;l&&this.setText(l)}else{var c=o.getAttribute("data-clipboard-text");c&&this.setText(c)}}u=!1;break;case"complete":M(r),o&&o!==_()&&o.focus&&o.focus()}var h=o,p=[this,i];return z.call(this,e,h,p,u)};typeof BV.define=="function"&&BV.define.amd?BV.define("vendor/BVZeroClipboard",["require","exports","module"],function(e,t,n){return a=n&&n.id||null,B}):typeof module=="object"&&module&&typeof module.exports=="object"&&module.exports&&typeof e.require=="function"?(f=module.id||null,module.exports=B):e.ZeroClipboard=B}(function(){return this}()),BV.define("bv/c2013/_extensions/view/localSocialSharingExt",["ENV","framework/bview","framework/util/bvtracker","bv/util/productInfo","jquery","underscore","vendor/json2","vendor/BVZeroClipboard"],function(e,t,n,r,i,s,o,u){var a=function(){var e=this.model.get("sharingConfig").link;n.feature({type:"Used",name:"BV Local Social Sharing",detail1:"Clicked",detail2:e,bvProduct:r.getType(this),productId:r.getId(this),interaction:!0,categoryId:r.getCategoryId(this)}),window.location.href=e},f=function(){u.destroy(),this.model.set("nonFlash",!0),this.listenToOnce(this.model,"change:sharingConfig",function(e,t){var n=t.flashDisabledValues;if(!n){e._disable();return}s.each(n,function(e,n){t[n]=e}),e.set("sharingConfig",t,{silent:!0})})};return{_init:function(){var t=e.get("config");u.config({debug:t.workspace!=="production",zIndex:2000008031,moviePath:t.utilPath+"zeroclipboard/1.3.5/BVZeroClipboard.swf",swfPath:t.utilPath+"zeroclipboard/1.3.5/BVZeroClipboard.swf",allowScriptAccess:"always",containerId:"bv-zeroclipboard-html-bridge",containerClass:"bv-zeroclipboard-container",flashBridgeName:"bv-zeroclipboard-flash-bridge"});var n=new u;n.on("noflash wrongflash",s.bind(f,this)),this.model.set("clip",n),this.listenToOnce(this.model,"change:reviewContent",this._display),this.listenToOnce(this.model,"change:sharingConfig",this._display),this.listenToOnce(this.model,"change:active",this._display)},render:i.noop,renderAsync:i.noop,_display:function(){this.model.get("active")?this.model.get("reviewContent")&&this.model.get("sharingConfig")&&this._renderComponent():this._remove()},_renderComponent:s.once(function(){t.prototype.render.apply(this,arguments),this._initZeroClipboard();var e=this;s.delay(function(){e.$el.find("#bv-response-preview").select()},500),n.feature({type:"Used",name:"BV Local Social Sharing",detail1:"Rendered",detail2:o.stringify(this.model.get("sharingConfig")),bvProduct:r.getType(this),productId:r.getId(this),categoryId:r.getCategoryId(this)})}),_initZeroClipboard:function(){var e=this.$el.find(".bv-local-social-sharing-button"),t=s.bind(a,this);if(this.model.get("nonFlash")){e.on("click",t);return}this.model.has("clip")&&(this.model.get("clip").destroy(),this.model.unset("clip"));var n=new u(e);n.on("complete",t),this.model.set("clip",n)},_remove:function(){this.model.unset("clip"),u.destroy(),this.$viewEl&&(this.$viewEl.remove(),this.$viewEl=this.viewEl=null)}}}),BV.define("hbs!activeContentFilters",["hbs","vendor/handlebars/runtime","template/helpers/view","template/helpers/extmsg","template/helpers/renderIcon"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){var r="",i,s,o;r+=' <span class="bv-active-filters-list-title">',o={hash:{prefix:"content_filter_"}},r+=l((i=n.extmsg,i?i.call(e,"list_title",e.msgpack,e,o):h.call(e,"extmsg","list_title",e.msgpack,e,o)))+'</span> <ul class="bv-active-filters-list"> ',s=n.each.call(e,e.filters,{hash:{},inverse:c.noop,fn:c.programWithDepth(d,t,e)});if(s||s===0)r+=s;r+=' <li class="bv-active-filters-list-item"> <button type="button" class="bv-active-filter-button-clear bv-focusable"> <span aria-hidden="true">',o={hash:{prefix:"content_filter_"}},r+=l((i=n.extmsg,i?i.call(e,"button_clear",e.msgpack,e,o):h.call(e,"extmsg","button_clear",e.msgpack,e,o)))+"</span> ",o={hash:{classList:"bv-close-icon",ariaHidden:"true"},inverse:c.noop,fn:c.program(8,y,t)},s=(i=n.renderIcon,i?i.call(e,"bv-sprite-close",o):h.call(e,"renderIcon","bv-sprite-close",o));if(s||s===0)r+=s;return r+=' <span class="bv-off-screen">',o={hash:{prefix:"offscreen_"}},r+=l((i=n.extmsg,i?i.call(e,"clear_all_filters",e.msgpack,e,o):h.call(e,"extmsg","clear_all_filters",e.msgpack,e,o)))+"</span> </button> </li> </ul> ",r}function d(e,t,r){var i="",s;i+=' <li data-bv-filter-headerid="',(s=n.id)?s=s.call(e,{hash:{}}):(s=e.id,s=typeof s===f?s.apply(e):s),i+=l(s)+'" class="bv-active-filters-list-item ',s=n.unless.call(e,e.enabled,{hash:{},inverse:c.noop,fn:c.program(3,v,t)});if(s||s===0)i+=s;i+='"> ',s=n.each.call(e,e.options,{hash:{},inverse:c.noop,fn:c.programWithDepth(m,t,e,r)});if(s||s===0)i+=s;return i+=" </li> ",i}function v(e,t){return"bv-filter-hidden"}function m(e,t,r,i){var s="",o,u,a;s+=' <button type="button" data-bv-filter-option="',(o=n.id)?o=o.call(e,{hash:{}}):(o=e.id,o=typeof o===f?o.apply(e):o),s+=l(o)+'" class="bv-active-filter-button bv-focusable ',o=n.unless.call(e,e.enabled,{hash:{},inverse:c.noop,fn:c.program(6,g,t)});if(o||o===0)s+=o;s+='" title="'+l((o=r.label,typeof o===f?o.apply(e):o))+'"> ',(u=n.label)?u=u.call(e,{hash:{}}):(u=e.label,u=typeof u===f?u.apply(e):u),s+=l(u)+" ",a={hash:{classList:"bv-close-icon",ariaHidden:"true"},inverse:c.noop,fn:c.program(8,y,t)},u=(o=n.renderIcon,o?o.call(e,"bv-sprite-close",a):h.call(e,"renderIcon","bv-sprite-close",a));if(u||u===0)s+=u;return s+=' <span class="bv-off-screen">',a={hash:{prefix:"offscreen_"}},s+=l((o=n.extmsg,o?o.call(e,"remove_filter",i.msgpack,i,a):h.call(e,"extmsg","remove_filter",i.msgpack,i,a)))+"</span> </button> ",s}function g(e,t){return"bv-hidden"}function y(e,t){return" &#x2718; "}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f="function",l=this.escapeExpression,c=this,h=n.helperMissing;a={hash:{tag:"div",classList:"active-filters hidden"},inverse:c.noop,fn:c.program(1,p,i)},u=(o=n.view,o?o.call(t,t,a):h.call(t,"view",t,a));if(u||u===0)s+=u;return s+=" ",s});return t.registerPartial("activeContentFilters",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("bv/c2013/view/contentFilterActiveFilters",["framework/bview","framework/util/bvtracker","bv/util/focusManager","bv/util/productInfo","jquery","underscore","hbs!activeContentFilters","mf!bv/c2013/messages/contentFilter"],function(e,t,n,r,i,s,o,u){var a=e.extend({name:"ActiveFilters",template:o,events:{"click button[data-bv-filter-option]":"clearOption","click button.bv-active-filter-button-clear":"clearAllFilters"},msgpacks:[u],init:function(){s(this.model.filters).each(function(e){this.listenTo(e,{filterOptionChanged:this.filterOptionChanged})},this),this.listenTo(this.model,{filterEnabledChanged:this.filterOptionChanged,allFiltersCleared:this._checkVisibility})},filterOptionChanged:function(){this._checkVisibility()},attach:function(){e.prototype.attach.apply(this,arguments),this._checkVisibility()},clearOption:function(e){var n=i(e.target).closest("[data-bv-filter-option]"),s=n.data("bv-filter-option"),o=parseInt(s,10)>1?s+"Stars":s+"Star",u=n.closest("[data-bv-filter-headerid]"),a=u.data("bv-filter-headerid"),f=this.model.getFilter(a),l=f.getOption(s);l.set("enabled",!1),t.feature({type:"Used",name:"Filter",detail1:"Clear"+o,bvProduct:r.getType(this),productId:r.getId(this),categoryId:r.getCategoryId(this)})},clearAllFilters:function(){this.model.clearAllFilters(),n.focusPrev(),t.feature({type:"Used",name:"Filter",detail1:"ClearAll",bvProduct:r.getType(this),productId:r.getId(this),categoryId:r.getCategoryId(this)})},_checkVisibility:function(){if(this.isBeforeAttach())return;var e=this.model.activeFilters.length===0;return e||this._checkFilterVisibility(),this.$viewEl.toggleClass("bv-hidden",e),!e},_checkFilterVisibility:function(){s(this.model.filters).each(function(e){var t=0,n=this._getViewForFilter(e);s(e.options).each(function(e){var r=this._getViewForOption(e,n),i=e.get("enabled");i&&++t,r.toggleClass("bv-hidden",!i)},this),n.toggleClass("bv-hidden",t===0)},this)},_getViewForFilter:function(e){return this.$('[data-bv-filter-headerid="'+e.id+'"]')},_getViewForOption:function(e,t){return t.find('[data-bv-filter-option="'+e.id+'"]')},isBeforeAttach:function(){return!this.$viewEl||!this.$viewEl.length}});return a}),BV.define("hbs!contentFilterDropdown",["hbs","vendor/handlebars/runtime","template/helpers/view"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){var r="",i;r+=' <!--[if lt IE 7]> <div class="bv-compat bvie6 bvie-lt8 bvie"> <![endif]--> <!--[if IE 7]> <div class="bv-compat bvie7 bvie-lt8 bvie"> <![endif]--> <!--[if IE 8]> <div class="bv-compat bvie8 bvie"> <![endif]--> <!--[if IE 9]> <div class="bv-compat bvie9 bvie"> <![endif]--> <!--[if gt IE 9]> <!--><div class="bv-compat"> <!--<![endif]--> <div class="bv-dropdown bv-filter-dropdown"> <ul id="bv-content-filter-dropdown-',(i=n.id)?i=i.call(e,{hash:{}}):(i=e.id,i=typeof i===l?i.apply(e):i),r+=c(i)+'" role="menu"> ',i=n.each.call(e,e.options,{hash:{},inverse:f.noop,fn:f.program(2,d,t)});if(i||i===0)r+=i;return r+=" </ul> </div> </div> ",r}function d(e,t){var r="",i;r+=' <li class="bv-dropdown-item bv-focusable ',i=n["if"].call(e,e.enabled,{hash:{},inverse:f.noop,fn:f.program(3,v,t)});if(i||i===0)r+=i;return r+='" data-bv-dropdown-value="',(i=n.id)?i=i.call(e,{hash:{}}):(i=e.id,i=typeof i===l?i.apply(e):i),r+=c(i)+'" tabindex="0" role="menuitem"> <span>',(i=n.label)?i=i.call(e,{hash:{}}):(i=e.label,i=typeof i===l?i.apply(e):i),r+=c(i)+"</span> </li> ",r}function v(e,t){return"bv-dropdown-item-selected"}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f=this,l="function",c=this.escapeExpression,h=n.helperMissing;s+='<div class="bv-core-container-',(o=n.version)?o=o.call(t,{hash:{}}):(o=t.version,o=typeof o===l?o.apply(t):o),s+=c(o)+'"> ',a={hash:{tag:"div",classList:"select-dropdown"},inverse:f.noop,fn:f.program(1,p,i)},u=(o=n.view,o?o.call(t,t,a):h.call(t,"view",t,a));if(u||u===0)s+=u;return s+=" </div> ",s});return t.registerPartial("contentFilterDropdown",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("bv/c2013/view/contentFilterDropdown",["underscore","jquery","bv/ui-core/bselectdropdownview","bv/util/focusManager","hbs!contentFilterDropdown"],function(e,t,n,r,i){return n.extend({name:"FilterDropdown",template:i,options:e(n.prototype.options).extend({allowToggle:!0}),moveNextFocusable:!1,init:function(){},topPositionModifier:function(e){return e-5},rightPositionModifier:function(e){return e+parseInt(this.parent.$viewEl.find(".bv-dropdown-target").css("paddingRight"),10)},select:function(e){n.prototype.select.apply(this,arguments);var r=t(e.target).closest(".bv-dropdown-item");r.toggleClass("bv-dropdown-item-selected")},setupKeyboardNavigation:function(){n.prototype.setupKeyboardNavigation.apply(this,arguments);var e=this,t={9:!0,38:!0,40:!0};e.$el.off("keydown.FilterSelectView").on("keydown.FilterSelectView",function(n){var i=n.keyCode;if(!t[i])return;var s=!1;switch(i){case 9:s=!0,e.leave(n);var o=n.shiftKey?"focusPrev":"focusNext";r[o]();break;case 38:document.activeElement===e.$first[0]&&(s=!0,r.moveFocus(e.$last));break;case 40:document.activeElement===e.$last[0]&&(s=!0,r.moveFocus(e.$first))}s&&(n.preventDefault(),n.stopPropagation())})}})}),BV.define("hbs!contentFilterSelect",["hbs","vendor/handlebars/runtime","template/helpers/view","template/helpers/renderIcon","template/helpers/extmsg"],function(e,t){var n=t.template(function(e,t,n,r,i){function p(e,t){var r="",i;r+=" ",i=n["if"].call(e,e.isMobile,{hash:{},inverse:h.program(5,m,t),fn:h.program(2,d,t)});if(i||i===0)r+=i;return r+=" ",r}function d(e,t){var r="",i,s,o;r+='  <label for="bv-dropdown-select-'+l((i=(i=e._bview,i==null||i===!1?i:i.viewId),typeof i===f?i.apply(e):i))+'" class="bv-off-screen"> ',o={hash:{}},r+=l((i=n.extmsg,i?i.call(e,"offscreen_filter_label",e.msgpack,e,o):c.call(e,"extmsg","offscreen_filter_label",e.msgpack,e,o)))+' </label> <select id="bv-dropdown-select-'+l((i=(i=e._bview,i==null||i===!1?i:i.viewId),typeof i===f?i.apply(e):i))+'" class="bv-content-filter-select-element bv-focusable" data-bv-filter-for="',(s=n.label)?s=s.call(e,{hash:{}}):(s=e.label,s=typeof s===f?s.apply(e):s),r+=l(s)+'"> '+' <option value="">',(s=n.label)?s=s.call(e,{hash:{}}):(s=e.label,s=typeof s===f?s.apply(e):s),r+=l(s)+"</option> ",s=n.each.call(e,e.options,{hash:{},inverse:h.noop,fn:h.program(3,v,t)});if(s||s===0)r+=s;return r+=" </select> ",r}function v(e,t){var r="",i;return r+=' <option value="',(i=n.id)?i=i.call(e,{hash:{}}):(i=e.id,i=typeof i===f?i.apply(e):i),r+=l(i)+'">',(i=n.label)?i=i.call(e,{hash:{}}):(i=e.label,i=typeof i===f?i.apply(e):i),r+=l(i)+"</option> ",r}function m(e,t){var r="",i,s,o;r+=' <div class="bv-dropdown" data-bv-filter-for="',(i=n.label)?i=i.call(e,{hash:{}}):(i=e.label,i=typeof i===f?i.apply(e):i),r+=l(i)+'"> <div class="bv-dropdown-target"> <button type="button" class="bv-focusable" role="menuitem" aria-haspopup="true" aria-owns="bv-content-filter-dropdown-',(i=n.id)?i=i.call(e,{hash:{}}):(i=e.id,i=typeof i===f?i.apply(e):i),r+=l(i)+'"> ',o={hash:{classList:"bv-dropdown-arrow",ariaHidden:"true",role:"presentation"},inverse:h.noop,fn:h.program(6,g,t)},s=(i=n.renderIcon,i?i.call(e,"bv-sprite-down-arrow-dark",o):c.call(e,"renderIcon","bv-sprite-down-arrow-dark",o));if(s||s===0)r+=s;r+=' <span class="bv-dropdown-title">',(s=n.label)?s=s.call(e,{hash:{}}):(s=e.label,s=typeof s===f?s.apply(e):s),r+=l(s)+'</span> <span class="bv-off-screen">',o={hash:{}},r+=l((i=n.extmsg,i?i.call(e,"offscreen_sort_arrow",e.msgpack,e,o):c.call(e,"extmsg","offscreen_sort_arrow",e.msgpack,e,o)))+'</span> </button> </div> <label for="bv-dropdown-select-'+l((i=(i=e._bview,i==null||i===!1?i:i.viewId),typeof i===f?i.apply(e):i))+'" class="bv-off-screen"> ',o={hash:{}},r+=l((i=n.extmsg,i?i.call(e,"offscreen_filter_label",e.msgpack,e,o):c.call(e,"extmsg","offscreen_filter_label",e.msgpack,e,o)))+' </label> <select id="bv-dropdown-select-'+l((i=(i=e._bview,i==null||i===!1?i:i.viewId),typeof i===f?i.apply(e):i))+'" class="bv-select-cleanslate bv-dropdown-select"> ',s=n.each.call(e,e.options,{hash:{},inverse:h.noop,fn:h.program(8,y,t)});if(s||s===0)r+=s;return r+=" </select> </div> ",r}function g(e,t){return" &#x25BC; "}function y(e,t){var r="",i;return r+=' <option value="',(i=n.id)?i=i.call(e,{hash:{}}):(i=e.id,i=typeof i===f?i.apply(e):i),r+=l(i)+'"> ',(i=n.label)?i=i.call(e,{hash:{}}):(i=e.label,i=typeof i===f?i.apply(e):i),r+=l(i)+" </option> ",r}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f="function",l=this.escapeExpression,c=n.helperMissing,h=this;s+=" ",a={hash:{tag:"div",classList:"content-filter-select"},inverse:h.noop,fn:h.program(1,p,i)},u=(o=n.view,o?o.call(t,t,a):c.call(t,"view",t,a));if(u||u===0)s+=u;return s+=" ",s});return t.registerPartial("contentFilterSelect",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("bv/c2013/view/contentFilterSelect",["jquery","underscore","bv/ui-core/bselectview","bv/c2013/view/contentFilterDropdown","bv/util/focusManager","hbs!contentFilterSelect"],function(e,t,n,r,i,s){return n.extend({name:"FilterSelect",events:{"change select":"change"},TargetView:r,template:s,change:function(t){var n=e(t.target),r=n.val(),i;if(r==="")return;i=this.model.getOption(r),i.toggleEnabled(),n.val("")},enter:function(){if(this.model.get("isMobile"))return;return n.prototype.enter.apply(this,arguments)},setupKeyboardNavigation:function(n){var r=this,s={9:!0,13:!0,27:!0,32:!0,38:!0,40:!0};n=n||this.$targetEl,n.off("keydown.FilterSelectView").on("keydown.FilterSelectView",function(n){function h(e){r.targetViewOptions=t.extend({},r.targetViewOptions,{startDelay:0}),r.enter(e),u=r.targetView}function p(e){r.leave(e),r.deregisterFocusableLayer()}var o=n.keyCode,u,a,f=!1,l,c;if(!s[o])return;u=r.targetView,a=u?u.rendered:!1;switch(o){case 9:f=!0,a&&p(n),l=n.shiftKey?"focusPrev":"focusNext",i[l]();break;case 13:case 32:f=!0,a?p(n):h(n);break;case 27:f=!0,p(n);break;case 40:f=!0,a||h(n),c=setInterval(function(){e.contains(document,u.$first[0])&&(r.moveFocus(u.$first),clearInterval(c))},20)}f&&(n.preventDefault(),n.stopPropagation())})},setText:e.noop})}),BV.define("hbs!contentFilterControls",["hbs","vendor/handlebars/runtime","template/helpers/view","template/helpers/subviews"],function(e,t){var n=t.template(function(e,t,n,r,i){function h(e,t){var r="",i,s;r+=' <div class="bv-filters ',i=n.unless.call(e,e.expanded,{hash:{},inverse:f.noop,fn:f.program(2,p,t)});if(i||i===0)r+=i;return r+='" role="menubar"> ',s={hash:{}},r+=c((i=n.subviews,i?i.call(e,e,s):l.call(e,"subviews",e,s)))+" </div> ",r}function p(e,t){return"bv-hidden"}this.compilerInfo=[2,">= 1.0.0-rc.3"],n=n||e.helpers;var s="",o,u,a,f=this,l=n.helperMissing,c=this.escapeExpression;a={hash:{tag:"div",classList:"filter-controls"},inverse:f.noop,fn:f.program(1,h,i)},u=(o=n.view,o?o.call(t,t,a):l.call(t,"view",t,a));if(u||u===0)s+=u;return s+=" ",s});return t.registerPartial("contentFilterControls",n),n.deps=[],n.tplMountedViews=[],n}),BV.define("bv/c2013/view/contentFilterControls",["framework/bview","bv/ui-core/focusableview","bv/c2013/view/contentFilterSelect","underscore","hbs!contentFilterControls","mf!bv/c2013/messages/contentFilter"],function(e,t,n,r,i,s){var o=e.extend({name:"FilterControls",events:{},template:i,msgpacks:[s],init:function(){this.listenTo(this.model,{"change:expanded":this.toggleExpandFilter})},createFilterViews:function(){var e=this.model.filters;return this.views||(this.views=[]),r(e).each(function(e){var t=new n({parent:this,model:e});this.views.push(t)},this),this.views},toggleExpandFilter:function(e,t){function r(e){var t=n.find(".bv-filters"),r=n.find(".bv-filters > .bv-content-filter-select"),i;e?(t.css("width","auto"),r.css("max-width","none")):(r.css("width",1),i=t.width(),t.css("width",i),r.css("width","auto").css("max-width",i))}var n=this.$viewEl;if(!n||!n.length)return;n.toggleClass("bv-hidden",!t),n.toggleClass("bv-focusable",t),t?(r(),n.attr("tabindex",-1),this.rescanFocusLayer(),this.moveFocus(n)):(r(!0),n.removeAttr("tabindex"),this.removeFocusables(n))}});return o=o.extend(t),o}),BV.define("secondary",["bv/ugc/submission","bv/ugc/authUser","bv/ugc/unsubscribeUser","bv/ugc/container","bv/ugc/deeplink","bv/ui-core/modestbox","bv/c2013/_extensions/collection/contentItemCollectionExt","bv/c2013/_extensions/view/contentItemExt","bv/c2013/_extensions/model/searchContentListExt","bv/c2013/_extensions/view/searchContentListExt","bv/c2013/_extensions/model/localSocialSharingExt","bv/c2013/_extensions/view/localSocialSharingExt","bv/c2013/view/contentFilterActiveFilters","bv/c2013/view/contentFilterControls"],function(e,t,n,r,i,s,o,u,a,f,l,c,h,p){return{Submission:e,AuthUser:t,UnsubscribeUser:n,Container:r,Deeplink:i,ModestBox:s,ContentItemCollectionExt:{collection:o},ContentItemExt:{view:u},SearchContentListExt:{model:a,view:f},LocalSocialSharingExt:{model:l,view:c},ActiveFiltersView:h,FilterControlsView:p}});