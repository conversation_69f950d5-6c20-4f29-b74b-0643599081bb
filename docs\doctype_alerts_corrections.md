# Corrections des alertes de changement de doctype

## Problèmes identifiés

1. **Alerte de confirmation incorrecte** : L'alerte de confirmation utilisait un mapping statique qui ne correspondait pas aux vraies destinations du workflow
2. **Mapping doctype incomplet** : Le mapping `places_docType` dans le contrôleur ne gérait pas correctement les destinations multiples (ex: ASSY → Assembly + Quality)
3. **Destinations non mises à jour** : Les alertes n'affichaient pas les vraies destinations basées sur les places d'origine

## Corrections apportées

### 1. Fichier `templates/js/jhess.html.twig`

#### Ajout du mapping des destinations
```javascript
// Mapping des doctypes vers leurs destinations principales
let docTypeDestinations = {
    'ASSY': 'Assemblage et Qualité',
    'MACH': 'Usinage',
    'MOLD': 'Moulage',
    'DOC':  'Qualité',
    'PUR':  'Achat (RFQ ou F30)'
};
```

#### Amélioration de l'alerte de confirmation
**Avant** :
```javascript
text: "Le document arrivera dans le département " + docTypeDisplayName[value] + "."
```

**Après** :
```javascript
text: "Vous êtes sur le point de changer le type de document vers " + (docTypeDisplayName[value] || value) + ". Le document sera déplacé vers les étapes appropriées."
```

**Avantages** :
- Message plus générique et professionnel
- Ne fait pas de promesse spécifique sur la destination (qui dépend de la place d'origine)
- Laisse l'alerte de succès afficher les vraies destinations

### 2. Fichier `src/Controller/DocumentController.php`

#### Correction du mapping `places_docType`
**Avant** :
```php
$places_docType = [
    'MACH' => ['ORIGINE'=> 'Machining', 'DESTINATION'=> 'Machining'],
    'MOLD' => ['ORIGINE'=> 'Molding', 'DESTINATION'=> 'Molding'],
    'ASSY' => ['ORIGINE'=> 'Assembly', 'DESTINATION'=> 'Assembly'],
    'PUR'  => ['ORIGINE'=> ['Achat_F30','Achat_RFQ','Quality'], 'DESTINATION'=> 'Quality'],
];
```

**Après** :
```php
$places_docType = [
    'MACH' => ['ORIGINE'=> 'Machining', 'DESTINATION'=> 'Machining'],
    'MOLD' => ['ORIGINE'=> 'Molding', 'DESTINATION'=> 'Molding'],
    'ASSY' => ['ORIGINE'=> 'Assembly', 'DESTINATION'=> ['Assembly', 'Quality']],
    'PUR'  => ['ORIGINE'=> ['Achat_F30','Achat_Rfq','Quality'], 'DESTINATION'=> 'Quality'],
    'DOC'  => ['ORIGINE'=> 'Quality', 'DESTINATION'=> 'Quality'],
];
```

#### Gestion des destinations multiples
**Ajout** :
```php
// Gérer les destinations multiples (comme ASSY -> Assembly + Quality)
if (is_array($DESTINATION)) {
    foreach ($DESTINATION as $dest) {
        $markingStart[$dest] = 1;
    }
} else {
    $markingStart[$DESTINATION] = 1;
}
```

#### Correction de la casse
- `Achat_RFQ` → `Achat_Rfq` (cohérence avec le workflow)

## Comportement final

### Alerte de confirmation
- **Déclenchement** : Avant l'envoi de la requête AJAX
- **Message** : Générique, ne promet pas de destination spécifique
- **Objectif** : Confirmer l'intention de l'utilisateur

### Alerte de succès
- **Déclenchement** : Après la réponse du serveur
- **Message** : Affiche les vraies destinations basées sur `currentPlaces`
- **Objectif** : Informer l'utilisateur des vraies destinations

### Exemples de messages

#### Confirmation
- "Vous êtes sur le point de changer le type de document vers Assemblage. Le document sera déplacé vers les étapes appropriées."

#### Succès
- "Doctype modifié avec succès. Le document va maintenant vers : Assemblage, Qualité"
- "Doctype modifié avec succès. Le document va maintenant vers : Usinage"
- "Doctype modifié avec succès. Le document va maintenant vers : RFQ"

## Test

Un fichier de test HTML a été créé : `tests/manual_doctype_alert_test.html`

Ce fichier permet de tester :
- Les alertes de confirmation pour tous les doctypes
- Les alertes de succès avec différentes destinations
- Le mapping des noms techniques vers les noms affichés

## Avantages des corrections

1. **Précision** : Les alertes affichent maintenant les vraies destinations
2. **Flexibilité** : Le système gère les destinations multiples (ASSY → Assembly + Quality)
3. **Cohérence** : Les noms des places sont cohérents dans tout le système
4. **Expérience utilisateur** : Messages plus clairs et informatifs
5. **Maintenabilité** : Code plus robuste et facile à maintenir
