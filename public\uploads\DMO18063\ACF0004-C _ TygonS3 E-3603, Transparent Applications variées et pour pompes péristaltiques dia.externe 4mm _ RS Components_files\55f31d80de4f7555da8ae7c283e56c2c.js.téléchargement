Bootstrapper.bindDOMLoaded(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;var getSiteMarket=function(){return Bootstrapper.data.resolve("13145")};var account_number="";switch(getSiteMarket.call(this)){case "de":account_number="********";break;case "uk":case "ie":case "za":case "dk":case "se":case "no":case "be02":case "benl":case "be01":case "befr":case "nl":case "ch":case "dech":case "at":case "hu":case "pl":case "fr":case "f1":case "it":case "es":case "pt":case "cz":account_number=
"********";break;case "au":case "nz":case "pt":case "cn":case "hk01":case "hken":case "hk02":case "hkcn":case "tw01":case "twen":case "jp":case "sg":case "my":case "ph":case "th":case "tw02":case "twcn":case "tw01":case "twen":case "kr":account_number="********";break}if(document.location.host.match(/^st1/))account_number="********";var serviceRegEx=/(id=services)|(id=tjanster)|(id=tjenester)|(id=servicios)|(id=servicos)|(id=szolgaltatasok)|(id=uslugi)|(id=sluzby)|(id=servizi)|(id=aboutRS)|(id=om-RS)|(id=openAccount)|(id=help)|(id=price)|(id=rsessentials)|(id=online-services)|(id=hub)|(file=contact)|(myLists)|(\/ma\/)/i;
var url=document.location.href;var section="";if(url.match(serviceRegEx))section="service";else section="sales";window.lpTag=window.lpTag||{};if(typeof window.lpTag._tagCount==="undefined"){window.lpTag={site:account_number,section:getSiteMarket.call(this)+","+section||"",autoStart:lpTag.autoStart===false?false:true,ovr:lpTag.ovr||{},_v:"1.6.0",_tagCount:1,protocol:"https:",events:{bind:function(app,ev,fn){lpTag.defer(function(){lpTag.events.bind(app,ev,fn)},0)},trigger:function(app,ev,json){lpTag.defer(function(){lpTag.events.trigger(app,
ev,json)},1)}},defer:function(fn,fnType){if(fnType==0){this._defB=this._defB||[];this._defB.push(fn)}else if(fnType==1){this._defT=this._defT||[];this._defT.push(fn)}else{this._defL=this._defL||[];this._defL.push(fn)}},load:function(src,chr,id){var t=this;setTimeout(function(){t._load(src,chr,id)},0)},_load:function(src,chr,id){var url=src;if(!src)url=this.protocol+"//"+(this.ovr&&this.ovr.domain?this.ovr.domain:"lptag.liveperson.net")+"/tag/tag.js?site\x3d"+this.site;var s=document.createElement("script");
s.setAttribute("charset",chr?chr:"UTF-8");if(id)s.setAttribute("id",id);s.setAttribute("src",url);document.getElementsByTagName("head").item(0).appendChild(s)},init:function(){this._timing=this._timing||{};this._timing.start=(new Date).getTime();var that=this;if(window.attachEvent)window.attachEvent("onload",function(){that._domReady("domReady")});else{window.addEventListener("DOMContentLoaded",function(){that._domReady("contReady")},false);window.addEventListener("load",function(){that._domReady("domReady")},
false)}if(typeof window._lptStop=="undefined")this.load()},start:function(){this.autoStart=true},_domReady:function(n){if(!this.isDom){this.isDom=true;this.events.trigger("LPT","DOM_READY",{t:n})}this._timing[n]=(new Date).getTime()},vars:lpTag.vars||[],dbs:lpTag.dbs||[],ctn:lpTag.ctn||[],sdes:lpTag.sdes||[],ev:lpTag.ev||[]};lpTag.init()}else window.lpTag._tagCount+=1;(function(){var firstTry=true,tryAgain;var _lpStopTrying=function(){clearInterval(tryAgain)};var startLETag=function(){if(lpTag.start){lpTag.isDom=
true;lpTag.start();_lpStopTrying()}else if(firstTry){firstTry=false;tryAgain=setInterval(startLETag,100)}};startLETag()})();var ensVar0=function(){if(rs.web.digitalData.page_type=="order confirmation")return Bootstrapper.data.resolve("12923");else return""};var ensVar1=function(){return Bootstrapper.data.resolve("10921")};var ensVar2=function(){return Bootstrapper.data.resolve("14629")};var ensVar3=function(){return Bootstrapper.data.resolve("44595")};var ensVar4=function(){return Bootstrapper.data.resolve("44631")};
var ensVar5=function(){return Bootstrapper.data.resolve("16555")};var ensVar6=function(){return Bootstrapper.data.resolve("23380")};var ensVar7=function(){return Bootstrapper.data.resolve("23142")};var ensVar8=function(){return Bootstrapper.data.resolve("16552")};var ensVar9=function(){return""};var ensVar10=function(){return Bootstrapper.data.resolve("13150")};var ensVar11=function(){return Bootstrapper.data.resolve("21895")};var ensVar12=function(){return Bootstrapper.data.resolve("21856")};var ensVar13=
function(){return Bootstrapper.data.resolve("21857")};var ensVar14=function(){return Bootstrapper.data.resolve("13149")};var ensVar15=function(){return Bootstrapper.data.resolve("13152")};var ensVar16=function(){return Bootstrapper.data.resolve("14357")};var ensVar17=function(){return Bootstrapper.data.resolve("13153")};var type=ensVar14.call(this),prodView_names=[ensVar4.call(this)],prodView_categories=[ensVar5.call(this)],prodView_skus=[ensVar6.call(this)],prodView_prices=[ensVar7.call(this)],purchase_total=
ensVar0.call(this),purchase_orderID=ensVar1.call(this),purchase_products=ensVar3.call(this),ctmrinfo_customer_type=ensVar8.call(this),ctmrinfo_customer_id=ensVar10.call(this),ctmrinfo_registration_date=ensVar11.call(this),ctmrinfo_cstatus=ensVar16.call(this),error_message=ensVar12.call(this),error_code=ensVar13.call(this),mrktinfo_campaignID=ensVar15.call(this),currencyCode=ensVar17.call(this),SDES=[];function sendData(data){if(data.length){for(i=0;i<data.length;i++)window.lpTag.sdes.push(data[i]);
window.lpTag.sdes.send()}else;}if(rs.web.digitalData.userId){var ctmrinfoObj={};ctmrinfoObj.ctype=ctmrinfo_customer_type;ctmrinfoObj.customerId=ctmrinfo_customer_id;ctmrinfoObj.cstatus=ctmrinfo_cstatus;ctmrinfoObj.registrationDate={};var registrationDatePart=ctmrinfo_registration_date.slice(0,10).split("/");ctmrinfoObj.registrationDate.day=registrationDatePart[0];ctmrinfoObj.registrationDate.month=registrationDatePart[1];ctmrinfoObj.registrationDate.year=registrationDatePart[2];SDES.push({"type":"ctmrinfo",
"info":ctmrinfoObj})}if(mrktinfo_campaignID)SDES.push({"type":"mrktInfo","info":{"campaignId":mrktinfo_campaignID}});if(type==="errors"){var errorObj={};errorObj.message=error_message;errorObj.code=error_code;SDES.push({"type":"error","error":errorObj})}else if(type==="product"){var productsArr=[],arrayLength=prodView_names.length>prodView_categories.length?prodView_names.length:prodView_categories.length;arrayLength=arrayLength>prodView_skus.length?arrayLength:prodView_skus.length;arrayLength=arrayLength>
prodView_prices.length?arrayLength:prodView_prices.length;for(var productIndex=0;productIndex<arrayLength;productIndex++){var productObj={};if(prodView_names[productIndex])productObj.name=prodView_names[productIndex];if(prodView_categories[productIndex])productObj.category=prodView_categories[productIndex];if(prodView_skus[productIndex])productObj.sku=prodView_skus[productIndex];if(prodView_prices[productIndex])productObj.price=prodView_prices[productIndex];productsArr.push({"product":productObj})}SDES.push({"type":"prodView",
"products":productsArr})}else if(type==="order confirmation"){var arrayLength=purchase_products.length;var purchaseObj={};if(arrayLength>0)purchaseObj.cart={"products":[]};for(var purchaseIndex=0;purchaseIndex<arrayLength;purchaseIndex++){var productObj={"product":{}};if(purchase_products[purchaseIndex]){productObj.product.sku=purchase_products[purchaseIndex].productId;productObj.product.price=parseFloat(purchase_products[purchaseIndex].price).toFixed(2);productObj.quantity=purchase_products[purchaseIndex].orderQuantity;
purchaseObj.cart.products.push(productObj)}}purchaseObj.type="purchase";if(purchase_total)purchaseObj.total=(Math.round(purchase_total*100)/100).toFixed(2);if(purchase_orderID)purchaseObj.orderId=purchase_orderID;if(currencyCode)purchaseObj.currency=currencyCode;SDES.push(purchaseObj)}var lpInitCallbackFunction=function(data,eventInfo){if(data.state=="initialised")sendData(SDES)};var lpStateCallbackFunction=function(data,eventInfo){var lpState=JSON.stringify(data.state).replace(/"/g,"");if(lpState){s.linkTrackVars=
"eVar1,eVar6,eVar7,eVar9,eVar10,eVar32,prop1,prop6,prop9,prop13,prop20,prop21,prop48,eVar94,eVar95,prop64,prop65,events";s.eVar94=lpState;s.prop64=s.eVar94;if(JSON.stringify(data.conversationId)){s.eVar95=JSON.stringify(data.conversationId).replace(/"/g,"");s.prop65=s.eVar95}s.eVar1=Bootstrapper.data.resolve("21900");s.prop1=s.eVar1;s.eVar6=Bootstrapper.data.resolve("13145");s.prop6=s.eVar6;s.eVar7=Bootstrapper.data.resolve("13149");s.prop48=s.eVar7;s.eVar9=Bootstrapper.data.resolve("13146");s.prop20=
s.eVar9;s.eVar10=Bootstrapper.data.resolve("13806");s.prop21=s.eVar10;s.eVar32=s.getTimeParting("n",0,"YYYY-MM-DD hh:mm|DDDD");s.prop9=s.eVar32;s.prop13=Bootstrapper.data.resolve("22074");s.tl(true,"o","LivePerson Chat Event")}var c=document.querySelector("textarea[data-lp-point\x3d'chat_input']");widgetEventsBound||"resume"!==data.state&&"chatting"!==data.state||(data.agentName.toLowerCase().indexOf("rs digital assistant")>=0?widgetOverrideFunction.call(this):hideWidgetOpenerFunction.call(this),
c.focus(),widgetEventsBound=!0);if("ended"===data.state)widgetEventsBound=false};lpTag.events.bind({eventName:"conversationInfo",appName:"lpUnifiedWindow",func:lpStateCallbackFunction,async:true});lpTag.events.bind({eventName:"state",appName:"lpUnifiedWindow",state:"initialised",func:lpInitCallbackFunction});var widgetEventsBound=!1;var widgetOverrideFunction=function widgetOverride(){var e=document.querySelector("button[data-lp-point\x3d'widget_sdk']");if(e)return e.click()};var hideWidgetOpenerFunction=
function hideWidgetOpener(){var e=document.querySelector('[data-lp-point\x3d"widget_sdk"]'),t=document.querySelector(".lp_slider_gap");e&&(e.style.display="none",t&&(t.style.marginLeft="0"))}},2611828,454075);