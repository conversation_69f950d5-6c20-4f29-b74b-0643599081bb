/**
 * Utilitaires pour le mapping des types de matériaux
 */

// Configuration globale des types de matériaux
window.MaterialTypeConfig = {
    // Types SAP avec leurs libellés descriptifs
    sapTypes: [
        { code: 'FERT', description: 'Finished Product' },
        { code: 'HALB', description: 'Semi Finished Product' },
        { code: 'VERP', description: 'Packaging' },
        { code: 'ROH', description: 'Material' },
        { code: 'RAW MATERIAL', description: 'RAW MATERIAL' },
        { code: 'NON VALUATED MATERIAL', description: 'NON VALUATED MATERIAL' },
        { code: 'LITERATURE', description: 'LITERATURE' }
    ],

    // Mapping des anciens libellés vers les codes SAP
    legacyMapping: {
        'FINISHED PRODUCT': 'FERT',
        'SEMI-FINISHED PRODUCT': 'HALB',
        'SEMI FINISHED PRODUCT': 'HALB',
        'PACKAGING': 'VERP',
        'LITTERATURE': 'LITERATURE',
        'Material': 'ROH'
    },

    // Codes SAP valides
    validSapCodes: ['FERT', 'HALB', 'VERP', 'ROH', 'RAW MATERIAL', 'NON VALUATED MATERIAL', 'LITERATURE']
};

/**
 * Mappe un libellé vers un code SAP
 * @param {string} value - La valeur à mapper
 * @returns {string} - Le code SAP correspondant
 */
function mapMaterialTypeToSAP(value) {
    if (!value) {
        return null;
    }

    // Si c'est déjà un code SAP valide, le retourner
    if (MaterialTypeConfig.validSapCodes.includes(value)) {
        return value;
    }

    // Sinon, essayer de mapper depuis les anciens libellés
    return MaterialTypeConfig.legacyMapping[value] || value;
}

/**
 * Retourne le libellé descriptif pour un code SAP
 * @param {string} sapCode - Le code SAP
 * @returns {string} - Le libellé descriptif
 */
function getSapTypeDescription(sapCode) {
    const sapType = MaterialTypeConfig.sapTypes.find(type => type.code === sapCode);
    return sapType ? sapType.description : sapCode;
}

/**
 * Génère les options HTML pour un select de type de matériau
 * @param {string} selectedValue - La valeur sélectionnée
 * @returns {string} - Le HTML des options
 */
function generateMaterialTypeOptions(selectedValue = null) {
    const uniqueOptions = [];
    const seenCodes = new Set();

    // Créer des options uniques basées sur les descriptions
    const optionsData = [
        { code: 'FERT', description: 'Finished Product' },
        { code: 'HALB', description: 'Semi Finished Product' },
        { code: 'VERP', description: 'Packaging' },
        { code: 'ROH', description: 'Material' },
        { code: 'RAW MATERIAL', description: 'RAW MATERIAL' },
        { code: 'NON VALUATED MATERIAL', description: 'NON VALUATED MATERIAL' },
        { code: 'LITERATURE', description: 'LITERATURE' }
    ];

    let html = '';
    optionsData.forEach(option => {
        const isSelected = selectedValue === option.code ? 'selected' : '';
        html += `<option value="${option.code}" ${isSelected}>${option.description}</option>\n`;
    });

    return html;
}

/**
 * Initialise un select de type de matériau
 * @param {HTMLSelectElement} selectElement - L'élément select
 * @param {string} selectedValue - La valeur sélectionnée
 */
function initializeMaterialTypeSelect(selectElement, selectedValue = null) {
    if (!selectElement) return;

    selectElement.innerHTML = generateMaterialTypeOptions(selectedValue);

    // Si le select utilise selectpicker, le rafraîchir
    if ($(selectElement).hasClass('selectpicker')) {
        $(selectElement).selectpicker('refresh');
    }
}

/**
 * Met à jour tous les selects de type de matériau sur la page
 */
function updateAllMaterialTypeSelects() {
    document.querySelectorAll('select[name="matProdType"], select[id="matProdType"], .material-type-select').forEach(select => {
        const currentValue = select.value || select.getAttribute('data-current-value');
        initializeMaterialTypeSelect(select, currentValue);
    });
}

// Initialisation automatique au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    updateAllMaterialTypeSelects();
});

// Rendre les fonctions disponibles globalement
window.mapMaterialTypeToSAP = mapMaterialTypeToSAP;
window.getSapTypeDescription = getSapTypeDescription;
window.generateMaterialTypeOptions = generateMaterialTypeOptions;
window.initializeMaterialTypeSelect = initializeMaterialTypeSelect;
window.updateAllMaterialTypeSelects = updateAllMaterialTypeSelects;
