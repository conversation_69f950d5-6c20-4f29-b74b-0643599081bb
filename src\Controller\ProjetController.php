<?php

namespace App\Controller;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Entity\Project;
use App\Repository\ProjectRepository;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Entity\Phase;
use App\Entity\Code;
use App\Entity\Impute;
use App\Repository\ImputeRepository;
use App\Repository\PhaseRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Entity\Config;
use App\Repository\ConfigRepository;

class ProjetController extends AbstractController
{
    #[Route('/projets', name: 'app_projet')]
    public function index(ProjectRepository $projectRepository, UserRepository $userRepository, ConfigRepository $configRepository): Response
    {
        // Récupérer la configuration
        $config = $configRepository->findOneBy([]);
        if ($config) {
            $today = new \DateTime();
            // On bloque l'accès si la date actuelle est postérieure à la date de clôture
            if ($today > $config->getDateFin()) {
                return $this->render('imputation/access_denied.html.twig', [
                    'message' => "La période d'imputation est terminée depuis le " . $config->getDateFin()->format('d-m-Y') . "."
                ]);
            }
            else if ($today < $config->getDateDeb()) {
                return $this->render('imputation/access_denied.html.twig', [
                    'message' => "La période d'imputation n'a pas encore débuté. Elle commencera le " . $config->getDateDeb()->format('d-m-Y') . "."
                ]);
            }
        }

        // Récupération des projets pour la saisie d'imputation
        $projets = $projectRepository->createQueryBuilder('p')
            ->where('p.Title != :title')
            ->setParameter('title', 'STANDARD')
            ->orderBy('p.OTP', 'DESC')
            ->getQuery()
            ->getResult();
            
        return $this->render('imputation/main.html.twig', [
            'projets' => $projets,
        ]);
    }
    
    #[Route('/projets/show/{id}', name: 'app_projet_show', methods: ['GET'])]
    public function show(ProjectRepository $projectRepository, PhaseRepository $phaseRepository, $id, EntityManagerInterface $entityManager): Response
    {
        return new JsonResponse(
            $projectRepository->find($id)->toArray(),
        );
    }


    #[Route('/projets/imputations', name: 'app_projet_imputations', methods: ['POST'])]
    public function getImputations(ProjectRepository $projectRepository, Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $projet_id = $request->get('projet_id');
        $phase_id = $request->get('phase_id');
        $user = $this->getUser();

        if($phase_id){
            $phase = $entityManager->getRepository(Phase::class)->find($phase_id);
            $codes = $phase->getCodes();
            $imputations = $entityManager->getRepository(Impute::class)->findBy(['user' => $user->getId(), 'code' => $codes->toArray()]);
            $imputations = array_map(function($imputation){
                return $imputation->toArray();
            }, $imputations);
        }elseif($projet_id){
            $phases = $entityManager->getRepository(Phase::class)->findBy(['project' => $projet_id]);
            $codes = [];
            foreach($phases as $phase){
                $codes = array_merge($codes, $phase->getCodes()->toArray());
            }
            $imputations = $entityManager->getRepository(Impute::class)->findBy(['user' => $user->getId(), 'code' => $codes]);
            $imputations = array_map(function($imputation){
                return $imputation->toArray();
            }, $imputations);
        }else{
            $imputations = $entityManager->getRepository(Impute::class)->findBy(['user' => $user->getId()]);
            $imputations = array_map(function($imputation){
                return $imputation->toArray();
            }, $imputations);
        }

        return new JsonResponse([
            "imputations" => $imputations
        ]);
    }

    #[Route('/projets/import', name: 'app_projet_import', methods: ['GET'])]
    public function import(ProjectRepository $projectRepository, EntityManagerInterface $entityManager): Response
    {
        set_time_limit(0);
    
        // Charger l'excel
        $spreadsheet = IOFactory::load('CN41.xlsx');
    
        // Désactiver les logs SQL pour optimiser les performances
        $entityManager->getConnection()->getConfiguration()->setSQLLogger(null);
    
        // Récupérer tous les projets existants en une seule requête
        $projects = $entityManager->getRepository(Project::class)->findAll();
        $projectMap = [];
        foreach ($projects as $proj) {
            $projectMap[$proj->getOTP()] = $proj;
        }
    
        // Récupérer toutes les phases existantes en une seule requête
        $phases = $entityManager->getRepository(Phase::class)->findAll();
        $phaseMap = [];
        foreach ($phases as $phase) {
            $phaseMap[$phase->getCode()] = $phase;
        }
    
        // Récupérer tous les codes existants en une seule requête
        $codes = $entityManager->getRepository(Code::class)->findAll();
        $codeMap = [];
        foreach ($codes as $code) {
            $codeMap[$code->getCode()] = $code;
        }
    
        $lastProject = null;
        $lastPhase = null;
    
        foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
            $array = $worksheet->toArray();
            array_shift($array);
    
            foreach ($array as $value) {
                $niveau = $value[1];
                $title = $value[2];
                $code = $value[3];
                $status = $value[4];
                $workCenter = $value[5];
    
                // Gestion des projets
                if ($niveau == "00") {
                    if (!isset($projectMap[$code])) {
                        $project = new Project();
                        $project->setOTP($code);
                        $project->setTitle($title);
                        $projectMap[$code] = $project;
                        $entityManager->persist($project);
                    } elseif ($projectMap[$code]->getStatus() != $status) {
                        $projectMap[$code]->setStatus($status);
                        $entityManager->persist($projectMap[$code]);
                    }
                    $lastProject = $projectMap[$code];
                }
    
                // Gestion des phases
                if ($niveau == "03" && $title !== "Cross operations") {
                    if (!isset($phaseMap[$code])) {
                        $phase = new Phase();
                        $phase->setCode($code);
                        $phase->setTitle($title);
                        $phase->setLevel($niveau);
                        $phase->setStatusTxt($status);
                        $phase->setStatus(!preg_match('/IMBL|TCLO|BLOQ/', $status));
                        $phase->setProjet($lastProject);
                        $phaseMap[$code] = $phase;
                        $entityManager->persist($phase);
                    } elseif ($phaseMap[$code]->getStatusTxt() != $status) {
                        $phaseMap[$code]->setStatusTxt($status);
                        $phaseMap[$code]->setStatus(!preg_match('/IMBL|TCLO|BLOQ/', $status));
                        $entityManager->persist($phaseMap[$code]);
                    }
                    $lastPhase = $phaseMap[$code];
                }
    
                // Gestion des codes
                if ($niveau == "05" && strpos($workCenter, 'Z') === 0) {
                    $codeKey = $code;
                    if (!isset($codeMap[$codeKey])) {
                        $codeObj = new Code();
                        $codeObj->setCode($code);
                        $codeObj->setTitle($title);
                        $codeObj->setLevel($niveau);
                        $codeObj->setStatus($status);
                        $codeObj->setWorkCenter($workCenter);
                        $codeObj->setPhase($lastPhase);
                        $codeMap[$codeKey] = $codeObj;
                        $entityManager->persist($codeObj);
                    } elseif ($codeMap[$codeKey]->getStatus() != $status) {
                        $codeMap[$codeKey]->setStatus($status);
                        $entityManager->persist($codeMap[$codeKey]);
                    }
                }
            }
        }
    
        // Dernier flush après la boucle
        $entityManager->flush();
    
        return new JsonResponse(["message" => "Importation terminée"]);
    }
    
}