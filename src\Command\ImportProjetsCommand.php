<?php

namespace App\Command;

use App\Entity\Project;
use App\Entity\Phase;
use App\Entity\Code;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ImportProjetsCommand extends Command
{
    protected static $defaultName = 'app:projets-import';
    protected static $defaultDescription = 'Importe les projets, phases et codes depuis un fichier Excel';

    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();

        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->setName('app:projets-import')
            ->setDescription('Importe les projets, phases et codes depuis un fichier Excel')
            ->addOption('no-logs', null, InputOption::VALUE_NONE, 'Désactiver les logs SQL pour optimiser les performances');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
         ini_set('memory_limit', '-1');
        // Désactivation du time limit
        if (function_exists('set_time_limit')) {
            set_time_limit(0);
        }

        $file = "D:\BE\FRSCMREL_NEW\public\CN41.xlsx";
        if (!file_exists($file)) {
            $output->writeln(sprintf('<error>Le fichier "%s" est introuvable.</error>', $file));
            return Command::FAILURE;
        }

        if ($input->getOption('no-logs')) {
            $this->entityManager->getConnection()->getConfiguration()->setSQLLogger(null);
        }

        $output->writeln('<info>Chargement du fichier Excel...</info>');
        $spreadsheet = IOFactory::load($file);

        // Chargement des entités existantes
        $output->writeln('<info>Récupération des projets existants...</info>');
        $projects = $this->entityManager->getRepository(Project::class)->findAll();
        $projectMap = [];
        foreach ($projects as $proj) {
            $projectMap[$proj->getOTP()] = $proj;
        }

        $output->writeln('<info>Récupération des phases existantes...</info>');
        $phases = $this->entityManager->getRepository(Phase::class)->findAll();
        $phaseMap = [];
        foreach ($phases as $phase) {
            $phaseMap[$phase->getCode()] = $phase;
        }

        $output->writeln('<info>Récupération des codes existants...</info>');
        $codes = $this->entityManager->getRepository(Code::class)->findAll();
        $codeMap = [];
        foreach ($codes as $code) {
            $codeMap[$code->getCode()] = $code;
        }

        $lastProject = null;
        $lastPhase = null;

        foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
            $rows = $worksheet->toArray();
            array_shift($rows); // Enlever l'en-tête

            foreach ($rows as $row) {
                [,$niveau, $title, $code, $status, $workCenter] = $row + [null, null, null, null, null, null];

                // Gestion des projets
                if ($niveau === '00') {
                    if (!isset($projectMap[$code])) {
                        // Création d’un nouveau projet avec titre ET statut
                        $project = new Project();
                        $project
                            ->setOTP($code)
                            ->setTitle($title)
                            ->setStatus($status);
                        $projectMap[$code] = $project;
                        $this->entityManager->persist($project);
                    } elseif (
                        // Mise à jour si le statut OU le titre a changé
                        $projectMap[$code]->getStatus() !== $status
                        || $projectMap[$code]->getTitle()  !== $title
                    ) {
                        $projectMap[$code]
                            ->setStatus($status)
                            ->setTitle($title);
                        $this->entityManager->persist($projectMap[$code]);
                    }
                    $lastProject = $projectMap[$code];
                }


                // Gestion des phases
                if ($niveau === '03' && $title !== 'Cross operations') {
                    if (!isset($phaseMap[$code])) {
                        // Création
                        $phase = new Phase();
                        $phase
                            ->setCode($code)
                            ->setTitle($title)
                            ->setLevel($niveau)
                            ->setStatusTxt($status)
                            ->setStatus(!preg_match('/IMBL|TCLO|BLOQ/', $status))
                            ->setProjet($lastProject);
                        $phaseMap[$code] = $phase;
                        $this->entityManager->persist($phase);
                    } elseif (
                        // Mise à jour si le statut textuel OU le titre a changé
                        $phaseMap[$code]->getStatusTxt() !== $status
                        || $phaseMap[$code]->getTitle()     !== $title
                    ) {
                        $phaseMap[$code]
                            ->setStatusTxt($status)
                            ->setStatus(!preg_match('/IMBL|TCLO|BLOQ/', $status))
                            ->setTitle($title);
                        $this->entityManager->persist($phaseMap[$code]);
                    }
                    $lastPhase = $phaseMap[$code];
                }


                // Gestion des codes
                if ($niveau === '05' && strpos($workCenter, 'Z') === 0) {
                    $codeKey = $code;
                    if (!isset($codeMap[$codeKey])) {
                        // Création
                        $codeObj = new Code();
                        $codeObj
                            ->setCode($code)
                            ->setTitle($title)
                            ->setLevel($niveau)
                            ->setStatus($status)
                            ->setWorkCenter($workCenter)
                            ->setPhase($lastPhase);
                        $codeMap[$codeKey] = $codeObj;
                        $this->entityManager->persist($codeObj);
                    } elseif (
                        // Mise à jour si le statut OU le titre a changé
                        $codeMap[$codeKey]->getStatus() !== $status
                        || $codeMap[$codeKey]->getTitle()  !== $title
                    ) {
                        $codeMap[$codeKey]
                            ->setStatus($status)
                            ->setTitle($title);
                        $this->entityManager->persist($codeMap[$codeKey]);
                    }
                }
            }
        }

        $output->writeln('<info>Enregistrement en base de données...</info>');
        $this->entityManager->flush();

        $output->writeln('<info>Importation terminée avec succès.</info>');
        return Command::SUCCESS;
    }
}
