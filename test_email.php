<?php

// Script de test simple pour vérifier l'envoi d'emails
require_once 'vendor/autoload.php';

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

echo "=== Test de configuration SMTP ===\n";

$mail = new PHPMailer(true);

try {
    // Configuration SMTP
    $mail->isSMTP();
    $mail->Host = 'smtp.office365.com';
    $mail->Port = 587;
    $mail->SMTPAuth = true;
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Username = '<EMAIL>';
    $mail->Password = 'bNEsp7M71Hhdp6MqlekUlw3TZduKHL';
    $mail->CharSet = 'UTF-8';
    
    echo "✅ Configuration SMTP OK\n";
    
    // Test de connexion
    echo "🔄 Test de connexion SMTP...\n";
    $mail->smtpConnect();
    echo "✅ Connexion SMTP réussie !\n";
    $mail->smtpClose();
    
    // Test d'envoi d'email
    echo "🔄 Test d'envoi d'email...\n";
    
    $mail->setFrom('<EMAIL>', 'Test DMO System');
    $mail->addAddress('<EMAIL>', 'Adrien Chatain'); // Utilise l'email d'un utilisateur existant
    
    $mail->isHTML(true);
    $mail->Subject = 'Test Email DMO System - ' . date('Y-m-d H:i:s');
    $mail->Body = '
    <html>
    <body>
        <h1>Test Email DMO System</h1>
        <p>Ceci est un email de test du système DMO.</p>
        <p>Date: ' . date('Y-m-d H:i:s') . '</p>
        <p>Si vous recevez cet email, la configuration SMTP fonctionne correctement.</p>
    </body>
    </html>';
    $mail->AltBody = 'Test Email DMO System - Ceci est un email de test du système DMO.';
    
    $mail->send();
    echo "✅ Email envoyé avec succès !\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Détails: " . $mail->ErrorInfo . "\n";
}

echo "\n=== Test terminé ===\n";
