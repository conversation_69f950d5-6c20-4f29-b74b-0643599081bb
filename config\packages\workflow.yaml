framework:
    workflows:
        document_workflow:
            type: workflow
            marking_store:
                type: method
                property: currentSteps
            supports:
                - App\Entity\Document
            initial_marking: [BE_0]
            places:
                - BE_0
                - BE_1
                - BE                                            
                - Produit
                - Qual_Logistique
                - Logistique
                - Metro
                - Quality
                - Achat_Rfq
                - Achat_RoHs_REACH
                - Assembly
                - Machining
                - Molding
                - Methode_assemblage
                - Planning
                - Core_Data
                - Project
                - Achat_F30
                - Prod_Data
                - Achat_FIA
                - Achat_Hts
                - Saisie_hts
                - Costing
                - GID
                - Indus
                - methode_Labo
                - QProd
                - Tirage_Plans
            transitions:
                to_BE_1:
                    from: BE_0
                    to: BE_1
                to_BE:
                    from: BE_1
                    to: BE
                to_Produit:
                    from: BE
                    to: Produit
                to_Qual_Logistique:
                    from: BE
                    to: Qual_Logistique
                to_Logistique:
                    from: Qual_Logistique
                    to: Logistique
                to_Metro_from_Quality:
                    from: Quality
                    to: Metro
                to_Quality:
                    from: BE
                    to: Quality
                to_Assembly_from_Quality:
                    from: Quality
                    to: Assembly
                to_Machining_from_Assembly:
                    from: Assembly
                    to: Machining
                to_Molding_from_Assembly:
                    from: Assembly
                    to: Molding
                to_Assembly_from_Machining:
                    from: Machining
                    to: Assembly
                to_Molding_from_Machining:
                    from: Machining
                    to: Molding
                to_Assembly_from_Molding:
                    from: Molding
                    to: Assembly
                to_Machining_from_Molding:
                    from: Molding
                    to: Machining
                to_Machining_from_Quality:
                    from: Quality
                    to: Machining
                to_Molding_from_Quality:
                    from: Quality
                    to: Molding
                to_Core_Data_from_Quality:
                    from: Quality
                    to: Core_Data
                to_Projet:
                    from: BE
                    to: Project
                to_Core_Data_from_Logistique:
                    from: Logistique
                    to: Core_Data
                to_Achat_Rfq_from_Produit:
                    from: Produit
                    to: Achat_Rfq
                to_Achat_Rfq_from_Quality:
                    from: Quality
                    to: Achat_Rfq
                to_Achat_RoHs_REACH:
                    from: Achat_Rfq
                    to: Achat_RoHs_REACH
                to_Assembly_from_Achat_Rfq:
                    from: Achat_Rfq
                    to: Assembly
                to_Machining_from_Achat_Rfq:
                    from: Achat_Rfq
                    to: Machining
                to_Molding_from_Achat_Rfq:
                    from: Achat_Rfq
                    to: Molding
                to_Core_Data_from_Achat_RFQ:
                    from: Achat_Rfq
                    to: Core_Data
                to_Assembly:
                    from: Produit
                    to: Assembly
                to_Quality_from_Assembly:
                    from: Assembly
                    to: Quality
                to_method_assemblage_from_Assembly:
                    from: Assembly
                    to: Methode_assemblage
                to_Planning_from_Assembly:
                    from: Assembly
                    to: Planning
                to_Metro_from_Assembly:
                    from: Assembly
                    to: Metro
                to_Machining:
                    from: Produit
                    to: Machining
                to_Quality_from_Machining:
                    from: Machining
                    to: Quality
                to_Planning_from_Machining:
                    from: Machining
                    to: Planning
                to_Metro_from_Machining:
                    from: Machining
                    to: Metro
                to_Molding:
                    from: Produit
                    to: Molding
                to_Quality_from_Molding:
                    from: Molding
                    to: Quality
                to_Planning_from_Molding:
                    from: Molding
                    to: Planning
                to_Metro_from_Molding:
                    from: Molding
                    to: Metro
                to_Core_Data_from_Planning:
                    from: Planning
                    to: Core_Data
                to_Indus:
                    from: Metro
                    to: Indus
                to_Core_Data_from_Metro:
                    from: Metro
                    to: Core_Data
                to_Methode_Labo:
                    from: Indus
                    to: methode_Labo
                to_GID:
                    from: Indus
                    to: GID
                to_Gid_from_Prod_Data:
                    from: Prod_Data
                    to: GID
                to_Costing:
                    from: GID
                    to: Costing
                to_Achat_F30:
                    from: Achat_Rfq
                    to: Achat_F30
                to_Core_Data:
                    from: Achat_F30
                    to: Core_Data
                to_Assembly_from_Achat_F30:
                    from: Achat_F30
                    to: Assembly
                to_Machining_from_Achat_F30:
                    from: Achat_F30
                    to: Machining
                to_Molding_from_Achat_F30:
                    from: Achat_F30
                    to: Molding
                to_Core_Data_from_Project:
                    from: Project
                    to: Core_Data
                to_Prod_Data:
                    from: Core_Data
                    to: Prod_Data
                to_Achat_FIA:
                    from: Prod_Data
                    to: Achat_FIA
                to_Achat_Hts:
                    from: Achat_FIA
                    to: Achat_Hts
                to_Saisie_hts:
                    from: Achat_Hts
                    to: Saisie_hts
                to_Costing_from_Achat_FIA:
                    from: Achat_FIA
                    to: Costing
                to_Qprod_from_Metro:
                    from: Metro
                    to: QProd
                to_Tirage_Plans_from_Core_Data:
                    from: Core_Data
                    to: Tirage_Plans