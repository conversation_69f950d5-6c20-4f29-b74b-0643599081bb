<?php

namespace App\Tests\Unit;

use App\Repository\DocumentRepository;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

class DocumentRepositoryMethodsTest extends TestCase
{
    public function testStatisticsMethodsExist(): void
    {
        $reflection = new ReflectionClass(DocumentRepository::class);
        
        // Phase 1.1 - Méthodes statistiques temporelles
        $this->assertTrue($reflection->hasMethod('getAverageTimePerWorkflowStep'));
        $this->assertTrue($reflection->hasMethod('getCycleTimeStatistics'));
        
        // Phase 1.2 - Méthodes statistiques de visas
        $this->assertTrue($reflection->hasMethod('getVisaStatisticsByStep'));
        $this->assertTrue($reflection->hasMethod('getDocumentsPendingVisaByUser'));
        $this->assertTrue($reflection->hasMethod('getValidatorPerformanceStatistics'));
        
        // Phase 1.3 - Méthodes statistiques de distribution
        $this->assertTrue($reflection->hasMethod('getDetailedDocumentTypeDistribution'));
        $this->assertTrue($reflection->hasMethod('getDocumentDistributionByProjectAndDMO'));
        $this->assertTrue($reflection->hasMethod('getDocumentCreationTrends'));
    }

    public function testMethodsArePublic(): void
    {
        $reflection = new ReflectionClass(DocumentRepository::class);
        
        $methods = [
            'getAverageTimePerWorkflowStep',
            'getCycleTimeStatistics',
            'getVisaStatisticsByStep',
            'getDocumentsPendingVisaByUser',
            'getValidatorPerformanceStatistics',
            'getDetailedDocumentTypeDistribution',
            'getDocumentDistributionByProjectAndDMO',
            'getDocumentCreationTrends'
        ];
        
        foreach ($methods as $methodName) {
            $method = $reflection->getMethod($methodName);
            $this->assertTrue($method->isPublic(), "Method {$methodName} should be public");
        }
    }

    public function testMethodsReturnTypes(): void
    {
        $reflection = new ReflectionClass(DocumentRepository::class);
        
        $methodsWithArrayReturn = [
            'getAverageTimePerWorkflowStep',
            'getCycleTimeStatistics',
            'getVisaStatisticsByStep',
            'getDocumentsPendingVisaByUser',
            'getValidatorPerformanceStatistics',
            'getDetailedDocumentTypeDistribution',
            'getDocumentDistributionByProjectAndDMO',
            'getDocumentCreationTrends'
        ];
        
        foreach ($methodsWithArrayReturn as $methodName) {
            $method = $reflection->getMethod($methodName);
            $returnType = $method->getReturnType();
            
            // Vérifier que la méthode a un type de retour défini
            $this->assertNotNull($returnType, "Method {$methodName} should have a return type");
            
            // Vérifier que le type de retour est 'array'
            $this->assertEquals('array', $returnType->getName(), "Method {$methodName} should return array");
        }
    }

    public function testFallbackMethodsExist(): void
    {
        $reflection = new ReflectionClass(DocumentRepository::class);
        
        // Vérifier que les méthodes fallback existent
        $fallbackMethods = [
            'getAverageTimePerWorkflowStepFallback',
            'getCycleTimeStatisticsFallback',
            'getVisaStatisticsByStepFallback',
            'getValidatorPerformanceStatisticsFallback',
            'getDetailedDocumentTypeDistributionFallback',
            'getDocumentDistributionByProjectAndDMOFallback',
            'getDocumentCreationTrendsFallback'
        ];
        
        foreach ($fallbackMethods as $methodName) {
            $this->assertTrue($reflection->hasMethod($methodName), "Fallback method {$methodName} should exist");
            
            $method = $reflection->getMethod($methodName);
            $this->assertTrue($method->isPrivate(), "Fallback method {$methodName} should be private");
        }
    }

    public function testMethodParameters(): void
    {
        $reflection = new ReflectionClass(DocumentRepository::class);
        
        // Test getDocumentsPendingVisaByUser parameters
        $method = $reflection->getMethod('getDocumentsPendingVisaByUser');
        $parameters = $method->getParameters();
        $this->assertCount(1, $parameters);
        $this->assertEquals('userId', $parameters[0]->getName());
        $this->assertTrue($parameters[0]->allowsNull());
        $this->assertTrue($parameters[0]->hasType());
        $this->assertEquals('int', $parameters[0]->getType()->getName());
        
        // Test getDocumentCreationTrends parameters
        $method = $reflection->getMethod('getDocumentCreationTrends');
        $parameters = $method->getParameters();
        $this->assertCount(1, $parameters);
        $this->assertEquals('months', $parameters[0]->getName());
        $this->assertTrue($parameters[0]->hasType());
        $this->assertEquals('int', $parameters[0]->getType()->getName());
        $this->assertTrue($parameters[0]->isDefaultValueAvailable());
        $this->assertEquals(12, $parameters[0]->getDefaultValue());
    }

    public function testDocumentationComments(): void
    {
        $reflection = new ReflectionClass(DocumentRepository::class);
        
        $methodsWithDocumentation = [
            'getAverageTimePerWorkflowStep',
            'getCycleTimeStatistics',
            'getVisaStatisticsByStep',
            'getDocumentsPendingVisaByUser',
            'getValidatorPerformanceStatistics',
            'getDetailedDocumentTypeDistribution',
            'getDocumentDistributionByProjectAndDMO',
            'getDocumentCreationTrends'
        ];
        
        foreach ($methodsWithDocumentation as $methodName) {
            $method = $reflection->getMethod($methodName);
            $docComment = $method->getDocComment();
            
            $this->assertNotFalse($docComment, "Method {$methodName} should have documentation");
            $this->assertStringContainsString('@return array', $docComment, "Method {$methodName} should document array return type");
        }
    }
}
