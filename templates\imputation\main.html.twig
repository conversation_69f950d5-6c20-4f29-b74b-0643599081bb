{% extends 'baseImputation.html.twig' %}

{% block title %}
    Imputation Projet
{% endblock %}

{% block body %}
<style>
    .projet-item:hover {
        background-color: #f8f9fa;
    }
    .projet-item.active {
        background-color: #e9ecef;
    }

    .phase-item.active>td {
        background-color: #e9ecef!important;
    }

    .imputation-item.active>td {
        background-color: #e9ecef!important;
    }

    td{
        vertical-align: middle;
    }

    .no-hover {
        pointer-events: none;
    }
    .no-hover>td {
        background-color: #F8F9FA;
    }
    .no-hover:hover {
        background-color: transparent;
    }
</style>
<div style="margin: 0 2%">
    <div class="row mt-3">
        <div class="col-md-3">
            <h4>Projet</h4>
            <div style="height: 30vh; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 4px;">
                {% for projet in projets %}
                    <div class="projet-item" data-projet-id="{{ projet.id }}" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #dee2e6;font-size: 12px;">
                        <strong>{{ projet.otp }}</strong> - {{ projet.title }}
                    </div>
                {% endfor %}
            </div>
        </div>
        <div class="col-md-9">
            <h4>Phases</h4>
            <div id="phases-container" style="height: 30vh; overflow-y: auto;">
            </div>
        </div>
        <div class="col-md-12">
            <hr class="mt-4"></hr>
            <h4 class="mb-0">Imputations</h4>
            <div id="imputations-container" >
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="editNbHeuresModal" tabindex="-1" aria-labelledby="editNbHeuresModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editNbHeuresModalLabel">Modifier le nombre d'heures</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="number" class="form-control" id="nbHeures" placeholder="Nombre d'heures">
                <input type="hidden" id="imputationId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="saveNbHeures">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

{# modal pour ajouter une imputation a une phase avec un champs code [select] et un champs nbHeures [number] #}
<div class="modal fade" id="addImputationModal" tabindex="-1" aria-labelledby="addImputationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addImputationModalLabel">Ajouter une imputation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="code" class="form-label">Code</label>
                    <select class="form-select" id="code">
                    </select>
                </div>
                <div class="mb-3">
                    <label for="nbHeures" class="form-label">Nombre d'heures</label>
                    <input type="number" class="form-control" id="nbHeures2" placeholder="Nombre d'heures">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="addImputation">Ajouter</button>
            </div>
        </div>
    </div>
</div>


{# Modal pour ajouter une imputation par l'admin #}
<div class="modal fade" id="addImputationManagerModal" tabindex="-1" aria-labelledby="addImputationManagerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addImputationManagerModalLabel">Ajouter une imputation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="users" class="form-label">Utilisateur</label>
                    <select class="form-select" id="users">
                    </select>
                </div>

                <div class="mb-3">
                    <label for="codeMaster" class="form-label">Code manager</label>
                    <select class="form-select" id="codeMaster">
                    </select>
                </div>
                <div class="mb-3">
                    <label for="nbHeuresAdmin" class="form-label">Nombre d'heures</label>
                    <input type="number" class="form-control" id="nbHeuresAdmin" placeholder="Nombre d'heures">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="addImputationManager">Ajouter</button>
            </div>
        </div>
    </div>
</div>



<script>
const user_work_center = "{{ app.user.workCenter }}";
$(document).ready(function() {
    {% if app.user.titre == "Chef de Projets" %}
        $.ajax({
        url: "{{ path('app_impute_users') }}",
        type: 'GET',
        success: function(users) {
            var $select = $('#users');
            $select.empty();
            users.forEach(function(user) {
                $('<option>')
                    .val(user.id)
                    .text(user.nom + ' ' + user.prenom)
                    .appendTo($select);
            });
        }
        });
    {% elseif app.user.isManager %}
        $.ajax({
            url: "{{ path('app_impute_users_manager') }}",
            type: 'GET',
            success: function(users) {
            console.log(users);
            var $select = $('#users');
            $select.empty();
            users.forEach(function(user) {
                $('<option>')
                .val(user.id)
                .text(user.nom + ' ' + user.prenom)
                .appendTo($select);
            });
            }
        });            
    {% endif %}
    $.ajax({
        url: "{{ path('app_impute_codes') }}",
        type: 'GET',
        dataType: 'json',
        success: function(codeMasters) {
            console.log(codeMasters);
            var $select = $('#codeMaster');
            $select.empty();
            codeMasters.forEach(function(codeMaster) {
                $('<option>')
                    .val(codeMaster.id)
                    .text(codeMaster.title)
                    .appendTo($select);
            });
        }
    });


});
function getPhases(projetId) {
    var url = "{{ path('app_projet_show', { 'id': 'edit_me' }) }}";
    url = url.replace('edit_me', projetId);
    $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
            var $table = $('<table>').addClass('table table-hover');
            var $thead = $('<thead>')
            .css({ 'position': 'sticky', 'top': '0', 'background': 'white', 'z-index': '10' })
            .appendTo($table);
            $('<tr>')
                .append($('<th>').text('Code').addClass('bg-light'))
                .append($('<th>').text('Titre').addClass('bg-light'))
                .append($('<th>').text('Commentaire').addClass('bg-light'))
                .append($('<th>').text('Statut').addClass('bg-light text-center'))
                .appendTo($thead);
            var $tbody = $('<tbody>');
            $.each(response.openPhases, function(index, phase) {
                $('<tr>')
                    .addClass('phase-item')
                    .attr('data-phase-id', phase.id)
                    .append($('<td>').text(phase.code))
                    .append($('<td>').text(phase.title))
                    {% if app.user.titre == "Chef de Projets" %}
                        .append($('<td>').html((phase.commentaire ? phase.commentaire : '')  + '<button class="ms-2 badge bg-primary edit-commentaire border-0" data-phase-id="' + phase.id + '" value="' + (phase.commentaire ? phase.commentaire : '') + '"><i class="fas fa-edit"></i></button>'))
                    {% else %}
                        .append($('<td>').text(phase.commentaire ? phase.commentaire : ''))
                    {% endif %}
                    .append($('<td>').html((phase.status ? '<i class="ms-2 fa-regular fa-circle-dot" style="color: #198754;" title="Phase ouverte"></i>' + (phase.statusManuel === null || phase.statusManuel ? ' <i class="fa-regular fa-thumbs-up manualclose" style="color: #198754;cursor: pointer;" data-phase-id="' + phase.id + '" title="Phase ouverte"></i>' : ' <i class="fa-regular fa-hand manualclose" style="color: #DC3545;cursor: pointer;" data-phase-id="' + phase.id + '" title="Fermée par Chef de projet"></i>') : '<i class="ms-2 fa-regular fa-circle-dot" style="color: #DC3545;" title="Phase fermée"></i>')))
                    .addClass('text-center')
                    .appendTo($tbody);
            });
            $.each(response.closedPhases, function(index, phase) {
                $('<tr>')
                    .addClass('closed-phase-item')
                    .attr('data-phase-id', phase.id)
                    .append($('<td>').text(phase.code))
                    .append($('<td>').text(phase.title))
                    {% if app.user.titre == "Chef de Projets" or app.user.titre == "Cheffe de Projets" %}
                        .append($('<td>').html((phase.commentaire ? phase.commentaire : '')  + '<button class="ms-2 badge bg-primary edit-commentaire border-0" data-phase-id="' + phase.id + '" value="' + (phase.commentaire ? phase.commentaire : '') + '"><i class="fas fa-edit"></i></button>'))
                    {% else %}
                        .append($('<td>').text(phase.commentaire ? phase.commentaire : ''))
                    {% endif %}
                    .append($('<td>').html((phase.status ? '<i class="ms-2 fa-regular fa-circle-dot" style="color: #198754;" title="Phase ouverte"></i>' + (phase.statusManuel === null || phase.statusManuel ? ' <i class="fa-regular fa-thumbs-up manualclose" style="color: #198754;cursor: pointer;" data-phase-id="' + phase.id + '" title="Phase ouverte"></i>' : ' <i class="fa-regular fa-hand manualclose" style="color: #DC3545;cursor: pointer;" data-phase-id="' + phase.id + '" title="Fermée par Chef de projet"></i>') : '<i class="ms-2 fa-regular fa-circle-dot" style="color: #DC3545;" title="Phase fermée"></i>')))
                    .addClass('text-center')
                    .appendTo($tbody);
            });
            $table.append($tbody);
            $('#phases-container').empty().append($table);
            $('#phases-container').animate({ scrollTop: 0 }, 'fast');
        }
    });
}

function getImputations(phaseId) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: "{{ path('app_projet_imputations') }}",
            type: 'POST',
            data: { phase_id: phaseId },
            success: function(response) {
                resolve(response.imputations); // On résout la Promise avec les imputations
            },
            error: function(error) {
                reject(error); // On rejette la Promise en cas d'erreur
            }
        });
    });
}

function fillImputations(imputations) {
    let $container = $('#imputations-container').empty();

    if (imputations.length === 0) {
        $container.append(
            $('<div>').addClass('alert alert-warning w-100 text-center mt-3')
                .text('Aucune imputation trouvée.')
        );
        return;
    }
    // reverse imputations
    imputations.reverse();

    // Création d'une div scrollable contenant le tableau
    let $scrollContainer = $('<div>').css({ 'max-height': '45vh', 'overflow-y': 'auto' }).addClass('table-responsive');

    let $table = $('<table>').addClass('table w-100 mb-0');
    let $thead = $('<thead>').css({ 'position': 'sticky', 'top': '0', 'background': 'white', 'z-index': '10' }).appendTo($table);
    $('<tr>')
        .append($('<th>').text('Période').addClass('bg-light'))
        .append($('<th>').text('Projet').addClass('bg-light'))
        .append($('<th>').text('Phase').addClass('bg-light'))
        .append($('<th>').text('Heures').addClass('text-center bg-light'))
        .append($('<th>').text('Code').addClass('text-center bg-light'))
        .append($('<th>').text('Poste').addClass('text-center bg-light'))
        .append($('<th>').text('Supprimer').addClass('text-center bg-light'))
        .appendTo($thead);

    let $tbody = $('<tbody>').appendTo($table);
    let total = 0;

    imputations.forEach(imputation => {
        $('<tr>').addClass('imputation-item').attr('data-imputation-id', imputation.id)
            .append($('<td>').text(imputation.periode.charAt(0).toUpperCase() + imputation.periode.slice(1)))
            .append($('<td>').text(imputation.code.projet))
            .append(
                $('<td>').html((imputation.code.phaseStatus ? '<i class="ms-2 fa-regular fa-circle-dot" style="color: #198754;" title="Phase ouverte"></i>' + (imputation.code.phaseStatusManuel === null || imputation.code.phaseStatusManuel ? ' <i class="fa-regular fa-thumbs-up me-2" style="color: #198754" title="Phase ouverte"></i>' : ' <i class="fa-regular fa-hand me-2" style="color: #DC3545" title="Fermée par Chef de projet"></i>') : '<i class="ms-2 me-2 fa-regular fa-circle-dot" style="color: #DC3545;" title="Phase fermée"></i>') + imputation.code.phase)
            )
            .append(
                $('<td>').addClass('text-center')
                    .append($('<span>').text(imputation.nbHeures).addClass('nbHeures'))
                    .append(imputation.code.phaseOpen ? $('<span>').html('<i class="fas fa-edit"></i>').addClass('editNbHeures ms-2').css('cursor', 'pointer').attr('data-imputation-id', imputation.id).attr('data-value', imputation.nbHeures) : '')
            )
            .append($('<td>').text(imputation.code.code).addClass('text-center'))
            .append($('<td>').text(imputation.code.workCenter).addClass('text-center'))
            .append(
                $('<td>').addClass('text-center')
                    .append(imputation.code.phaseOpen ? $('<i class="fas fa-trash-alt delete-impute"></i>').css('cursor', 'pointer').attr('data-imputation-id', imputation.id) : '')
            )
            .appendTo($tbody);
        total += imputation.nbHeures;
    });

    let $tfoot = $('<tfoot>').css({ 'position': 'sticky', 'bottom': '0', 'background': 'white', 'z-index': '10' }).appendTo($table);
    $('<tr>')
        .addClass('fw-bold border-0')
        .append($('<td>').text('Total').attr('colspan', 3).addClass('text-end fw-bold bg-light border-0'))
        .append($('<td>').text(total).addClass('text-center bg-light border-0'))
        .append($('<td>').addClass('bg-light border-0'))
        .append($('<td>').addClass('bg-light border-0'))
        .append($('<td>').addClass('bg-light border-0'))
        .appendTo($tfoot);

    $scrollContainer.append($table);
    $container.append($scrollContainer);
}

function highlightImputation(imputations) {
    $('.imputation-item').removeClass('active');
    $.each(imputations, function(index, imputation) {
        var $imputationItem = $('.imputation-item[data-imputation-id="' + imputation.id + '"]');
        $imputationItem.addClass('active');
    });
}

function editNbHeures(imputationId, nbHeures) {
    var url = "{{ path('app_impute_edit', { 'id': 'edit_me' }) }}";
    url = url.replace('edit_me', imputationId);
    Toast.fire({
        icon: 'info',
        title: 'Modification en cours...'
    });
    return new Promise((resolve, reject) => {
        $.ajax({
            url: url,
            type: 'PUT',
            data: { nbHeures: nbHeures },
            success: function(response) {
                Toast.fire({
                    icon: 'success',
                    title: 'Modification réussie!'
                });
                resolve(response); // On résout la Promise avec la réponse
            },
            error: function(error) {
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la modification!'
                });
                reject(error); // On rejette la Promise en cas d'erreur
            }
        });
    });
}

function addImputation(codeId, nbHeures) {
    Toast.fire({
        icon: 'info',
        title: 'Ajout en cours...'
    });
    return new Promise((resolve, reject) => {
        $.ajax({
            url: "{{ path('app_impute_create') }}",
            type: 'POST',
            data: {codeId: codeId, nbHeures: nbHeures },
            success: function(response) {
                Toast.fire({
                    icon: 'success',
                    title: 'Ajout réussi!'
                });
                resolve(response); // On résout la Promise avec la réponse
            },
            error: function(error) {
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de l\'ajout!'
                });
                reject(error); // On rejette la Promise en cas d'erreur
            }
        });
    });
}


getImputations().then(imputations => {
    fillImputations(imputations);
});

$(document).on('click', '.phase-item', function() {
    $('.phase-item').removeClass('active');
    $(this).addClass('active');
    var phaseId = $(this).data('phase-id');
    getImputations(phaseId).then(imputations => {
        if (imputations.length > 0){
            highlightImputation(imputations);
        }else{
            $('.imputation-item').removeClass('active');
            $.ajax({
                url: "{{ path('app_projet_codes') }}",
                type: 'POST',
                data: { phase_id: phaseId },
                success: function(response) {
                    var $select = $('#code');
                    $select.empty();

                    response.codes.sort((a, b) => a.title.localeCompare(b.title));
                    $.each(response.codes, function(index, code) {
                        if (code.workCenter == user_work_center){
                            $('<option>')
                                .val(code.id)
                                .text(code.workCenter + ' - ' + code.title)
                                .attr('selected', 'selected')
                                .appendTo($select);
                        }else{
                            $('<option>')
                                .val(code.id)
                                .text(code.workCenter + ' - ' + code.title)
                                .appendTo($select);
                        }
                    });
                    {% if app.user.titre == "Chef de Projets" or app.user.isManager %}
                        $('#addImputationManagerModal').modal('show');
                    {% else %}
                    $('#addImputationModal').modal('show');
                    {% endif %}
                }
            });
        }
    });
});

$(document).on('click', '.projet-item', function() {
    $('.projet-item').removeClass('active');
    $(this).addClass('active');
    var projetId = $(this).data('projet-id');
    highlightImputation([]);
    getPhases(projetId);
});

$(document).on('click', '.editNbHeures', function() {
    var imputationId = $(this).data('imputation-id');
    var value = $(this).data('value');
    $('#imputationId').val(imputationId);
    $('#nbHeures').val(value);
    $('#editNbHeuresModal').modal('show');
});

$(document).on('click', '#saveNbHeures', function() {
    var imputationId = $('#imputationId').val();
    var nbHeures = $('#nbHeures').val();
    editNbHeures(imputationId, nbHeures).then(response => {
        getImputations().then(imputations => {
            fillImputations(imputations);
        });
        $('#editNbHeuresModal').modal('hide');
    });
});

$(document).on('click', '#addImputation', function() {
    var codeId = $('#code').val();
    var nbHeures = $('#nbHeures2').val();
    addImputation(codeId, nbHeures).then(response => {
        getImputations().then(imputations => {
            fillImputations(imputations);
        });
        $('#addImputationModal').modal('hide');
    });
});

$(document).on('click', '.delete-impute', function() {
    var imputationId = $(this).data('imputation-id');
    Swal.fire({
        title: 'Êtes-vous sûr?',
        text: "Vous ne pourrez pas revenir en arrière!",
        showCancelButton: true,
        confirmButtonText: 'Oui supprimer!',
        cancelButtonText: 'Non',
        customClass: {
            confirmButton: 'btn btn-sm btn-primary',
            cancelButton: 'btn btn-sm btn-danger'
        },
    }).then((result) => {
        if (result.isConfirmed) {
            Toast.fire({
                icon: 'info',
                title: 'Suppression en cours...'
            });
            $.ajax({
                url: "{{ path('app_impute_delete', { 'id': 'edit_me' }) }}".replace('edit_me', imputationId),
                type: 'DELETE',
                success: function(response) {
                    Toast.fire({
                        icon: 'success',
                        title: 'Suppression réussie!'
                    });
                    getImputations().then(imputations => {
                        fillImputations(imputations);
                    });
                }
            });
        }
    });
});

$(document).on('click', '.edit-commentaire', function(e) {
    e.stopPropagation();
    var phaseId = $(this).data('phase-id');
    console.log($(this).attr('value'));
    Swal.fire({
        title: 'Modifier le commentaire',
        input: 'text',
        inputAttributes: {
            autocapitalize: 'off',
        },
        inputValue: $(this).attr('value'),
        showCancelButton: true,
        confirmButtonText: 'Enregistrer',
        cancelButtonText: 'Annuler',
        showLoaderOnConfirm: true,
        customClass: {
            confirmButton: 'btn btn-sm btn-primary',
            cancelButton: 'btn btn-sm btn-danger'
        },
        preConfirm: (commentaire) => {
            Toast.fire({
                icon: 'info',
                title: 'Modification en cours...'
            });
            return $.ajax({
                url: "{{ path('app_phase_edit_commentaire', { 'id': 'edit_me' }) }}".replace('edit_me', phaseId),
                type: 'PUT',
                data: { commentaire: commentaire },
                success: function(response) {
                    Toast.fire({
                        icon: 'success',
                        title: 'Commentaire modifié!'
                    });
                    getPhases($('.projet-item.active').data('projet-id'));
                }
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    });
});

{% if app.user.titre == "Chef de Projets" or app.user.titre == "Cheffe de Projets" %}
$(document).on('click', '.manualclose', function(e) {
    e.stopPropagation();
    var phaseId = $(this).data('phase-id');
    $.ajax({
        url: "{{ path('app_phase_manual_close', { 'id': 'edit_me' }) }}".replace('edit_me', phaseId),
        type: 'PUT',
        success: function(response) {
            getPhases($('.projet-item.active').data('projet-id'));
            getImputations().then(imputations => {
                fillImputations(imputations);
            });
        }
    });
});
{% endif %}


function addImputationManager(userId, codeId, nbHeures) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: "{{ path('app_impute_create') }}",
            type: 'POST',
            data: {
                userId: userId,
                codeId: codeId,
                nbHeures: nbHeures
            },
            success: function(response) {
                resolve(response);
            },
            error: function(error) {
                reject(error);
            }
        });
    });
}

$(document).on('click', '#addImputationManager', function() {
    var userId = $('#users').val();
    var codeId = $('#code').val(); // ou '#codeMaster' selon le select que vous souhaitez utiliser pour l'admin
    var nbHeures = $('#nbHeuresAdmin').val(); // On récupère la valeur du champ admin

    addImputationManager(userId, codeId, nbHeures)
        .then(response => {
            getImputations().then(imputations => {
                fillImputations(imputations);
            });
            $('#addImputationManagerModal').modal('hide');
        })
        .catch(error => {
            console.error("Erreur lors de la création de l'imputation:", error);
        });
});

</script>
{% endblock %}