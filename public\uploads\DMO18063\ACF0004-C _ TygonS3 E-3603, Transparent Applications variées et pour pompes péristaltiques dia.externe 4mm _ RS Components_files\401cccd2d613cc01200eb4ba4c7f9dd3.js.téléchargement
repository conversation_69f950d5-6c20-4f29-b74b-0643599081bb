Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.on("mousedown",".cartButton",function(){Bootstrapper.ensEvent.trigger("Product Page Add to cart",this)},true)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.on("mousedown",".cartButton",function(){Bootstrapper.ensEvent.trigger("Product add to basket",this)},true)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function text(){try{var page=window.rs?window.rs.web.digitalData.page_type:"";if(page&&page.match("new product")){var result=document.getElementsByClassName("stock-msg-content")[0].textContent?document.getElementsByClassName("stock-msg-content")[0].textContent:"";return result.trim()}else return document.getElementsByClassName("floatLeft stockMessaging availMessageDiv bottom5")[0].textContent?
document.getElementsByClassName("floatLeft stockMessaging availMessageDiv bottom5")[0].textContent:""}catch(e){return"error"}},load:"page",trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"Products - Web Stock Levels",collection:"All Pages",source:"Manage",priv:"false"},{id:"13809"})},13809)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){var productVideoThumbnail=document.getElementsByClassName("ll-video-thumbnail");if(productVideoThumbnail.length>0)return"product video available";else return"no video"},load:"page",trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"Products - Product video exists",collection:"Product Page",
source:"Manage",priv:"false"},{id:"23240"})},23240)},-1,-1);