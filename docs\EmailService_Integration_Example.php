<?php

/**
 * Exemple d'intégration du service EmailService dans DMOController
 * 
 * Ce fichier montre comment intégrer le service EmailService dans les méthodes
 * existantes du contrôleur DMO pour envoyer automatiquement des emails lors
 * des changements de statut.
 */

namespace App\Controller;

use App\Service\EmailService;
// ... autres imports

class DMOController extends AbstractController
{
    /**
     * Exemple d'intégration dans la méthode de création d'une DMO
     */
    public function create(
        Request $request,
        EntityManagerInterface $entityManager,
        EmailService $emailService // Injection du service
    ): Response {
        // ... code existant pour la création ...

        // Après la création de la DMO
        $dmo->setDecision('CREATED');
        $dmo->setLastUpdateDate(new \DateTimeImmutable());
        $dmo->setSpentTime(0);
        $dmo->setRequestor($user);

        $entityManager->persist($dmo);
        $entityManager->flush();

        // Générer l'identifiant DMO après la persistance
        $this->generateDmoIdentifier($dmo, $entityManager);

        // NOUVEAU : Envoyer l'email de création
        try {
            $emailService->sendDecisionEmail($dmo, ''); // Pas d'ancienne décision pour une création
        } catch (\Exception $e) {
            // Logger l'erreur mais ne pas bloquer le processus
            $this->logger->error('Erreur envoi email création DMO: ' . $e->getMessage());
        }

        return $this->json([
            'status' => 'success',
            'message' => 'DMO créée avec succès',
            'dmo_id' => $dmo->getDmoId()
        ]);
    }

    /**
     * Exemple d'intégration dans la méthode de mise à jour d'une DMO
     */
    public function update(
        int $id,
        Request $request,
        EntityManagerInterface $entityManager,
        EmailService $emailService // Injection du service
    ): JsonResponse {
        $dmo = $entityManager->getRepository(DMO::class)->find($id);
        
        if (!$dmo) {
            return new JsonResponse(['status' => 'error', 'message' => 'DMO non trouvée'], 404);
        }

        // Sauvegarder l'ancienne décision et le statut pour les notifications
        $oldDecision = $dmo->getDecision();
        $oldStatus = $dmo->isStatus() ? 'Open' : 'Closed';

        // ... code existant pour la mise à jour des champs ...

        $action = $request->request->get('action');
        switch ($action) {
            case 'accept':
                $dmo->setDecision('ACCEPTED');
                break;
            case 'reject':
                $dmo->setDecision('REJECTED');
                $dmo->setStatus(false);
                $dmo->setDateEnd(new \DateTimeImmutable());
                break;
            case 'under-review':
                $dmo->setDecision('UNDER REVIEW');
                break;
            case 'close':
                $dmo->setDecision('ACCEPTED');
                $dmo->setStatus(false);
                $dmo->setDateEnd(new \DateTimeImmutable());
                break;
        }

        $dmo->setLastUpdateDate(new \DateTimeImmutable());
        $entityManager->flush();

        // NOUVEAU : Envoyer les emails de notification
        try {
            // Email de changement de décision
            if ($oldDecision !== $dmo->getDecision()) {
                $emailService->sendDecisionEmail($dmo, $oldDecision);
            }

            // Email de clôture si le statut a changé vers fermé
            $currentStatus = $dmo->isStatus() ? 'Open' : 'Closed';
            if ($oldStatus !== $currentStatus && $currentStatus === 'Closed') {
                $emailService->sendClosureEmail($dmo, $oldStatus);
            }
        } catch (\Exception $e) {
            // Logger l'erreur mais ne pas bloquer le processus
            $this->logger->error('Erreur envoi email mise à jour DMO: ' . $e->getMessage());
        }

        return new JsonResponse([
            'status' => 'success',
            'message' => 'DMO mise à jour avec succès'
        ]);
    }

    /**
     * Méthode dédiée pour changer le statut d'une DMO avec notification
     */
    #[Route('/dmo/{id}/change-status', name: 'app_dmo_change_status', methods: ['POST'])]
    public function changeStatus(
        int $id,
        Request $request,
        EntityManagerInterface $entityManager,
        EmailService $emailService
    ): JsonResponse {
        $dmo = $entityManager->getRepository(DMO::class)->find($id);
        
        if (!$dmo) {
            return new JsonResponse(['status' => 'error', 'message' => 'DMO non trouvée'], 404);
        }

        $newDecision = $request->request->get('decision');
        $reason = $request->request->get('reason', ''); // Pour les rejets

        if (!in_array($newDecision, ['CREATED', 'UNDER REVIEW', 'ACCEPTED', 'REJECTED'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Décision invalide'], 400);
        }

        $oldDecision = $dmo->getDecision();
        $oldStatus = $dmo->isStatus() ? 'Open' : 'Closed';

        // Mettre à jour la décision
        $dmo->setDecision($newDecision);
        
        // Gérer les cas spéciaux
        if ($newDecision === 'REJECTED') {
            $dmo->setStatus(false);
            $dmo->setDateEnd(new \DateTimeImmutable());
            if ($reason) {
                $dmo->setExAssessment($reason);
            }
        }

        $dmo->setLastUpdateDate(new \DateTimeImmutable());
        $entityManager->flush();

        // Envoyer les notifications
        try {
            // Email de changement de décision
            $emailService->sendDecisionEmail($dmo, $oldDecision);

            // Email de clôture si nécessaire
            $currentStatus = $dmo->isStatus() ? 'Open' : 'Closed';
            if ($oldStatus !== $currentStatus && $currentStatus === 'Closed') {
                $emailService->sendClosureEmail($dmo, $oldStatus);
            }

            $message = 'DMO mise à jour et notification envoyée avec succès';
        } catch (\Exception $e) {
            $this->logger->error('Erreur envoi email changement statut DMO: ' . $e->getMessage());
            $message = 'DMO mise à jour avec succès, mais erreur lors de l\'envoi de la notification';
        }

        return new JsonResponse([
            'status' => 'success',
            'message' => $message,
            'dmo' => [
                'id' => $dmo->getDmoId(),
                'decision' => $dmo->getDecision(),
                'status' => $dmo->isStatus() ? 'Open' : 'Closed'
            ]
        ]);
    }

    /**
     * Méthode pour renvoyer une notification email
     */
    #[Route('/dmo/{id}/resend-notification', name: 'app_dmo_resend_notification', methods: ['POST'])]
    public function resendNotification(
        int $id,
        EntityManagerInterface $entityManager,
        EmailService $emailService
    ): JsonResponse {
        $dmo = $entityManager->getRepository(DMO::class)->find($id);
        
        if (!$dmo) {
            return new JsonResponse(['status' => 'error', 'message' => 'DMO non trouvée'], 404);
        }

        try {
            // Renvoyer l'email correspondant à la décision actuelle
            $emailService->sendDecisionEmail($dmo, 'RESEND');
            
            return new JsonResponse([
                'status' => 'success',
                'message' => 'Notification renvoyée avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Erreur lors du renvoi de la notification: ' . $e->getMessage()
            ], 500);
        }
    }
}

/**
 * NOTES D'INTÉGRATION :
 * 
 * 1. Ajouter EmailService dans le constructeur ou les méthodes qui en ont besoin
 * 2. Toujours encapsuler les appels au service dans des try-catch
 * 3. Logger les erreurs sans bloquer le processus principal
 * 4. Vérifier que la DMO a un demandeur avec un email valide
 * 5. Sauvegarder les anciens états avant modification pour les comparaisons
 * 
 * EXEMPLE D'UTILISATION DANS UNE VUE TWIG :
 * 
 * <button onclick="changeDmoStatus({{ dmo.id }}, 'ACCEPTED')" class="btn btn-success">
 *     Accepter
 * </button>
 * 
 * <script>
 * function changeDmoStatus(dmoId, decision) {
 *     fetch(`/dmo/${dmoId}/change-status`, {
 *         method: 'POST',
 *         headers: {
 *             'Content-Type': 'application/x-www-form-urlencoded',
 *         },
 *         body: `decision=${decision}`
 *     })
 *     .then(response => response.json())
 *     .then(data => {
 *         if (data.status === 'success') {
 *             alert('DMO mise à jour et notification envoyée !');
 *             location.reload();
 *         } else {
 *             alert('Erreur: ' + data.message);
 *         }
 *     });
 * }
 * </script>
 */
