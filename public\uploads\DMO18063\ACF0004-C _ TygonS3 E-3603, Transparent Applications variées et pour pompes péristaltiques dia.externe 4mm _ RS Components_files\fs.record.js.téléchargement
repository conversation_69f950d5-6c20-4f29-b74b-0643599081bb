/***************************************
* @preserve
* ForeSee Web SDK: record
* Built February 23, 18 13:09:14
* Code version: 19.6.1
* Template version: 19.6.1
***************************************/
_fsDefine(["require","fs",_fsNormalizeUrl("$fs.utils.js"),"recordconfig"],function(t,e,i,r){function s(t,e,r,s,o){this.jrny=n.jrny=new i.Journey(s.id||i.getRootDomain(),i.APPID.REPLAY,r.uid||r.get("rid")||"record_userId",t),this.jrny.addEventsDefault("properties",{fs_pageViews:[r.get("pv")]}),this.initialize.apply(this,arguments)}var n={},o=window,a={RECORDER_SESSION_STARTED:"fs_sessionStarted",RECORDER_CANCELED:"fs_recorderCanceled",RECORDER_STOP_OLDBROWSER:"fs_recorderStoppedOldBrowser",RECORDER_TRANSMIT_FAILED:"fs_recorderTransmitFailed"},c={_trackers:[]};c.Tracker=function(t,e,r,s,n,o){this.recorder=t,this.throttleTime=s,this.recFunction=n,this.subject=e,o||this._capture({},e),i.Bind(e,r,function(t,e){return function(i){t._capture&&t._capture(i,e),i=null,e=null}}(this,e)),c._trackers.push(this),e=null,t=null,r=null,n=null},c.Tracker.prototype.merge=function(t,e,r,s){i.Bind(e,r,function(t,e,i){return function(r){t._capture&&t._capture(r,e,i)}}(this,e,t)),t=null,r=null},c.Tracker.prototype._capture=function(t,r,s){clearTimeout(this.lastCapThrottle);var n=s||this.recorder;if(!this.lastCap||i.now()-this.lastCap>this.throttleTime)this.recFunction.call(this,t,r,n),this.lastCap=i.now();else{var o={};for(var a in t)6==a.length&&"layerX"!=a&&"layerY"!=a&&(o[a]=t[a]);this.lastCapThrottle=setTimeout(function(t,i,r,s){return e.isDefined(i.clientX)&&(i.sX=i.clientX,i.sY=i.clientY),function(){i.delayed=!0,t._capture&&t._capture(i,r,s)}}(this,o,r,n),this.throttleTime)}n=null,s=null,t=null,r=null},c.Tracker.prototype.dispose=function(){this.lastCapThrottle&&clearTimeout(this.lastCapThrottle),e.dispose(this)};var h=c.Tracker,l={SESSIONID:"rpid",GLOBALSESSIONID:"mid",TRANSMITTING:"rt",CANCELED:"cncl",CANCELEDPERMANENT:"rcp",SESSION:"SESSION",DATA:"DATA",GLOBALREFRESHTIME:"grft"},d={_frames:[]};d.TrackFrame=function(t,e,r){d._getFrameByXPath(t)||d._frames.push({xp:t,nd:e,rc:r,sid:d._frames.length+"_",id:i.generateGUID(),initialized:!1})},d._getFrameByXPath=function(t){for(var e=t.join("/"),i=0;i<d._frames.length;i++)if(d._frames[i].xp.join("/")==e)return d._frames[i]},d._getFrameById=function(t){for(var e=0;e<d._frames.length;e++)if(d._frames[e].id==t)return d._frames[e]},d.BeginTrackingChildFrames=function(t){setInterval(function(){for(var e=0;e<d._frames.length;e++){var i=d._frames[e];!i.initialized&&i.nd.contentWindow&&i.nd.contentWindow.postMessage(JSON.stringify({cxr:!0,id:i.id,xp:i.xp,sid:i.sid,sp:I.getPosition(i.nd,t.win)}),"*")}},400),i.Bind(window,"message",function(i){if(i.data.timetounload)for(var r=0;r<d._frames.length;r++)d._frames[r].id===i.data.frameId&&(d._frames[r].initialized=!1);if(i.data=i.data+"",i.data&&e.isFunction(i.data.indexOf)&&i.data.indexOf("_cxrXDS_")>-1){var s;try{s=JSON.parse(i.data)}catch(t){return}if(s){var n=d._getFrameById(s.id);if(n){var o=t.getLogger();n.initialized=!0,o._data&&o._data.length>0?o._data=o._data+","+s.data:o._data=s.data}}}});new h(t,t.win,"record:scroll",500,function(e,r,s){for(var n=(i.getScroll(t.win),0);n<d._frames.length;n++){var o=d._frames[n];o.initialized&&o.nd.contentWindow&&o.nd.contentWindow.postMessage(JSON.stringify({cxsp:!0,sp:I.getPosition(o.nd,t.win)}),"*")}})},d.SendDataToParentFrame=function(t,e,i){t.postMessage(JSON.stringify({_cxrXDS_:!0,id:e,data:i}),"*")};var u={};u.increment=function(t,e){try{var i=t.get("meta")||{};i[e]=(i[e]||0)+1,t.set("meta",i)}catch(t){}},u.set=function(t,e,i){try{var r=t.get("meta")||{};r[e]=i,t.set("meta",r)}catch(t){}};var g=function(t,e,config){var i=g,r=t.document;return i.ProcessHTML(i._getDocType(r)+r.querySelectorAll("html")[0].outerHTML,r,e,!!config.advancedSettings.pii.useWhiteListing,config)},p=g;g.ProcessHTML=function(t,e,i,r,config){return S.getCacheableObject(g._scrubHTML(t,r,config),i)},g._scrubHTML=function(t,i,config){var r,s=g,n=config.advancedSettings||{};if(t=Array.isArray(t)?t[0]:t||"",n.regexScrub)for(r=0;r<n.regexScrub.length;r++)t=t.replace(n.regexScrub[r],"");return t=s._removeInnerScriptContent(t),t=s._removeZeroWidthEntities(t),t=s._fixPreTags(t),t=s._fixViewState(t),t=s._stripOptionTags(t),t=s._convertSpecialChars(t,config),t=s._stripInputValues(t),t=i?s._whiteListBlock(t,i):s._maskFSRHiddenBlocks(t,i),i||e.isDefined(n.keepComments)&&!0===n.keepComments||(t=s._removeComments(t)),t};var f=function(t){return t[0].indexOf("fsrHiddenBlock")<0?"":t[0]};g._removeComments=function(t){return/<!--/gim.test(t)?p._superReplace(t,/(<!--)[\w\W]*?(-->)/gim,f):t},g._convertSpecialChars=function(t,config){if(config.advancedSettings){var i,r=config.advancedSettings.specialMappings;if(e.isDefined(r))for(i=0,len=r.length;i<len;i++)t=t.replace(r[i][0],r[i][1])}return t},g._removeInnerScriptContent=function(t){return/script/gim.test(t)?p._superReplace(t,/(<script[^>]*?>)[\s\S]*?(<\/[\w]*script>)/gim,function(t){return t[1]+t[2]}):t},g._maskFSRHiddenBlocks=function(t,e){return e?this._superReplace(t,new RegExp(".+","mig"),p._maskBlock):t.indexOf("fsrHiddenBlock")>=0?g._superReplace(t,new RegExp("\x3c!--(\\W)*fsrHiddenBlockStart[\\w\\W]*?fsrHiddenBlockEnd(\\W)*--\x3e","mig"),p._maskBlock):t},g._removeZeroWidthEntities=function(t){return t.replace(/(\u200B)|(\u200C)|(\u200D)|(\uFEFF)/g,"")},g._fixPreTags=function(t){return t.indexOf("/pre")>-1||t.indexOf("/PRE")>-1?p._superReplace(t,new RegExp("[\\s\\S]*?(?:\\/pre\\s*>)|[\\s\\S]+","mig"),p._removeFormat):t},g._fixViewState=function(t){return t.indexOf("VIEWSTATE")>-1?t.replace(/<input[^>]*name=["']?__VIEWSTATE[^>]*>/gim,'<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" />'):t};var m=function(t){return t[0].match(/<\/select[^>]*>/gim)?t[0]:""};g._stripOptionTags=function(t){return/select/gim.test(t)?p._superReplace(t,/(<option[^>]*>[\s\S]*?<\/[\w]*option>)/gim,m):t};var _=function(t,e,i){if(e)return e+i.replace(/[\w\d\s]/g,"*")+'"'};g._stripInputValues=function(t){return t.length>0&&/value/.test(t)?t.replace(/(<(?:\s)*?input(?:[^>]+)value=\")([^>\"]*)\"/gim,_):t},g._superReplace=function(t,i,r){for(var s,n=i.exec(t),o="",a=0;e.isDefined(n);)o+=t.substring(a,n.index),s=r(n),o+=s,a=n.index+n[0].length,n=i.exec(t);return o+=t.substring(a,t.length)};var T=function(t,e,i,r){return i||r||"*"};g._maskBlock=function(t){var e=t instanceof Array?t[0]:t||"";return e.length>0?e.replace(/^(?!<!--.*)(>)|(&[^\s;]*;)|(<[^<>]*?>)|(<)^(?!<--.*)|\w/g,T).replace(/&((?!nbsp| |amp|quot).)*?;/g,"*"):""},g._removeFormat=function(t){var e=0,i=t[0];return i&&(i="",e=g._regexIndexOf(t[0],new RegExp("<\\s*?pre","mig")),i+=t[0].substring(0,e).replace(/\t/g," ").replace(/\s+/g," "),i+=t[0].substring(e)),i},g._regexIndexOf=function(t,e,i){var r=t.substring(i||0).search(e);return r>=0?r+(i||0):r},g._regexLastIndexOf=function(t,e,i){for(var r=i,s=g.regexIndexOf(t,e,r);s>-1;)r=s,s=g.regexIndexOf(t,e,r+1);return r},g._getDocType=function(t){var i,r="",s=t.doctype,n=t.childNodes,o=0;if(O._browser.isIE&&("CSS1Compat"!=t.compatMode||5==t.documentMode))return r;if(s){try{if((i=(new XMLSerializer).serializeToString(s).toString())&&i.length>0)return i}catch(t){}r="<!DOCTYPE HTML",s.publicId&&(r=r+' PUBLIC "'+s.publicId+'"'),s.systemId&&(r=r+' SYSTEM "'+s.systemId+'"'),r+=">"}else if(n[o].text){for(;n[o].text&&(0===n[o].text.indexOf("\x3c!--")||0===n[o].text.indexOf("<?xml"));)o++;e.isDefined(n[o].text)&&0===e.toLowerCase(n[o].text).indexOf("<!doctype")&&(r=n[o].text)}return r},g._whiteListBlock=function(t,e){var i=t.indexOf("\x3c!--fsrWhiteListStart"),r="";return e?i>=0?r+=g._maskBlock(t.substring(0,i))+g._superReplace(t.substring(i),new RegExp("\x3c!--(\\W)*fsrWhiteListEnd(\\W)*--\x3e([\\W\\D\\S](?!!--(\\W)*fsrWhiteListStart(\\W)*--\x3e))*","mig"),g._maskBlock):this._superReplace(t,new RegExp(".+","mig"),g._maskBlock):t};var S={_browser:null,_cacheList:[]};S.getCacheableObject=function(t,r){var s=S._browser.isMobile;r&&r.join&&(r=r.join(","));var n,o={},a=S,c=a._cacheList;for(n=c.length-1;n>=0;n--)if(c[n].str==t){o.uid=c[n].uid;break}if(!e.isDefined(o.uid)){if(o.uid=i.generateGUID(),o.kl=r,r&&t.length>100)for(n=c.length-1;n>=0;n--)if(c[n].kl==r){var h=y(c[n].str,t);o.diff={uid:c[n].uid,d:h};break}e.isDefined(o.diff)?(!s||c.length<500)&&(c[c.length]={str:t,uid:o.uid,kl:o.kl}):(o.str=t,(!s||c.length<500)&&(c[c.length]=o))}return o},S.reset=function(){S._cacheList=[]};var y=function(t,e){for(var i=t,r=e,s=t.length,n=e.length,o=s>n?n:s,a=o,c=0,h=0,l=0,d=0;a-c>15;)d=Math.round((a-c)/2),l=a-d,i.substr(0,l)!=r.substr(0,l)?a=l:c=l;var u=e.substr(c);for(a=u.length;a-h>15;)d=Math.round((a-h)/2),l=a-d,u.substr(u.length-l)!=i.substr(i.length-l)?a=l:h=l;return{o:c,c:h,r:e.substring(c,e.length-h)}},E=[],v=function(t,r,s,config){this.config=config,this.recorder=s,this.browser=r,this.cors=new i.CORS(r,t.fstg),this.isIframeMode=s.isIframeMode;var n=t.get([l.GLOBALSESSIONID,l.TRANSMITTING,l.CANCELED,"pv"]),o=n[l.GLOBALSESSIONID],a=n[l.TRANSMITTING],c=n[l.CANCELED],h=i.getScreenResolution();this.stg=t,this.pageViews=t.get("pv"),this.failCount=0,this.transportid=0,this.lastTransmissionTime=0,this.holdTransmits=!1,this.transmitting=!1,this.canceled=!1,this.siteid=config.clientId||"unk",this.domain=document.domain.toString(),this.StorageReady=new i.FSEvent,this.TransportReady=new i.FSEvent,this._quickXpathCache={},this._reset(),this.isIframeMode?(this.TransportReady.fire(),this.transmitting=!0,this.StorageReady.fire(),this.iFrameCommitItv=setInterval(e.proxy(this._flushAndTransmit,this),1e3)):(this.log(s,v.EVENT_TYPES.PAGE_MARKER,{dtm:e.startTS,ofs:(new Date).getTimezoneOffset(),v:r.browser.name,dv:r.browser.version,sid:config.clientId,r:"W3C",l:config.layout,m:r.isMobile,f:document.referrer.toString(),sz:{w:h.w,h:h.h}},-1),i.Bind(window,"unload",e.proxy(function(){this.commitData()},this)),this._backup_storage=new i.DomStorage(this.siteid,!1,!1,r),this._storage=new i.WindowStorage(this.siteid,!1,r),this._syncStorage(),this._syncSession(o,!!a,!!c),this._syncData(),this.startStateMonitor(),this.startStorageMonitor(),this.TransportReady.subscribe(e.proxy(function(){this.StorageReady.fire(),this.transmitting&&this.startHeartbeat(),this._flushAndTransmit()},this),!0,!0),this.TransportReady.fire(),this.recorder.ready.subscribe(e.proxy(function(){this.recorder.EmergencySendRequired.subscribe(e.proxy(function(){this._flushAndTransmit(!0)},this))},this),!0,!0)),s=null};v.prototype.commitData=function(){this._flush(),this.transmitting&&this._transmit(),this._storage&&(this._storage.set(l.SESSION,this._session),this._storage.setBlob(this._data),this._storage.commit()),this._backup_storage&&(this._backup_storage.set(l.SESSION,this._session),this._backup_storage.setBlob(this._data),this._backup_storage.commit())},v.prototype.dispose=function(){E=[],this._flush(),this.cors.dispose(),this.iFrameCommitItv&&clearInterval(this.iFrameCommitItv),this.recorder.EmergencySendRequired.unsubscribeAll(),this.recorder.EmergencySendRequired=null,this.stopStorageMonitor(),this.stopStateMonitor()},v.prototype.setTransmitOK=function(){this.canceled||(this.transmitting=!0,this._session[l.TRANSMITTING]=!0,this.stg.set(l.TRANSMITTING,!0),this._flushAndTransmit(),this.startHeartbeat())},v.prototype.setTransmitNotOK=function(){this.stopHeartbeat(),this.transmitting=!1,this._session[l.TRANSMITTING]=!1,this.stg.set(l.TRANSMITTING,!1)},v.prototype.cancelRecord=function(t){this.setTransmitNotOK(),this.stopStateMonitor(),this.stopStorageMonitor(),this.canceled=!0,this._session[l.CANCELED]=!0,this.stg.set(l.CANCELED,!0),t&&this.stg.set(_SESSION_SYMBOLS.CANCELEDPERMANENT,this.cancel),this.iFrameCommitItv&&clearInterval(this.iFrameCommitItv),this._reset()},v.prototype._syncStorage=function(){var t,e,i=this._storage,r=this._backup_storage;i&&r&&i.isNew()&&!r.isNew()&&(t=r.get(l.SESSION),i.set(l.SESSION,t),e=r.getBlob(),i.setBlob(e))},v.prototype._syncSession=function(t,r,s){var n=this._storage;this._backup_storage;if(n){if(this.refreshLastTime=this.stg.get(l.GLOBALREFRESHTIME),this.refreshLastTime){this.refreshLastTime=parseInt(this.refreshLastTime);i.now()-this.refreshLastTime>36e5&&(t=null,n.erase(l.SESSION),this.stg.erase(l.GLOBALSESSIONID))}n.get(l.SESSION)?(this._session=n.get(l.SESSION),this.sessionid=this._session[l.SESSIONID],this.gsessionid=this._session[l.GLOBALSESSIONID],n.set(l.SESSIONID,this.sessionid),e.isDefined(r)?(this.transmitting=r,this._session[l.TRANSMITTING]=this.transmitting):(this.transmitting=this._session[l.TRANSMITTING],this.stg.set(l.TRANSMITTING,this.transmitting)),e.isDefined(s)?(this.canceled=s,this._session[l.CANCELED]=this.canceled):(this.canceled=this._session[l.CANCELED],this.stg.set(l.CANCELED,this.canceled)),n.set(l.SESSION,this._session)):(this._session={},this.sessionid=i.generateGUID(),this._session[l.SESSIONID]=this.sessionid,t||(t=i.generateGUID()),this.gsessionid=t,this._session[l.GLOBALSESSIONID]=this.gsessionid,this.transmitting=r,this._session[l.TRANSMITTING]=this.transmitting,this.stg.set(l.TRANSMITTING,this.transmitting),this.canceled=s,this._session[l.CANCELED]=this.canceled,this.stg.set(l.CANCELED,this.canceled),this.stg.set(l.GLOBALSESSIONID,t),n.set(l.SESSION,this._session),n.commit()),this.refreshLastTime=i.now(),this._session[l.GLOBALREFRESHTIME]=this.refreshLastTime,this.stg.set(l.GLOBALREFRESHTIME,this.refreshLastTime)}},v.prototype._syncData=function(){this._storage?this._data=this._storage.getBlob():this._backup_storage&&(this._data=this._backup_storage.getBlob())},v.prototype._flushAndTransmit=function(t){if((this._storage||this._backup_storage)&&!this.canceled||this.isIframeMode){var e=this.testStorageLimit();this.transmitting&&(i.now()-this.lastTransmissionTime>5e3||t)||e||this.isIframeMode?(this._flush(),this._transmit()):t&&this.browser.isMobile&&this.commitData()}},v.prototype.processImmediately=function(t){this.cors.send({method:"GET",url:i.sign(e.config.recUrl+"process/"+e.enc(this.gsessionid)+"?delay="+(t||0)),failure:e.proxy(function(t){},this),success:e.proxy(function(t){},this)})},v.prototype._transmit=function(){if(0!==this._data.length||this.isIframeMode){if(this.isIframeMode)return void(this._data.length>0&&(d.SendDataToParentFrame(this.recorder.iFrameParentFr,this.recorder.ifrid,this._data),this._data=""));if(this.holdTransmits&&i.now()-this.lastTransmissionTime<1e4)return void(this.hasEmergency=!0);this.holdTransmits=!0,this.hasEmergency=!1,this.lastTransmissionTime=i.now();var t,r="corsservice",s=e.toLowerCase(this.siteid.replace(/[- _.&]/g,"")),o='{"data":['+this._data+"]}",c=o.length;u.set("rtp",r.substr(0,1)),!this.config.advancedSettings.skipCompression&&c>1e3&&c<1e6&&(o=i.Compress.fragmentAndCompress(o)),t="datalen:"+o.length+",time:"+i.now(),u.increment("rta"),this.cors.send({action:"data",method:"POST",contentType:"text/plain;charset=UTF-8",encoding:e.enc(I.getEncoding()),version:5,gSessionId:this.gsessionid,sessionId:this.sessionid,domain:this.domain||window.location.hostname,siteId:s,url:e.config.recUrl+r+"?action=data&metadata="+e.enc(t)+"&encoding="+e.enc(I.getEncoding())+"&session_id="+e.enc(this.sessionid)+"&global_session_id="+e.enc(this.gsessionid)+"&domain="+e.enc(this.domain)+"&site_id="+e.enc(s)+"&version=5.0&cachebust="+Math.random(),skipEncode:!0,data:o,failure:e.proxy(function(t){n.jrny.addEventString(a.RECORDER_TRANSMIT_FAILED),u.increment("rtf"),this.failCount++,this.holdTransmits=!1;try{t=JSON.parse(t)}catch(t){}if(e.isDefined(t.status)){var i=Math.abs(parseInt(t.status));u.increment("rtf"+i)}if(this.failCount>10)return u.set("rtcr",1),u.set("rtcp",this.pageViews),void this.cancelRecord();this.hasEmergency&&setTimeout(e.proxy(function(){this._flushAndTransmit(!0)},this),100)},this),success:function(t,i,r){return function(s){u.increment("rts"),t.failCount=0,t.holdTransmits=!1;try{s=JSON.parse(s)}catch(t){}if(e.isDefined(s.status)){var n=parseInt(s.status,10);if(1==n)i==t.transportid?t._data="":t._data.substr(0,r.length)==r&&(t._data=t._data.substr(r.length));else if(2==n)return u.set("rtcr",2),u.set("rtcp",t.pageViews),void t.cancelRecord();t.hasEmergency&&setTimeout(function(){t._flushAndTransmit(!0)},100)}}}(this,this.transportid,this._data+"")})}},v.prototype._ping=function(){if(e.isDefined(this.transport)&&!this.canceled){var t=e.toLowerCase(this.siteid.replace(/[- _.&]/g,""));this.transport||(this.transport=new this.transport),this.cors.send({action:"ping",contentType:"text/plain;charset=UTF-8",sessionId:this.sessionid,siteId:t,url:e.config.recUrl+"corsservice?action=ping&session_id="+e.enc(this.sessionid)+"&site_id="+e.enc(t)+"&cachebust="+Math.random(),skipEncode:!0,data:"",failure:function(){},success:function(){}})}},v.prototype._reset=function(){this._log={start:this._log?i.now():e.startTS,log:[],guid:i.generateGUID()},this._log_size_est=0},v.prototype._flush=function(){if(0!==this._log.log.length){this.transportid++,this._log.log.toJSON=null;var t=JSON.stringify(this._log);t&&t.length>0&&(this._data&&this._data.length>0?this._data=this._data+","+t:this._data=t),this._reset()}},v.prototype.logXPath=function(t,i){if(!this.canceled){if(i=i.join(","),!e.isDefined(this._quickXpathCache[i])){E[E.length]=i;var r=this.recorder.iFramexPathPrefix+(E.length-1);this._quickXpathCache[i]=r,this.log(t,v.EVENT_TYPES.XPATH_CACHE,{idx:r,xp:i},r)}return this._quickXpathCache[i]}},v.prototype.log=function(t,r,s,n,o){if(!this.canceled){var a=0;o||(o=0);var c={x:e.isDefined(n)?n:this.logXPath(t,t.getPath()),e:r,d:s,t:Math.max(0,i.now()-this._log.start-o)};this._log.log[this._log.log.length]=c,r==v.EVENT_TYPES.DOM_SERIALIZE?s.dom.str&&(a=s.dom.str.length,this._log_size_est+=a):r==v.EVENT_TYPES.CSS_SERIALIZE?s.stylesheet?(a=s.stylesheet.length,this._log_size_est+=a):s.v&&(a=Math.round(1*s.v.length),this._log_size_est+=a):r==v.EVENT_TYPES.DOM_MUTATION_NODE_MODIFIED?s.h.str||s.h.diff?(a=Math.round(1*((s.h.str?s.h.str.length:0)+(s.h.diff?s.h.diff.d.r.length:0)+(s.h.kl?s.h.kl.length:0)+(s.h.uid?s.h.uid.length:0))),a+=50,this._log_size_est+=a):(a=84,this._log_size_est+=a):(a=60,this._log_size_est+=a)}},v.prototype.testStorageLimit=function(){return!this.isIframeMode&&this._data.length+this._log_size_est>3e6},v.prototype._monitorState=function(){var t=this.stg.get([l.CANCELED,l.TRANSMITTING]),i=t[l.CANCELED],r=t[l.TRANSMITTING];if(e.isDefined(i)&&this.canceled!=i&&i)return void this.cancelRecord();e.isDefined(r)&&this.transmitting!=r&&(r?this.setTransmitOK():this.setTransmitNotOK())},v.prototype.startHeartbeat=function(){this.heartbeatTransmit||(this._ping(),this.heartbeatTransmit=setInterval(e.proxy(function(){this._ping()},this),45e3))},v.prototype.stopHeartbeat=function(){this.heartbeatTransmit&&(clearInterval(this.heartbeatTransmit),delete this.heartbeatTransmit)},v.prototype.startStateMonitor=function(){this.stateMonitor||(this._monitorState(),this.stateMonitor=setInterval(e.proxy(function(){this._monitorState()},this),2e3))},v.prototype.stopStateMonitor=function(){this.stateMonitor&&(clearInterval(this.stateMonitor),delete this.stateMonitor)},v.prototype.startStorageMonitor=function(){this.storageMonitor||(this.storageMonitor=setInterval(e.proxy(function(){this.log(this.recorder,v.EVENT_TYPES.HEARTBEAT,{ofs:(new Date).getTimezoneOffset()},-1),this._flushAndTransmit()},this),this.browser.isMobile?1e4:2e4))},v.prototype.stopStorageMonitor=function(){this.storageMonitor&&(clearInterval(this.storageMonitor),delete this.storageMonitor)},v.prototype.clearState=function(){this._session=!1,this._storage.kill(),this._backup_storage.kill(),this.commitData()},v.EVENT_TYPES={DOM_SERIALIZE:0,XPATH_CACHE:1,FRAME_SIZE:2,FRAME_SCROLL:3,MOUSE_MOVE:4,WINDOW_MOUSEOUT_MOUSEENTER:5,INPUT_SERIALIZE:6,FOCUS_BLUR:7,KEY_PRESS:8,CARET_INFO:9,VALUE_CHANGED:10,DOM_MUTATION_NODE_MODIFIED:11,DOM_MUTATION_NODE_ATTR_MODIFIED:12,JAVASCRIPT_ERROR:13,MOUSE_CLICK:14,MOUSE_DOWN:15,MOUSE_UP:16,PAGE_MARKER:17,DOC_SIZE:18,SCROLL_EL:19,NOT_RECORDED:20,CSS_SERIALIZE:21,ORIENTATION:22,ZOOM:23,TOUCH:24,INCOMPLETE_INPUT_CAPTURE:25,ORIENTATION_CHANGE:26,CUSTOM_BEHAVIOR:27,CUSTOM_ERROR:28,HEARTBEAT:29};var w={};w.getMapping=function(t,e,i){if(e=e||[],t.parentNode&&(e=w.getMapping(t.parentNode,e)),1==t.nodeType){var r,s;if(t.previousSibling){r=1,s=t.previousSibling;do{1==s.nodeType&&s.nodeName==t.nodeName&&r++,s=s.previousSibling}while(s);1==r&&(r=null)}else if(t.nextSibling){s=t.nextSibling;do{1==s.nodeType&&s.nodeName==t.nodeName?(r=1,s=null):(r=null,s=s.previousSibling)}while(s)}e.push(b(t,r,!!i))}return e};var b=function(t,i,r){var s;return t.getAttribute&&(s=t.getAttribute("id"))&&s.indexOf("yui_")>-1&&(s=null),e.toLowerCase(t.nodeName)+(s&&!r?"[@id='"+s+"']["+(i||1)+"]":i>0?"["+i+"]":"")};w.getNodeXPathPosition=function(t){for(var e=t.nodeType,i=t.tagName,r=t.parentNode,s=0,n=r.childNodes,o=n.length,a=0;o>s&&n[s];){if(n[s].nodeType==e&&(i&&i==n[s].tagName||!i)&&(a++,n[s]==t))return a;s++}return 0};var N=function(t){this._elementCache={},this._callbackTimer=null,this.recorder=t,this.cacheIndex=0,t=null};N.prototype.dispose=function(){e.dispose(this._elementCache),this._elementCache=null,this.recorder=null,clearTimeout(this._callbackTimer)},N.prototype.push=function(t,r,s,n){var o={t:t,c:r,i:s,p:n,d:i.now()};n=n||"",t&&1!=t.nodeType&&(t=t.parentNode),t&&this._elementCache&&(t._fsrKey||(t._fsrKey={id:"_"+this.cacheIndex++}),this._elementCache[t._fsrKey.id+e.toLowerCase(n)]=o,this._callbackTimer||(this._callbackTimer=setTimeout(e.proxy(function(){this.handleEvents()},this),1e3)),t=null,r=null)},N.prototype.handleEvents=function(){this._callbackTimer=null;try{this.recorder.win.document}catch(t){return}var t,r=this._elementCache,s=i.now();for(var n in r)t=r[n],t._fsrKey=null,"object"==typeof t&&t.t&&t.t.nodeName&&"svg"!=t.t.nodeName&&(t.c(s-t.d),e.dispose(t),t=null);e.dispose(this._elementCache),this._elementCache={},r=null},N.prototype.accelerate=function(){this.handleEvents()};var C=[],M=function(t,r){if(!r._fsrTracker){r._fsrTracker=this,C.push(this),this.input=r,this.recorder=t;var s="";if(s=r.getAttribute("type")?r.getAttribute("type"):"text",this._isTextInput()&&(this.lastValue=r.getAttribute("value")||""),((!this._isTextInput()||r.value)&&("checkbox"!=s||r.checked)||r.type&&"select-one"==r.type)&&this.serialize(!0),i.Bind(r,"record:scroll",e.proxy(function(){this.input&&this.recorder.getLogger().log(this.recorder,v.EVENT_TYPES.SCROLL_EL,{t:s,x:this.recorder.getLogger().logXPath(this.recorder,w.getMapping(this.input)),ps:{x:this.input.scrollLeft,y:this.input.scrollTop}})},this)),i.Bind(r,"record:focus",e.proxy(function(){this.input&&(this._isTextInput()&&(this._updateCaret(),this._caretMonitor=setInterval(e.proxy(function(){this.input&&this._updateCaret()},this),350)),this.recorder.getLogger().log(this.recorder,v.EVENT_TYPES.FOCUS_BLUR,{t:s,st:1,x:this.recorder.getLogger().logXPath(this.recorder,w.getMapping(this.input))}))},this)),i.Bind(r,"record:blur",e.proxy(function(t){this.input&&(this._isTextInput()&&clearInterval(this._caretMonitor),this.recorder.getLogger().log(this.recorder,v.EVENT_TYPES.FOCUS_BLUR,{t:s,st:0,x:this.recorder.getLogger().logXPath(this.recorder,w.getMapping(this.input))}))},this)),"checkbox"==s||"radio"==s){var n=e.proxy(function(){this.input&&this._lastCheckedState!=this.input.checked&&(this._lastCheckedState=this.input.checked,this.recorder.getLogger().log(this.recorder,v.EVENT_TYPES.VALUE_CHANGED,{t:s,b:this.input.checked,x:this.recorder.getLogger().logXPath(this.recorder,w.getMapping(this.input))}))},this);i.Bind(r,"record:click",n),i.Bind(t.win,"record:click",n)}"checkbox"!=s&&"radio"!=s&&i.Bind(r,"record:change",e.proxy(function(){if(this.input){this._lastSelectListContents!=this.input.innerHTML&&this.serialize(),this.lastValue=this.input.value;var t=this.input.selectedOptions,e="";t&&t.length>0&&!this._isSecret()&&(e=t[0].innerText),this.recorder.getLogger().log(this.recorder,v.EVENT_TYPES.VALUE_CHANGED,{t:s,si:this.input.selectedIndex,x:this.recorder.getLogger().logXPath(this.recorder,w.getMapping(this.input)),v:e})}},this)),this._isTextInput()&&(this.lastValue=this.input.value,i.Bind(r,"record:keydown",function(t){return function(e){t.input&&(O._browser.isIE&&(e={keyCode:e.keyCode,charCode:e.charCode}),setTimeout(function(t,e){return function(){if(t.input){t._updateCaret();var i=0;if("number"==typeof e.keyCode?i=e.keyCode:"number"==typeof e.which?i=e.which:"number"==typeof e.charCode&&(i=e.charCode),Math.abs(t.lastValue.length-t.input.value.length)>1||t.lastCaretInfo.e-t.lastCaretInfo.s>1)t.serialize();else{0===i&&32==e.charCode&&(i=e.charCode);var r=t.recorder.getLogger().logXPath(t.recorder,w.getMapping(t.input));i<=46||i>=91&&i<96||i>=112&&i<=145?t.recorder.getLogger().log(t.recorder,v.EVENT_TYPES.KEY_PRESS,{t:s,xp:r,sk:i,ps:{x:t.input.scrollTop,y:t.input.scrollLeft}}):t.recorder.getLogger().log(t.recorder,v.EVENT_TYPES.KEY_PRESS,{t:s,xp:r,v:t._filterTextIfApplicable(t.input.value.substr(t.lastCaretInfo.c-1,1)),ps:{x:t.input.scrollTop,y:t.input.scrollLeft}})}t.lastValue=t.input.value}}}(t,e),1))}}(this))),t=null,r=null}};M.prototype.hasChanged=function(){return this._lastCheckedState!=this.input.checked||this.lastValue!=this.input.value},M.prototype._filterTextIfApplicable=function(t){return t&&this._isSecret()?t.replace(/[^ \n\r\t]/g,"*"):t},M.prototype._isSecret=function(){var t=this.input.getAttribute("class")||this.input.getAttribute("className");return!(t&&t.indexOf("fsrVisible")>-1||"reset,submit,button".indexOf(this.input.getAttribute("type"))>-1)},M.prototype._isTextInput=function(){var t="text",r=e.toLowerCase(this.input.tagName),s=["text","password","textarea","number","email","url","search","color","tel",""];return this.input.getAttribute("type")&&(t=this.input.getAttribute("type")),("textarea"==r||i.inArray(t,s))&&"select"!==r},M.prototype.serialize=function(t){var i=e.toLowerCase(this.input.tagName),r=this.input.getAttribute("type");if(r||"input"==(r=e.toLowerCase(this.input.tagName))&&(r="text"),"hidden"!=r){if(t){if(!("radio"!=r&&"checkbox"!=r||this.input.checked))return void(this._lastCheckedState=!1);if(("text"==r||"password"==r||"textarea"==i)&&""===this.input.value)return}var s=this.recorder.getLogger(),n=v.EVENT_TYPES.INPUT_SERIALIZE,o=s.logXPath(this.recorder,w.getMapping(this.input));switch(r="select,textarea".indexOf(i)>-1?i:r){case"textarea":s.log(this.recorder,n,{t:r,x:o,v:this._filterTextIfApplicable(this.input.value)||"",wt:I.getStyle(this.input,this.recorder.win,"width"),ht:I.getStyle(this.input,this.recorder.win,"height")});break;case"radio":case"checkbox":!this.input.checked&&t||(s.log(this.recorder,n,{t:r,x:o,b:this.input.checked}),this._lastCheckedState=this.input.checked);break;case"select":for(var a=[],c=0;c<this.input.options.length;c++)a[a.length]={v:this._filterTextIfApplicable(this.input.options[c].value),t:this._filterTextIfApplicable(this.input.options[c].text)};!this.input.multiple&&this._isSecret()&&a.length>1&&(a.length=1),s.log(this.recorder,n,{t:r,x:o,sz:{w:this.input.offsetWidth,h:this.input.offsetHeight},o:a,si:this.input.options.selectedIndex}),this._lastSelectListContents=this.input.innerHTML;break;default:s.log(this.recorder,n,{t:r,x:o,v:this._filterTextIfApplicable(this.input.value)||""})}}},M.prototype._updateCaret=function(){var t=this._getCaretInfo(this.input),e=this.recorder?this.recorder.getLogger():null;!e||this.lastCaretInfo&&this.lastCaretInfo.s==t.s&&this.lastCaretInfo.e==t.e&&this.lastCaretInfo.c==t.c||setTimeout(function(t,e){return function(){t&&t.recorder&&t.recorder.getLogger().log(t.recorder,v.EVENT_TYPES.CARET_INFO,e)}}(this,{x:e.logXPath(this.recorder,w.getMapping(this.input)),ci:t}),20),this.lastCaretInfo=t},M.prototype._getCaretInfo=function(t){var i={s:0,e:0,c:0};if(t){var r=t.value,s=t.ownerDocument;if(O._browser.isIE&&s.selection){if("textarea"==e.toLowerCase(t.tagName)&&s.selection){r.charCodeAt(r.length-1)<14&&(r=r.replace(/34/g,"")+String.fromCharCode(28));var n=s.selection.createRange(),o=n.duplicate();o.moveToElementText(t),o.setEndPoint("StartToEnd",n),i.e=r.length-o.text.length,o.setEndPoint("StartToStart",n),i.s=r.length-o.text.length,i.c=i.e,r.substr(r.length-1)==String.fromCharCode(28)&&(r=r.substr(0,r.length-1));var a=r.substr(0,i.s).split("\n").length-1,c=r.substr(0,i.c).split("\n").length-1,h=r.substr(0,i.e).split("\n").length-1;i.c-=c,i.s-=a,i.e-=h}else if(s.selection){var l=s.selection.createRange(),d=l.duplicate();i.s=0-d.moveStart("character",-1e5),i.e=i.s+l.text.length,i.c=i.e}}else try{i.s=t.selectionStart,i.e=t.selectionEnd,i.c=i.e}catch(t){}!i.s&&i.s<0&&(i={s:0,e:0,c:0})}return i},M.prototype.dispose=function(){this.input._fsrTracker=null,this.recorder=null,this.lastValue=null,this.input=null,this.lastCaretInfo=null,this._lastSelectListContents=null,clearTimeout(this._caretMonitor)};var I={};I.nodeBelongsTo=function(t,e){if(e.contains)return e.contains(t);if(e.compareDocumentPosition)return!!(16&e.compareDocumentPosition(t));for(;t.parentNode;){if(t==e||t==e.body)return!0;t=t.parentNode}return!1},I.getEncoding=function(){return O._browser.isIE?window.document.charset:window.document.characterSet},I.getParentWindow=function(t){var e=t;if(t){if(t._pw)return t._pw;if(t.ownerDocument&&t.ownerDocument.defaultView)return t.ownerDocument.defaultView;for(;t.parentNode||t.document;){if(t.document)return e._pw=t,t;t=t.parentNode}}},I.getStyle=function(t,e,i){if(!e)return"";var r="",s=e.document.defaultView;return s&&s.getComputedStyle&&s.getComputedStyle(t,"")?r=s.getComputedStyle(t,"").getPropertyValue(i):t.currentStyle&&(i=i.replace(/\-(\w)/g,function(t,e){return e.toUpperCase()}),r=t.currentStyle[i]),r},I.getPosition=function(t,e){for(var i=0,r=0,s=I.getStyle;t;)i+=t.offsetTop+(parseFloat(s(t,e,"borderTopWidth"))||0),r+=t.offsetLeft+(parseFloat(s(t,e,"borderLeftWidth"))||0),t=t.offsetParent;return{x:r,y:i}},I.getPositionRelativeToMainView=function(t,e,r){for(var s,n=!1;!n&&e;){n=e.parent==e;var o=I.getPosition(t,e),a=i.getScroll(e);r&&!n&&(o.x-=a.x,o.y-=a.y),s?(s.x+=o.x,s.y+=o.y):s=o,t=e.frameElement,e=e.parent}return s},I.getDocSize=function(t){var e=t||window.document,i=e.body,r=e.documentElement,s=Math.max;return{width:s(s(i.scrollWidth,r.scrollWidth),s(i.offsetWidth,r.offsetWidth),s(i.clientWidth,r.clientWidth)),height:s(s(i.scrollHeight,r.scrollHeight),s(i.offsetHeight,r.offsetHeight),s(i.clientHeight,r.clientHeight))}},I.externalizeStyleCSSTextToInnerHTML=function(t){var i,s,n=!1;if(e.isDefined(r.useCSSText)||(i=document.createElement("style"),i.setAttribute("type","text/css"),r.useCSSText=e.isDefined(i.styleSheet)),r.useCSSText){i="STYLE"==t.nodeName?[t]:t.querySelectorAll("style");for(var o=0,a=i.length;o<a;o++)s=i[o],s.styleSheet&&s.styleSheet.cssText&&s.styleSheet.cssText.replace(/\s/gi,"").length!=s.innerHTML.replace(/\s/gi,"").length&&(s.innerHTML=s.styleSheet.cssText,n=!0)}return n};var O=function(t,r,config,s){this.config=config,this.browser=r,this.isXDRIFrameMode=s,this.isTop=t.getTop()==t,this.recorder=t,this.mutation=new k(t),this._setupMouseTracking(),this.sizeCapture=new h(this.recorder,this.recorder.win,"resize",700,function(t,e,s){var n=i.getSize(s.win),o=0,a=s.getLogger(),c=I.getDocSize(s.doc),h={w:c.width,h:c.height};if(s.docSize=c,r.isMobile){var l=n;n=h,h=l,o=window.orientation}a.log(s,v.EVENT_TYPES.FRAME_SIZE,{sz:n,avs:h,st:o,m:r.isMobile},null,-350),a.log(s,v.EVENT_TYPES.DOC_SIZE,{sz:{w:c.width,h:c.height}},null,-340)});var n=this.recorder.win.onerror;this.recorder.win.onerror=e.proxy(function(t,e,i){this.getLogger().log(this,v.EVENT_TYPES.JAVASCRIPT_ERROR,{v:t+", "+e+", "+i}),n&&n.apply(this.win,arguments)},this.recorder),this.inputCaptures=[],this.updateNodeBinding(this.recorder.win.document.body,!1,!0),this.inputReserialize=function(t,i,r){return function(){setTimeout(function(){try{t.recorder.win.document}catch(t){return}for(var i=0;i<r.length;i++){var s=r[i];r[i].hasChanged()&&e.isDefined(s.input)&&"SELECT"!=s.input.nodeName&&(s.lastValue=s.input.value,s.serialize())}},20),t.isTop||t.recorder.getTop().eventcap.inputReserialize.apply()}}(this,this.recorder,this.inputCaptures),i.Bind(this.recorder.win.document,"record:click",this.inputReserialize),i.Bind(this.recorder.win.document,"record:touchstart",this.inputReserialize),
this.config.advancedSettings&&this.config.advancedSettings.scrollEls&&(this._setupScrollTracking(),t.DomUpdated.subscribe(e.proxy(function(t){this._setupScrollTracking(t)},this))),t=null};O.prototype._setupScrollTracking=function(t){t||(t=document);var i=this.config.advancedSettings.scrollEls;if(e.isString(i))for(var r=t.querySelectorAll(i),s=0;s<r.length;s++)r[s]._fsrScrollCapture||(r[s]._fsrScrollCapture=new h(this.recorder,r[s],"scroll",400,function(t){return function(e,i){i||(i=e.target||e.srcElement),i&&t.getLogger().log(t,v.EVENT_TYPES.SCROLL_EL,{x:t.getLogger().logXPath(t,w.getMapping(i)),ps:{x:i.scrollLeft,y:i.scrollTop}})}}(this.recorder)))},O.prototype._setupMouseTracking=function(){var t=this,r=function(r,s,n,o){var a=v.EVENT_TYPES,c=n.getLogger();if(r.delayed||n.getTop().isMousedOverWindow||(c.log(n,a.WINDOW_MOUSEOUT_MOUSEENTER,{st:1}),n.getTop().isMousedOverWindow=!0),n.getTop().isMousedOverWindow){o=o||a.MOUSE_MOVE;var h={x:"unknown"!=typeof r.clientX?r.clientX:"unknown"!=typeof r.screenX?r.screenX:r.sX,y:"unknown"!=typeof r.clientY?r.clientY:"unknown"!=typeof r.screenY?r.screenY:r.sY},l=-1;if(o==a.MOUSE_CLICK||o==a.MOUSE_DOWN){var d=r.explicitOriginalTarget||r.originalTarget||r.target||r.srcElement;d&&(l=c.logXPath(n,w.getMapping(d)))}var u=i.getScroll(n.win);if(void 0===h.x&&(h=n.lastRawMouseCoords),e.isDefined(h)){n.lastRawMouseCoords=h;var g={x:u.x+h.x,y:u.y+h.y};if(n.win!==n.win.top)if(t.isXDRIFrameMode)g.x+=n.ifrFrameOffset.x-u.x,g.y+=n.ifrFrameOffset.y-u.y;else{var p=n.win.frameElement,f=I.getPositionRelativeToMainView(p,I.getParentWindow(p),!1);f&&(g.x+=f.x-u.x,g.y+=f.y-u.y)}c.log(n,o,{ps:g,x:l})}}},s=250;if(this.browser.isMobile){if(r=null,s=100,this.isTop){this.lastOrientation={alpha:0,beta:0,gamma:0},i.Bind(window,"record:deviceorientation",e.proxy(function(t){if(e.isDefined(t.alpha)){var i=Math.round(t.alpha),r=Math.round(t.beta),s=Math.round(t.gamma),n=this.recorder;(Math.abs(i-this.lastOrientation.alpha)>10||Math.abs(r-this.lastOrientation.beta)>10||Math.abs(s-this.lastOrientation.gamma)>10)&&(this.lastOrientation.alpha=i,this.lastOrientation.beta=r,this.lastOrientation.gamma=s,n.getLogger().log(n,v.EVENT_TYPES.ORIENTATION,{ot:{a:i,b:r,g:s}}))}},this)),this.doOrientationChange=e.proxy(function(t){var e=this.recorder.win.document.documentElement,r=I.getDocSize(this.recorder.doc),s=0;Math.abs(window.orientation)>0&&(s=1),this.recorder.getLogger().log(this.recorder,v.EVENT_TYPES.ORIENTATION_CHANGE,{ps:i.getScroll(this.recorder.win),oc:{isL:s,ww:e.clientWidth,wh:e.clientHeight,dw:r.width,dh:r.height,wiw:window.innerWidth,wih:window.innerHeight}})},this),i.Bind(window,"record:orientationchange",this.doOrientationChange),this.doOrientationChange();for(var n=function(t,i,r){var s=[];if(e.isDefined(t.touches))for(var n=0;n<t.touches.length;n++){var o=t.touches[n];s.push({x:o.pageX,y:o.pageY})}r.getLogger().log(r,v.EVENT_TYPES.TOUCH,{ts:s})},o=["start","end","cancel","leave","move"],a=0;a<o.length;a++)i.Bind(window,"record:touch"+o[a],function(t){return function(e){n(e,0,t)}}(this.recorder));i.Bind(window,"record:touchstart",e.proxy(function(){this.EmergencySendRequired.fire()},this.recorder))}}else{var c=this.browser.isIE?this.recorder.win.document:this.recorder.win;this.isTop?this.mouseMoveCapture=new h(this.recorder,c,"record:mousemove",200,r,!0):this.mouseMoveCapture=this.recorder.getTop().eventcap.mouseMoveCapture.merge(this.recorder,c,"record:mousemove",r,!0),i.Bind(this.recorder.win.document,"record:mousedown",e.proxy(function(t){this.mHandler.call(this.ctx,t,null,this.rec,v.EVENT_TYPES.MOUSE_CLICK)},{ctx:this,rec:this.recorder,mHandler:r}))}if(this.scrollCapture=new h(this.recorder,this.recorder.win,"record:scroll",s,function(t){return function(e,s,n){var o=!0;if(n.win==n.win.top)if(S._browser.isMobile){var a=I.getDocSize(n.doc),c={cw:window.innerWidth,ch:window.innerHeight,w:a.width,h:a.height},h={z:c,ps:i.getScroll(n.win)};o=!1,n.getLogger().log(n,v.EVENT_TYPES.ZOOM,h)}else r&&t.call(this,e,s,n);o&&n.getLogger().log(n,v.EVENT_TYPES.FRAME_SCROLL,{ps:i.getScroll(n.win)})}}(r)),this.isTop){this.browser.isMobile||(this.recorder.isMousedOverWindow=!0,i.Bind(this.recorder.win.document,"record:mouseover",function(t,e){return function(t){!e||e.isMousedOverWindow||t.relatedTarget||t.fromElement||(e.getLogger().log(e,v.EVENT_TYPES.WINDOW_MOUSEOUT_MOUSEENTER,{st:1}),e.isMousedOverWindow=!0)}}(0,this.recorder)),i.Bind(this.recorder.win.document,"record:mouseout",e.proxy(function(t){t=t||this.recorder.win.event;var e,i=this.recorder,r=t.relatedTarget||t.toElement;try{r&&(e=r.nodeName)}catch(t){}i&&i.isMousedOverWindow&&(!r||e&&"HTML"==e)&&(i.getLogger().log(i,v.EVENT_TYPES.WINDOW_MOUSEOUT_MOUSEENTER,{st:0}),i.isMousedOverWindow=!1,i.EmergencySendRequired.fire())},this)));var l=function(t){return function(){var e=I.getDocSize(t.doc);t.docSize&&t.docSize.width==e.width&&t.docSize.height==e.height||(t.docSize=e,t.getLogger().log(t,v.EVENT_TYPES.DOC_SIZE,{sz:{w:e.width,h:e.height}}))}}(this.recorder);l.apply(this,[]),this.recorder.DomUpdated.subscribe(l),l=null}r=null},O.MAX_UPDATE_NODE_BINDING_EXECUTION_TIME=500,O.prototype.updateNodeBinding=function(t,r,s){for(var n=+new Date,o=t.querySelectorAll("input, select, textarea"),a=this.inputCaptures,c=o.length-1;c>=0;c--){if(i.now()-n>O.MAX_UPDATE_NODE_BINDING_EXECUTION_TIME){this.recorder.logger.log(this.recorder,v.EVENT_TYPES.INCOMPLETE_INPUT_CAPTURE,{});break}var h=o[c];if(h._fsrTracker)for(var l=a.length-1;l>=0;l--){var d=a[l];if(d.input==h&&d.hasChanged()){d.serialize();break}}else a[a.length]=new M(this.recorder,h),"reset"==h.getAttribute("type")&&i.Bind(h,"record:click",function(t){return function(){setTimeout(e.proxy(function(){for(var t=0;t<this.length;t++)this[t].serialize()},t),1)}}(a));h=null}!s&&this.browser.isIE&&this.mutation.setNodeSpecificBindings(t,r),o=null,a=null,t=null},O.prototype.dispose=function(){this.mutation.dispose(),this.mutation=null,this.recorder=null,this.inputCaptures=null,this.inputReserialize=null;var t,e=c._trackers;for(t=0;t<e.length;t++)e[t].dispose(),e.splice(t--,1);var i=C;for(t=0;t<i.length;t++)i[t].dispose(),i.splice(t--,1)};var L=function(){};L.prototype={useWhiteListing:!1,dynamicWhiteListUnblocks:[],dynamicSelectorsToMask:[],dynamicSelectorsToUnmask:[],dynamicWhiteListEls:[],dynamicSelectorsToRemask:[],dynamicSelectorsToBlockAssets:[],staticWhiteListEls:[],isMasking:!1,noMaskClassArray:[]};L.prototype._maskStaticBySelector=function(t,e,r){var s,n,o,a=e.document,c=a.querySelectorAll(t);for(this.blockingCommentStart=r?"fsrWhiteListStart":"fsrHiddenBlockStart",this.blockingCommentEnd=r?"fsrWhiteListEnd":"fsrHiddenBlockEnd",s=0;s<c.length;s++)n=c[s],"BODY"==n.nodeName?i.hasClass(n,"_fsrWL")||(i.addClass(n,"_fsrWL"),n.insertBefore(a.createComment(this.blockingCommentStart),n.firstChild),n.appendChild(a.createComment(this.blockingCommentEnd))):(o=n.parentNode,i.hasClass(o,"_fsrWL")||(n.insertBefore(a.createComment(this.blockingCommentStart),n.firstChild),n.appendChild(a.createComment(this.blockingCommentEnd))))},L.prototype._remaskBySelector=function(t,e){i.removeClass(e.document.querySelectorAll(t),"fsrVisible")},L.prototype._unmaskStaticBySelector=function(t,e){i.addClass(e.document.querySelectorAll(t),"fsrVisible")},L.prototype._obscureBySelector=function(t,e){i.addClass(e.document.querySelectorAll(t),"fsrObscure")},L.prototype._maskDynamicBySelector=function(t,e,i){t.length>0&&"string"==typeof t&&(void 0===i||""===i||"inTxBlk"==i?(this._maskStaticBySelector(t,e,!1),this.dynamicSelectorsToMask[this.dynamicSelectorsToMask.length]=t):"evUnblk"==i?(this._unmaskStaticBySelector(t,e),this.dynamicSelectorsToUnmask[this.dynamicSelectorsToUnmask.length]=t):"evReblk"==i?(this._remaskBySelector(t,e),this.dynamicSelectorsToRemask[this.dynamicSelectorsToRemask.length]=t):"DOMWL"==i&&(this._maskStaticBySelector(t,e,!0),this.dynamicWhiteListUnblocks[this.dynamicWhiteListUnblocks.length]=t))},L.prototype._maskTitleTag=function(t,e){if(t)for(var r=e.location.href,s=e.document,n=s.head,o=n.getElementsByTagName("title")[0],a=0,c=t.length;a<c;a++)if(i.testAgainstSearch(t[a],r)){n.insertBefore(s.createComment("fsrHiddenBlockStart"),o),n.insertBefore(s.createComment("fsrHiddenBlockEnd"),o.nextSibling);break}},L.prototype._unmaskDynamicBySelector=function(t,e){this._maskDynamicBySelector(t,e,"evUnblk")},L.prototype._maskAssetDynamicBySelector=function(t,e){if(i.addClass(e.document.querySelectorAll(t),"fsrHidden"),t.length>0){"string"==typeof t&&(t=[t]);for(var r=this.dynamicSelectorsToBlockAssets,s=0;s<t.length;s++)r[r.length]=t[s]}},L.prototype._removeVisibility=function(t,e){this._maskDynamicBySelector(t,e,"evReblk")},L.prototype._whiteListUnblockDynamic=function(t,e){this._maskDynamicBySelector(t,e,"DOMWL")},L.prototype.addClassToNoMaskArray=function(t){this.noMaskClassArray[this.noMaskClassArray.length]=t},L.prototype.isNodeMasked=function(t){var e,i,r,s=!0;if(this.noMaskClassArray.length>0&&t.className)for(e=this.noMaskClassArray,i=0;i<e.length;i++)r=e[i],t.className.indexOf(r)>-1&&(s=!1),i=r=null;return t.childNodes.length<1||!s||!!(t.childNodes[0].data&&t.childNodes[0].data.indexOf(this.blockingCommentStart)>-1)},L.prototype._tagDynamicMaskNodes=function(t,e,r){var s,n,o,a=r.document,c=!1;if(!this.isMasking){if(this.isMasking=!0,this.useWhiteListing){if(c=!0,this.dynamicWhiteListUnblocks.length>0&&!e)for(o=a.querySelectorAll(this.dynamicWhiteListUnblocks.join(",")),s=0,n=o.length;s<n;s++)if(I.nodeBelongsTo(t,o[s])||o[s]==t){if(!this.isNodeMasked(t)){c=!1;break}}else I.nodeBelongsTo(o[s],t)&&(this.isNodeMasked(o[s])||i.hasClass(o[s],"_fsrWL")||(i.addClass(o[s],"_fsrWL"),o[s].insertBefore(a.createComment("fsrWhiteListStart"),o[s].firstChild),o[s].appendChild(a.createComment("fsrWhiteListEnd"))))}else if(this.dynamicSelectorsToMask.length>0&&!e)for(o=a.querySelectorAll(this.dynamicSelectorsToMask.join(",")),s=0,n=o.length;s<n;s++)if(I.nodeBelongsTo(t,o[s])||o[s]==t){if(!this.isNodeMasked(t)){c=!0;break}}else I.nodeBelongsTo(o[s],t)&&(this.isNodeMasked(o[s])||(o[s].insertBefore(a.createComment("fsrHiddenBlockStart"),o[s].firstChild),o[s].appendChild(a.createComment("fsrHiddenBlockEnd"))));if(this.dynamicSelectorsToUnmask.length>0){for(s=this.dynamicSelectorsToUnmask.length-1;s>=0;s--)i.addClass(a.querySelectorAll(this.dynamicSelectorsToUnmask[s]),"fsrVisible");for(n=this.dynamicSelectorsToRemask.length-1;n>=0;n--)i.removeClass(a.querySelectorAll(this.dynamicSelectorsToRemask[n]),"fsrVisible")}if(this.dynamicSelectorsToBlockAssets.length>0)for(s=this.dynamicSelectorsToBlockAssets.length-1;s>=0;s--)i.addClass(a.querySelectorAll(this.dynamicSelectorsToBlockAssets[s]),"fsrHidden");this.isMasking=!1}return c},L.prototype.maskDocument=function(t,r){var s=e.toLowerCase(r.location.href||"about:blank");if(this.useWhiteListing=!1,t.staticWhiteListEls)for(var n in t.staticWhiteListEls)if(t.staticWhiteListEls.hasOwnProperty(n)&&(s.indexOf(n)>-1||i.testAgainstSearch(n,s))){this.useWhiteListing=!0;break}if(t.dynamicWhiteListEls)for(var o in t.dynamicWhiteListEls)if(t.dynamicWhiteListEls.hasOwnProperty(o)&&(s.indexOf(o)>-1||i.testAgainstSearch(o,s))){this.useWhiteListing=!0;break}t.useWhiteListing=this.useWhiteListing;var a=function(t,e,r,n,o){for(var a in e)(s.indexOf(a)>-1||i.testAgainstSearch(a,s))&&r.call(t,e[a],n,o)};this.useWhiteListing?(a(this,t.staticWhiteListEls,this._maskStaticBySelector,r,!0),a(this,t.dynamicWhiteListEls,this._whiteListUnblockDynamic,r)):(a(this,t.staticBlockEls,this._maskStaticBySelector,r),a(this,t.dynamicBlockEls,this._maskDynamicBySelector,r)),this._maskTitleTag(t.blockTitleEl,r),a(this,t.staticVisibleEls,this._unmaskStaticBySelector,r),a(this,t.dynamicVisibleEls,this._unmaskDynamicBySelector,r),a(this,t.assetBlockEls,this._maskAssetDynamicBySelector,r),a(this,t.removeVisibilityEls,this._removeVisibility,r),a(this,t.obscureEls,this._obscureBySelector,r)};var x={iFrameXpaths:{}};x.performFrameBindings=function(t,s){if(!r.advancedSettings.skipIframes){var n,o,a,c,h,l=(s||t.win.document).querySelectorAll("iframe");for(n=0;n<l.length;n++){if(o=l[n],!(o._fsrB||o.getAttribute("_fsrB"))&&!x.iFrameXpaths[w.getMapping(o)]){var u=this.testFrameOrigin(o),g=this.testFrameSrc(o);if(g&&u){if(x.iFrameXpaths[w.getMapping(o)]=!0,o.setAttribute("_fsrB",!0),!(a=this.getFrameWindow(o)))return;if(c=this.getFrameDocument(a,!1),h=!1,c){this.unbindInternalIframes(c);try{"complete, loaded".indexOf(c.readyState)>-1&&(h=!0)}catch(t){}h&&(e.isDefined(a.recorder)?a.recorder.serializeDom():(a.recorder=new U(t.stg,t.browser,a,w.getMapping(o),t,t.config),t.childRecorders.push(a.recorder),i.Bind(a,"unload",function(t){return function(){t.dispose()}}(a.recorder))))}c=null,a=null}else g&&!u&&(x.iFrameXpaths[w.getMapping(o)]=!0,o.setAttribute("_fsrB",!0),d.TrackFrame(w.getMapping(o),o,t));i.BindOnce(o,"record:load",function(t){return function(e){x.bindToFrameLoad(e,t)}}(t))}o=null}l=null,t=null}},x.bindToFrameLoad=function(t,r){var s,n=t.originalTarget||t.target||t.srcElement;n&&this.testFrameSrc(n)&&x.testFrameOrigin(n)&&(s=x.getFrameWindow(n),x.getFrameDocument(s)&&(e.isDefined(s.recorder)||(s.recorder=new U(r.stg,r.browser,s,w.getMapping(n),r,r.config,r.isIframeMode),r.childRecorders.push(s.recorder),s.recorder.ready.subscribe(e.proxy(function(){i.Bind(s,"unload",this.dispose)},s.recorder),!0,!0))),s=null),n=null,r=null,t=null},x.testFrameSrc=function(t){var e,i=["javascript:","shim.gif","about:blank"];for(e=0;e<i.length;e++)if(t.src.indexOf(i[e])>-1)return!1;return!0},x.testFrameOrigin=function(t,e){var r=t.getAttribute("src");if(!r||0===r.indexOf(" "))return!1;var s=t.src;return i.testSameDomain(e||window.location.href,s)},x.unbindInternalIframes=function(t){var e,i=t.querySelectorAll("iframe[_fsrB='true']");for(e=0;e<i.length;e++)i[e]._fsrB=!1},x.getFrameWindow=function(t){var e;return t&&t.contentWindow?e=t.contentWindow:t&&t.contentDocument&&t.contentDocument.defaultView&&(e=t.contentDocument.defaultView),e&&e!=e.top?e:null},x.getFrameDocument=function(t){try{return t.document}catch(t){return!1}};var k=function(t){this.config=t.config,this.recorder=t,this.mutationSynonym=window.MutationObserver?"MutationObserver":!!window.WebKitMutationObserver&&"WebKitMutationObserver",this.domMethodCopies=[],this.domMethodRewrites=[],this.domUpdateThrottler=new N(this.recorder),this.supportsDomPrototypes=!O._browser.isIE||(e.isDefined(t.win.Element)||e.isDefined(t.win.HTMLElement))&&!r.useEleMutation,this.setupMutationObserver(this.recorder,this)||O._browser.isIE&&(this.saveDomMutationMethods(),this.supportsDomPrototypes&&this.overrideDomPrototype(),this.setNodeSpecificBindings(this.recorder.win.document),this.recorder.DomUpdated.subscribe(e.proxy(function(t){this.setNodeSpecificBindings(t),t=null},this))),t=null};k.mutatorInfo={ignoreNodeContentsList:"input,select,img,link,meta,title,textarea,br,hr,script".split(","),watchNodeList:"html,head,header,h1,h2,h3,h4,h5,article,aside,section,details,footer,figure,nav,body,div,span,ul,li,dd,dt,ol,dl,tr,td,span,form,img,a,area,iframe,fieldset,select,input,textarea,table,label",svgWatchNodeList:"svg,defs,g,symbol,use,circle,ellipse,line,polygon,polyline,rect,linearGradient,radialGradient,animate,animateColor,animateMotion,animateTransform,set,stop,a,altGlyphDef,clipPath,color-profile,cursor,filter,font,font-face,foreignObject,image,marker,mask,pattern,switch,text,view,path,clippath",domRewriteList:"appendChild,removeChild,removeNode,insertAdjacentHTML,replaceChild,replaceNode,swapNode,insertBefore".split(",")};var D=k.mutatorInfo;k.prototype.setupMutationObserver=function(t,e){var i=this,r=this.mutationSynonym,s=e.domAttrModifiedHandler(t,e,i.config,!0),n=e.domSubtreeModifiedHandler(t,e,i.config,!0);if(r)return e.mutationObserver=new window[r](function(t){t.forEach(function(t){var e;switch(t.type){case"attributes":R(t.attributeName,t.target.tagName)&&(e={target:t.target,attrName:t.attributeName,newValue:t.target.getAttribute(t.attributeName)},s(e)),e=null;break;case"characterData":e={target:t.target.parentNode},n(e);break;case"childList":A(t)&&(e={target:t.target},n(e),e=null)}})}),this.mutationConfig={attributes:!0,childList:!0,subtree:!0},e.mutationObserver.observe(t.win.document,this.mutationConfig),!0};var A=function(t){var e,i;if(t.addedNodes&&t.addedNodes.length>0)for(e=0;e<t.addedNodes.length;e++)if(i=t.addedNodes[e],"SCRIPT"!=i.tagName)return!0;if(t.removedNodes&&t.removedNodes.length>0)for(e=0;e<t.removedNodes.length;e++)if(i=t.removedNodes[e],"SCRIPT"!=i.tagName)return!0},R=function(t,e){return!(!t||"_"==t.substr(0,1)||"on"==t.substr(0,2)||"href"==t||"script,meta,title".indexOf(e)>=0)&&(!(0===t.indexOf("siz")||0===t.indexOf("jQuery"))||0===t.indexOf("_fsrb"))};k.prototype.propertyChangeHandler=function(t){var r=t.propertyName,s=t.srcElement,config=this.config;if(!R(r,s.tagName))return void(s=null);if("innerHTML"==r||"innerText"==r||"outerHTML"==r)("outerHTML"==r||s.tagName&&"SELECT"==s.tagName)&&(s=s.parentNode),this.mutationSynonym||this.domUpdateThrottler.push(s,function(t,e){return function(i){var r=t.recorder,s=r.masker._tagDynamicMaskNodes(e,!1,r.win),n=w.getMapping(e),o=r.getLogger();o&&(o.log(r,v.EVENT_TYPES.DOM_MUTATION_NODE_MODIFIED,{x:o.logXPath(r,n),h:g.ProcessHTML(e.innerHTML,r.win.document,n,s,config)},null,i),t._handleDomUpdateIfNecessary(e)),e=null,t=null,s=null}}(this,s),!1);else if("on"!=r.substring(0,2)){for(var n=0;n<D.domRewriteList.length;n++)if(r==D.domRewriteList[n])return;if("on"==e.toLowerCase(r.substr(0,2)))return;var o=s[r];r.indexOf("style.")>-1&&(r="style",o=s.getAttribute(r)),this.mutationSynonym||this.domUpdateThrottler.push(s,function(t,s){return function(n){if(t.recorder.masker._tagDynamicMaskNodes(s,!1,t.recorder.win),"style"==r){var a=s.getAttribute("style")||s.style;a&&a.cssText&&(o=a.cssText)}var c=t.recorder.getLogger(),h=w.getMapping;if(c)if("style"==r){o&&"null"!=o||(o="");var l=c.logXPath(t,h(s,null,"id"==r)),d=s.getAttribute("style")||s.style;d&&c.log(t.recorder,v.EVENT_TYPES.DOM_MUTATION_NODE_ATTR_MODIFIED,{a:r,v:void 0!==d.cssText?d.cssText:d+"",x:l},null,n)}else{var u=e.toLowerCase(s.tagName);"object"==typeof o||"value"==r&&"input"==u&&"hidden"==s.type||i.inArray(u,["input","select","textarea"])&&!i.inArray(r,["className","cols","rows","class","width","height","align"])||c.log(t.recorder,v.EVENT_TYPES.DOM_MUTATION_NODE_ATTR_MODIFIED,{a:r,v:o+"",x:c.logXPath(t,h(s,null,"id"==r))},null,n)}t=null,c=null,s=null}}(this,s),!0,r)}s=null,t=null},k.prototype.domSubtreeModifiedHandler=function(t,r,s){return function(t){var n,o=t.originalTarget||t.target||t.srcElement,a=e.toLowerCase(o.tagName);if(i.inArray(a,D.ignoreNodeContentsList))"select"==a&&null!==o._fsrTracker&&r.domUpdateThrottler.push(o,function(t){return function(){t._fsrTracker&&t._fsrTracker.serialize()}}(o),!1);else{if(s.advancedSettings&&s.advancedSettings.svgCaptureEnabled&&i.inArray(a,D.svgWatchNodeList.split(","))){for(n=o;!n.innerHTML&&n.parentNode;)n=n.parentNode;o=n}r.domUpdateThrottler.push(o,function(t,e){return function(i){e._doDomUpdateWorkWithLog&&e._doDomUpdateWorkWithLog(t,i)}}(o,r),!1)}}};var P=function(t,i,r,s,n,o){return function(n){var o,a=s.getLogger(),c=w.getMapping;if(a)if("class"==i&&s.masker._tagDynamicMaskNodes(t,!0,s.win),"style"==i){o=a.logXPath(s,c(t,null,"id"==i));var h=t.getAttribute("style")||t.style;h&&"_fsrb"!=i&&a.log(s,v.EVENT_TYPES.DOM_MUTATION_NODE_ATTR_MODIFIED,{a:i,v:void 0!==h.cssText?h.cssText:h+"",x:o},null,n)}else if("_fsrb"!=i&&("value"!=i||"INPUT"!=t.tagName||"hidden"!=t.type)&&"on"!=e.toLowerCase(i.substr(0,2))){var l=c(t,null,"id"==i);o=a.logXPath(s,l),a.log(s,v.EVENT_TYPES.DOM_MUTATION_NODE_ATTR_MODIFIED,{a:i,v:r,x:o},null,n)}}};k.prototype.domAttrModifiedHandler=function(t,e,i){return function(i){var r=i.attrName,s=i.newValue,n=i.originalTarget||i.target||i.srcElement;"on"==r.substr(0,2)||"script,meta,title".indexOf(n.tagName)>-1||(e.domUpdateThrottler.push(n,P(n,r,s,t),!0,r),n=null)}},k.prototype._doDomUpdateWorkWithLog=function(t,e){var i=this.recorder;3==t.nodeType&&(t=t.parentNode);var r=w.getMapping(t);if(0===r[0].indexOf("html")){var s=this.recorder.masker._tagDynamicMaskNodes(t,!1,i.win),n=t.innerHTML?g.ProcessHTML(t.innerHTML,i.win.document,r,s,this.config):{},o=i.getLogger();o&&(o.log(i,v.EVENT_TYPES.DOM_MUTATION_NODE_MODIFIED,{x:o.logXPath(i,r),h:n},null,e),this._handleDomUpdateIfNecessary(t))}},k.prototype._handleDomUpdateIfNecessary=function(t){var r=e.toLowerCase(t.tagName);1!=t.nodeType||i.inArray(r,k.ignoreNodeContentsList)||this.recorder.DomUpdated.fire(t)},k.prototype.setNodeSpecificBindings=function(t){for(var e=t.querySelectorAll(D.watchNodeList+", "+D.svgWatchNodeList),r=function(t){return function(e){t.propertyChangeHandler(e)}}(this),s=e.length-1;s>=0;s--){var n=e[s];if(i.BindOnce(n,"record:propertychange",r,!0,!0),!this.supportsDomPrototypes){var o;if(!n.getAttribute("_fsrRewrite")){n.setAttribute("_fsrRewrite","true");for(var a=D.domRewriteList.length-1;a>=0;a--)o=D.domRewriteList[a],n[o]=this.domMethodRewrites[o]}}n=null}e=null,t=null,r=null},k.prototype.methodRewriter=function(t,s){var config=this.config;return function(){var n,o=this.parentNode,a=s.domMethodCopies[t];if(a){if("BODY"==this.tagName)try{n=a(arguments[0],arguments[1])}catch(t){n=a.apply(this,arguments)}else n=a.apply(this,arguments);return o&&1==o.nodeType&&s.domUpdateThrottler.push(o,function(t,s){return function(n){var o,a,c=t.recorder.masker._tagDynamicMaskNodes(s,!1,t.recorder.win),h=w.getMapping(s),l=t.recorder.getLogger();if(l){if(o=e.toLowerCase(s.tagName),r.svgCaptureEnabled&&i.inArray(o,D.svgWatchNodeList.split(","))){for(a=s;!a.innerHTML;){if(!a.parentNode)return;a=a.parentNode}s=a}l.log(t.recorder,v.EVENT_TYPES.DOM_MUTATION_NODE_MODIFIED,{x:l.logXPath(t.recorder,h),h:g.ProcessHTML(s.innerHTML,t.recorder.win.document,h,c,config)},null,n),t._handleDomUpdateIfNecessary(s)}s=null,t=null,c=null}}(s,o),!1),n}}},k.prototype.saveDomMutationMethods=function(){var t,e;for(e=D.domRewriteList.length-1;e>=0;e--)t=D.domRewriteList[e],this.domMethodCopies[t]=this.recorder.win.document.body[t],this.domMethodRewrites[t]=this.methodRewriter(t,this)},k.prototype.overrideDomPrototype=function(){var t,i,r=this.recorder.win,s=e.isDefined(r.Element)?r.Element.prototype:void 0,n=e.isDefined(r.HTMLElement)?r.HTMLElement.prototype:void 0;for(i=D.domRewriteList.length-1;i>=0;i--)t=D.domRewriteList[i],s&&(s[t]+"").indexOf("native")>-1&&(s[t]=this.domMethodRewrites[t]),n&&(n[t]+"").indexOf("native")>-1&&(n[t]=this.domMethodRewrites[t])},k.prototype.removeDomPrototypeOverrides=function(){var t,i,r=this.recorder.win,s=e.isDefined(r.Element)?r.Element.prototype:void 0,n=e.isDefined(r.HTMLElement)?r.HTMLElement.prototype:void 0;for(i=D.domRewriteList.length-1;i>=0;i--)t=D.domRewriteList[i],s&&s[t]&&delete s[t],n&&n[t]&&delete n[t]},k.prototype.dispose=function(){this.domUpdateThrottler&&(this.domUpdateThrottler.accelerate(),this.domUpdateThrottler.dispose()),e.dispose(this.domMethodCopies),this.mutationObserver&&(this.mutationObserver.disconnect(),this.mutationObserver=null),this.supportsDomPrototypes&&O._browser.isIE&&this.removeDomPrototypeOverrides(),this.domMethodCopies=null,this.domMethodRewrites=null,this.recorder=null};var B=new i.FSEvent;e.API.expose("beginTransmitting",function(){B.subscribe(function(){n.controller.beginTransmitting()},!0,!0)}),e.API.expose("cancelRecord",function(){B.subscribe(function(){n.controller.cancelRecord()},!0,!0)}),e.API.expose("getSession",function(t){t=t||function(){},B.subscribe(function(){var e=n.controller.recorder.logger;t({sessionid:e.sessionid,gsessionid:e.gsessionid,sig:e.gsessionid+"/"+e.sessionid})},!0,!0)});var W=function(t){n.controller=t,B.fire()},U=function(t,r,s,n,o,config,a,c,h,l,u){S._browser=O._browser=this.browser=r,u&&this.setXFrameScrollPosition(u),this.isIframeMode=!!a,this.iFramexPathPrefix=l||"",this.ifrid=c,this.iFrameParentFr=h,this.config=config,this.stg=t,this.win=s,this.ready=new i.FSEvent,this.childRecorders=[],this.disposed=!1,this.DomUpdated=new i.FSEvent,this.DomUpdated.subscribe(e.proxy(function(){this.update.apply(this,arguments)},this)),this.EmergencySendRequired=new i.FSEvent,this.instancePath=n||[],this.isMousedOverWindow=!1,this.logger=this.eventcap=this.recordParent=null,this.win.document&&(this.recordParent=o,o||(this.logger=new v(t,r,this,this.config)),this.getLogger().StorageReady.subscribe(e.proxy(function(){this.ready.fire(),config.advancedSettings&&(this.masker=new L,this.masker.maskDocument(config.advancedSettings.pii,this.win)),this.serializeDom(),this.eventcap=new O(this,r,this.config,this.isIframeMode),x.performFrameBindings(this),a||d.BeginTrackingChildFrames(this),o||this.logger._flushAndTransmit(!0),r.isMobile&&this.setTransmitOK()},this),!0,!0)),o=null,s=null,n=null};U.prototype.update=function(t){t||(t=this.doc.body),0!==t.childNodes.length&&(this.eventcap.updateNodeBinding(t,!!t),x.performFrameBindings(this,t))},U.prototype.getLogger=function(){return this.recordParent?this.recordParent.getLogger():this.logger||{log:function(){}}},U.prototype.logCustomEvent=function(t,i,r){e.isDefined(t)&&"string"==typeof t||(t=""),this.getLogger().log(this,r,{attrName:t,v:JSON.stringify(i)})},U.prototype.getPath=function(){return this._getPathCache||(this.recordParent?this._getPathCache=this.instancePath.concat(this.recordParent.getPath()):this._getPathCache=this.instancePath),this._getPathCache},U.prototype.getTop=function(){return this.recordParent?this.recordParent.getTop():this},U.prototype.setTransmitOK=function(){this.getLogger().setTransmitOK()},U.prototype.processImmediately=function(t){this.getLogger().processImmediately(t)},U.prototype.setTransmitNotOK=function(){this.getLogger().setTransmitNotOK()},U.prototype.cancelRecord=function(t){this.getLogger().cancelRecord(t)},U.prototype.dispose=function(){S.reset();var t=this;if(t.win||(t=t.recorder),!this.disposed){this.disposed=!0;for(var e=0;e<t.childRecorders.length;e++)t.childRecorders[e].dispose();t.eventcap&&t.eventcap.dispose(),t.lastRawMouseCoords&&(t.lastRawMouseCoords=null),t.eventcap=null,t.win=null,t.recordParent=null,t.DomUpdated.unsubscribeAll(),t.DomUpdated=null,t.logger&&(t.logger.dispose(),t.logger=null),t=null,i.Unbind("record:*")}},U.prototype.clearState=function(){this.logger&&this.logger.clearState()},U.prototype.serializeDom=function(){if(this.getLogger()){var t;if(this.win==this.win.top){var r=this.win.document.body;t=S.getCacheableObject(r.innerHTML,w.getMapping(r)).uid}this.getLogger().log(this,v.EVENT_TYPES.DOM_SERIALIZE,{dom:g(this.win,this.getPath(),this.config),url:this.win.location.href.toString(),v:this.browser.agent,buid:t,start:e.startTS,tz:(new Date).getTimezoneOffset(),domloadtime:i.now()-e.startTS})}},U.prototype.getGlobalId=function(){return this.getLogger().gsessionid},U.prototype.setXFrameScrollPosition=function(t){this.ifrFrameOffset=t};var F=function(config,t){this.config=config,this.browser=t};F.prototype.supported=function(){var t,r,s,n=this.config.advancedSettings||{},a=this.browser,c=this.config.blacklist,h=this.config.whitelist,l=!1,d=n.device_type_support,u=location.toString(),g=function(t,n){var a=n[t];switch(t){case"urls":case"text":for(r=0;r<a.length;r++)if(i.testAgainstSearch(a[r],u))return!0;break;case"variables":for(r=0;r<a.length;r++)if(e.isDefined(a[r].name)){if(s=i.retrieveNestedVariable(o,a[r].name),a[r].value===s)return!0;if(!0===s&&"true"==a[r].value)return!0;if(!1===s&&"false"==a[r].value)return!0}break;case"cookies":var c,h=new i.Cookie;for(r=0;r<a.length;r++)if(c=h.get(a[r].name),e.isDefined(c)&&c==a[r].value)return!0}return!1};if(!(n.browser_cutoff[a.browser.name]&&a.browser.actualVersion>=n.browser_cutoff[a.browser.name]||n.platform_cutoff[a.os.name]&&a.os.version>=n.platform_cutoff[a.os.name]))return!1;if(d&&(!d.desktop&&!a.isMobile||!d.tablet&&a.isTablet||!d.phone&&a.isMobile&&!a.isTablet))return!1;if(c&&c.active)for(t in c)if(g(t,c))return!1;if(h&&h.active){for(t in h)if(g(t,h)){l=!0;break}if(!l)return!1}for(t=0;t<n.device_blacklist.length;t++)if(e.toLowerCase(a.agent).indexOf(n.device_blacklist[t].toLowerCase())>-1)return!1;return!0};var V=null;return s.prototype.isIframe=function(){return window!=window.top},s.prototype.isCrossDomainFrame=function(){if(this.isIframe())try{return window.top.document.body.toString().length.length<0}catch(t){return!0}return!1},s.prototype.initialize=function(t,s,n,o,c){if("undefined"==typeof Uint8Array)return void(this.jrny&&this.jrny.addEventString(a.RECORDER_STOP_OLDBROWSER));var h=this.isIframe(),l=this.isCrossDomainFrame();if(!h||l){if(o&&e.ext(r.advancedSettings||{},o),this.winobj=s,this.browser=t,r&&r.instances)for(var d=0;d<r.instances.length;d++){var u=r.instances[d];if(!u.disabled){e.ext(r,u);break}}this.crit=new F(r,t),this.stg=n,n.ready.subscribe(e.proxy(function(){t.ready.subscribe(e.proxy(function(){if(this.crit.supported()){var o=this.stg.get(["rt","i","pv"]);l?i.Bind(window,"message",e.proxy(function(a){if(a.data=a.data+"",a.data&&e.isFunction(a.data.indexOf)&&a.data.length>3&&a.data.indexOf("{")>-1){var h;try{h=JSON.parse(a.data)}catch(t){return}h.src&&"fsframe"==h.src||(h.cxr&&h.id?(this.recorder=new U(n,t,s,h.xp,null,r,l,h.id,a.source,h.sid,h.sp),this.recorder.ready.subscribe(e.proxy(function(){c.set("replay_id",this.recorder.logger.gsessionid),c.set("sessionid",this.recorder.logger.sessionid)},this),!0,!0),o.rt||"x"!==o.i||this.beginTransmitting(),i.Bind(window,"unload",e.proxy(function(){if(this.recorder){var t=this.recorder.ifrid;window.top.postMessage({unloadiFrame:!0,frameId:t},"*"),this.recorder.dispose(),this.recorder=null}},this))):h.cxsp&&this.recorder&&this.recorder.setXFrameScrollPosition(h.sp))}},this)):(this.recorder=new U(n,t,s,null,null,r,l),this.recorder.ready.subscribe(e.proxy(function(){c.set("replay_id",this.recorder.logger.gsessionid),c.set("sessionid",this.recorder.logger.sessionid)},this),!0,!0),this.jrny&&1===o.pv&&this.jrny.addEventString(a.RECORDER_SESSION_STARTED),o.rt||"x"!==o.i||this.beginTransmitting(),i.Bind(window,"unload",e.proxy(function(){this.recorder&&(this.recorder.dispose(),this.recorder=null)},this)),h||W(this))}},this),!0,!0)},this),!0,!0)}},s.prototype.beginTransmitting=function(){this.recorder&&this.recorder.setTransmitOK()},s.prototype.dispose=function(){this.recorder&&(this.transmitIfCan(),this.recorder.dispose(),V=null)},s.prototype.transmitIfCan=function(){var t=this.recorder;if(t){var e=t.getLogger();e&&e.transmitting&&e._flushAndTransmit()}},s.prototype.cancelRecord=function(){this.recorder&&(this.recorder.dispose(),this.recorder=null),this.jrny&&this.jrny.addEventString(a.RECORDER_CANCELED)},s.getInstance=function(t,e,i,r,n){return null===V&&(V=new s(t,e,i,r,n)),V},s.disposeInstance=function(){V&&(V.dispose(),V=null)},s});