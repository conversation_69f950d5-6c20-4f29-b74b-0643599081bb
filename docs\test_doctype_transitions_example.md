# Guide de test des transitions de doctype avec places d'origine

## Comment tester la fonctionnalité

**Important** : Le nettoyage dépend maintenant de la **place d'origine** du document dans le workflow, pas seulement de la transition de doctype.

### 1. Via l'API REST

Pour tester la fonctionnalité de nettoyage automatique lors des changements de doctype, vous pouvez utiliser l'API REST existante.

#### Exemple de test MACH -> PUR depuis Machining

1. **C<PERSON>er ou récupérer un document de type MACH** en place Machining avec des données de production :
```bash
# Supposons que vous avez un document ID 123 de type MACH en place Machining
curl -X GET "http://votre-domaine/document/123/edit" \
  -H "Content-Type: application/json"
```

2. **Vérifier que le document a des données de production et est en place Machining** :
   - currentSteps: {"Machining": 1}
   - matProdType: "HALB"
   - unit: "PC"
   - leadtime: 30
   - procType: "E"
   - prisDans1: "Value1"
   - prisDans2: "Value2"
   - mof: "MOF123"

3. **Changer le doctype vers PUR** :
```bash
curl -X POST "http://votre-domaine/document/123/edit" \
  -H "Content-Type: application/json" \
  -d '{
    "field": "doctype",
    "value": "PUR"
  }'
```

4. **Vérifier le nettoyage complet** (depuis Machining) :
   - Tous les champs listés ci-dessus doivent être à `null` (y compris MOF et pris dans)
   - L'historique des mises à jour doit contenir une entrée de nettoyage automatique avec mention de la place d'origine

#### Exemple de test ASSY -> PUR depuis Assembly

1. **Document ASSY en place Assembly avec données** :
   - currentSteps: {"Assembly": 1}
   - matProdType: "HALB"
   - unit: "PC"
   - leadtime: 30
   - procType: "E"
   - prisDans1: "Value1"
   - prisDans2: "Value2"
   - mof: "MOF123"

2. **Changer vers PUR** :
```bash
curl -X POST "http://votre-domaine/document/123/edit" \
  -H "Content-Type: application/json" \
  -d '{
    "field": "doctype",
    "value": "PUR"
  }'
```

3. **Vérification** (nettoyage partiel depuis Assembly) :
   - matProdType, unit, leadtime, procType doivent être à `null`
   - **mof doit rester "MOF123"** (spécificité ASSY)
   - **prisDans1 et prisDans2 doivent rester inchangés** (spécificité ASSY)

#### Exemple de test PUR -> MACH depuis Achat_Rfq

1. **Document PUR en place Achat_Rfq avec données RFQ** :
   - currentSteps: {"Achat_Rfq": 1}
   - matProdType: "ROH"
   - unit: "KG"
   - commodityCode: "COMM123"
   - purchasingGroup: "BUYER1"
   - procType: "F"

2. **Changer vers MACH** :
```bash
curl -X POST "http://votre-domaine/document/123/edit" \
  -H "Content-Type: application/json" \
  -d '{
    "field": "doctype",
    "value": "MACH"
  }'
```

3. **Vérification** (nettoyage RFQ depuis Achat_Rfq) :
   - Tous les champs RFQ doivent être à `null`

#### Exemple de test PUR -> ASSY depuis Achat_F30

1. **Document PUR en place Achat_F30 avec données RFQ** :
   - currentSteps: {"Achat_F30": 1}
   - matProdType: "ROH"
   - unit: "KG"
   - commodityCode: "COMM456"
   - purchasingGroup: "BUYER2"
   - procType: "F"

2. **Changer vers ASSY** :
```bash
curl -X POST "http://votre-domaine/document/123/edit" \
  -H "Content-Type: application/json" \
  -d '{
    "field": "doctype",
    "value": "ASSY"
  }'
```

3. **Vérification** (nettoyage RFQ depuis Achat_F30) :
   - Tous les champs RFQ doivent être à `null`

### 2. Via l'interface utilisateur

Si vous avez une interface web :

1. **Ouvrir un document** avec des données dans les champs concernés
2. **Changer le type de document** via le formulaire
3. **Vérifier** que les champs appropriés ont été vidés automatiquement
4. **Consulter l'historique** pour voir l'entrée de nettoyage automatique

### 3. Vérifications importantes

#### Historique des modifications
Après chaque transition, vérifiez que l'historique contient :
```json
{
  "type": "edit",
  "date": "2024-01-XX XX:XX:XX",
  "user_id": XXX,
  "user_name": "Nom Prénom",
  "details": "Nettoyage automatique lors du changement de type: matProdType, unit, leadtime, procType, prisDans1, prisDans2, mof"
}
```

#### Workflow
Vérifiez que le document est bien déplacé dans la bonne étape du workflow :
- MACH/MOLD/ASSY -> PUR : document en étape "Quality"
- PUR -> MACH : document en étape "Prod Usinage"
- PUR -> MOLD : document en étape "Prod Moulage"
- PUR -> ASSY : document en étape "Prod Assembly" et "Quality"

### 4. Cas de test négatifs

#### Transitions non définies
Testez des transitions qui ne sont pas dans les règles (ex: DOC -> OTHER) :
- Aucun nettoyage spécifique ne doit avoir lieu
- Seule la logique de réinitialisation générale s'applique

#### Champs déjà vides
Testez avec des documents où les champs sont déjà `null` :
- Aucun champ ne doit apparaître dans l'historique de nettoyage
- Pas d'erreur ne doit survenir

### 5. Tests de performance

Pour des documents avec beaucoup de données :
- Vérifiez que le nettoyage n'impacte pas significativement les performances
- Testez avec des documents ayant de nombreux visas et commentaires

### 6. Rollback et récupération

Testez la possibilité de récupérer les données :
1. Faire une transition qui nettoie des données
2. Vérifier que les données sont bien sauvegardées quelque part si nécessaire
3. Tester la possibilité de revenir en arrière si besoin

## Résultats attendus

### Succès
- ✅ Les bons champs sont nettoyés selon les règles
- ✅ L'historique est correctement mis à jour
- ✅ Le workflow est correctement mis à jour
- ✅ Aucune erreur n'est générée
- ✅ Les performances restent acceptables

### Échecs possibles
- ❌ Champs non nettoyés quand ils devraient l'être
- ❌ Champs nettoyés quand ils ne devraient pas l'être
- ❌ Erreurs 500 lors de la transition
- ❌ Historique non mis à jour
- ❌ Workflow incorrect après transition
