Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;(function(){(function(w,d,t,r,u){var f,n,i;w[u]=w[u]||[],f=function(){var o={ti:"6001327"};o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")},n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function(){var s=this.readyState;s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)},i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,
i)})(window,document,"script","//bat.bing.com/bat.js","uetq")}).call(window);(function(){var page_type=function(){return Bootstrapper.data.resolve("13149")};if(page_type.call()=="order confirmation"){window.uetq=window.uetq||[];window.uetq.push({"gv":Bootstrapper.data.resolve("14630"),"gc":Bootstrapper.data.resolve("13153")})}}).call(window)},2456835,526202);