{% extends 'base.html.twig' %}

{% block title %}Détails - {{ dmo.getDmoId }}{% endblock %}

{% block navbar %}
    {% include '_partials/_nav2.html.twig' %}
{% endblock %}

{% block body %}
<style>
  .card-custom {
    border-radius: 0.75rem;
  }
  .badge-status-open {
    background-color: #d1e7dd;
    color: #0f5132;
  }
  .badge-status-closed {
    background-color: #f8d7da;
    color: #842029;
  }
  .badge-decision {
    background-color: #cff4fc;
    color: #055160;
  }

  .badge-CREATED {
    border: 1px solid #6B7280; 
    background-color: #F9FAFB; 
    color: #6B7280
  }

  .badge-UNDER {
    border: 1px solid #0D6EFD; 
    background-color: #E3F2FD; 
    color: #0D6EFD
  }

  .badge-REJECTED {
    background-color: #FFEDED ; 
    color: #FF0000; 
    border: 1px solid #FF0000
  }

  .badge-ACCEPTED{
    border: 1px solid #2D7D46; 
    background-color: #F0FDF4; 
    color: #2D7D46
  }
</style>
<div class="row mb-2 mx-3 mt-2">
    <div class="col-md-9 pe-0">
        <div class="card shadow border-0 h-100 ms-3">
            <div class="card-header text-white d-flex justify-content-between align-items-center" style="background: linear-gradient(90deg, #009BFF, #00D4FF);">
                <h4 class="mb-0">Détails - {{ dmo.getDmoId }}</h4>
                <button class="btn text-white" id="attachement" data-bs-toggle="modal" data-bs-target="#attachementModal" onclick="refresh_files({{ dmo.dmo }})">
                    <i class="fas fa-paperclip"></i>
                    <span class="d-none d-md-inline fw-bold ms-2" id="fileCount"></span>
                </button>
            </div>
            <div class="card-body" id="detail_dmo">
                <div class="row mb-2">
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Date de création:</small>
                        <p class="editable" data-field="dateInit">
                            {{ dmo.dateInit ? dmo.dateInit|date('Y-m-d') : 'Non défini' }}
                        </p>
                    </div>
                    
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Dernière mise à jour:</small>
                        <p class="editable" data-field="lastUpdateDate">
                            {{ dmo.lastUpdateDate ? dmo.lastUpdateDate|date('Y-m-d') : 'Non défini' }}
                        </p>
                    </div>
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Date de fin:</small>
                        <p class="editable" data-field="dateEnd">
                            {{ dmo.dateEnd ? dmo.dateEnd|date('Y-m-d H:i:s') : 'Non défini' }}
                        </p>
                    </div>
                </div>
                <hr>
                <div class="row mb-2">
                    
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Décision:</small>
                        <div>
                            <span class="badge badge-{{dmo.Decision}} text-uppercase">
                                {% if dmo.Decision == 'CREATED' %}
                                    Créée
                                {% elseif dmo.Decision == 'ACCEPTED' %}
                                    Acceptée
                                {% elseif dmo.Decision == 'REJECTED' %}
                                    Rejetée
                                {% elseif dmo.Decision == 'UNDER REVIEW' %}
                                    En révision
                                {% else %}
                                    Non défini
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Statut:</small>
                        <div>
                            {% if dmo.status %}
                                <span class="badge badge-status-open text-uppercase">Ouvert</span>
                            {% else %}
                                <span class="badge badge-status-closed text-uppercase">Clôturé</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Département:</small>
                        <p class="editable" data-field="Departement">{{ dmo.getDepartementRequestor }}</p>
                    </div>
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Demandeur:</small>
                        <p class="editable" data-field="Requestor">{{ dmo.getRequestor.nom()~ ' ' ~ dmo.getRequestor.prenom() }}</p>
                    </div>
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Division:</small>
                        <p class="editable" data-field="Division">{{ dmo.getNameDivisonProductRange }}</p>
                    </div>
                </div>
                <hr>
                <div class="row mb-2">
                    <div class="col-md-8">
                        <small class="text-uppercase text-muted">Description:</small>
                        <p class="editable" data-field="Description">{{ dmo.Description|nl2br }}</p>
                    </div>
                </div>
                <hr>
                <div class="row mb-2">
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Eng Owner:</small>
                        {% if dmo.Decision in ['CREATED', 'UNDER REVIEW', 'ACCEPTED'] and dmo.status == true %}
                            <select name="EngOwner" data-field="EngOwner" class="form-control">
                                <option value="">-- Sélectionnez --</option>
                                {% for user in users_bde %}
                                    <option value="{{ user.id }}" {% if dmo.getUserNameEngOwner|lower == user.getUsername|lower %}selected{% endif %}>
                                        {{  user.getNom }} {{ user.getPrenom }}
                                    </option>
                                {% endfor %}
                            </select>
                        {% else %}
                            <p>{{ dmo.getUserNameEngOwner }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Évaluation Ex:</small>
                        {% if dmo.Decision in ['CREATED', 'UNDER REVIEW', 'ACCEPTED'] and dmo.status == true %}
                            <textarea name="ExAssessment" data-field="ExAssessment" class="form-control" rows="1">{{ dmo.ExAssessment }}</textarea>
                        {% else %}
                            <p>{{ dmo.ExAssessment }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Temps passé:</small>
                        {% if dmo.Decision in ['CREATED', 'UNDER REVIEW', 'ACCEPTED'] and dmo.status == true %}
                            <input type="number" name="SpentTime" data-field="SpentTime" value="{{ dmo.SpentTime }}" class="form-control">
                        {% else %}
                            <p>{{ dmo.SpentTime }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Classification Ex:</small>
                        {% if dmo.Decision in ['CREATED', 'UNDER REVIEW', 'ACCEPTED'] and dmo.status == true %}
                            <select name="Ex" data-field="Ex" class="form-control">
                                <option value="" {% if dmo.Ex is empty %}selected{% endif %}></option>
                                <option value="ATEX" {% if dmo.Ex == 'ATEX' %}selected{% endif %}>ATEX</option>
                                <option value="CSA" {% if dmo.Ex == 'CSA' %}selected{% endif %}>CSA</option>
                                <option value="EX" {% if dmo.Ex == 'EX' %}selected{% endif %}>EX</option>
                                <option value="IECEX" {% if dmo.Ex == 'IECEX' %}selected{% endif %}>IECEX</option>
                                <option value="NO" {% if dmo.Ex == 'NO' %}selected{% endif %}>NO</option>
                            </select>
                        {% else %}
                            <p>{{ dmo.Ex }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Gamme de produits:</small>
                        {% if dmo.Decision in ['CREATED', 'UNDER REVIEW', 'ACCEPTED'] and dmo.status == true %}
                            <select name="productRange" data-field="productRange" class="form-control">
                                <option value="">-- Sélectionnez --</option>
                                {% for range in productRanges %}
                                    <option value="{{ range.id }}" {% if dmo.getNameProductRange == range.ProductRange %}selected{% endif %}>
                                        {{ range.ProductRange }}
                                    </option>
                                {% endfor %}
                            </select>
                        {% else %}
                            <p>{{ dmo.getNameProductRange }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Pr Diffusion:</small>
                        {% if dmo.Decision in ['CREATED', 'UNDER REVIEW', 'ACCEPTED'] and dmo.status == true %}
                            <input type="number" name="PrNumber" data-field="PrNumber" value="{{ dmo.PrNumber }}" class="form-control">
                        {% else %}
                            <p>{{ dmo.PrNumber }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Projet:</small>
                        {% if dmo.Decision in ['CREATED', 'UNDER REVIEW', 'ACCEPTED'] and dmo.status == true %}
                            <select name="Project" data-field="Project" class="form-control" id="project-select">
                                <option value="">-- Sélectionnez --</option>
                                {% for project in projects %}
                                    <option value="{{ project.id }}" {% if dmo.getProjectRelation == project %}selected{% endif %}>
                                        {{ project.otp() }}
                                    </option>
                                {% endfor %}
                            </select>
                        {% else %}
                            <p>{{ dmo.getProjectRelation ? dmo.getProjectRelation.otp() : '' }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Document:</small>
                        {% if dmo.Decision in ['CREATED', 'UNDER REVIEW', 'ACCEPTED'] and dmo.status == true %}
                            <select name="Document" data-field="Document" class="form-control">
                                <option value="" {% if dmo.Document is empty %}selected{% endif %}></option>
                                <option value="DEO" {% if dmo.Document == 'DEO' %}selected{% endif %} title="Demande d'Essai">DEO</option>
                                <option value="Drawing" {% if dmo.Document == 'Drawing' %}selected{% endif %} title="Any type of drawings (GA, FT, ID, part, BoM e)">Drawing</option>
                                <option value="NT" {% if dmo.Document == 'NT' %}selected{% endif %} title="Technical Note">NT</option>
                                <option value="NU" {% if dmo.Document == 'NU' %}selected{% endif %} title="Installation Procedure">NU</option>
                                <option value="Other" {% if dmo.Document == 'Other' %}selected{% endif %} title="">Other</option>
                                <option value="Specification" {% if dmo.Document == 'Specification' %}selected{% endif %} title="FMME, CC etc...">Specification</option>
                            </select>
                        {% else %}
                            <p>{{ dmo.Document }}</p>
                        {% endif %}
                    </div>

                    <div class="col-md-4">
                        <small class="text-uppercase text-muted">Type:</small>
                        {% if dmo.Decision in ['CREATED', 'UNDER REVIEW', 'ACCEPTED'] and dmo.status == true %}
                            <select name="Type" data-field="Type" class="form-control">
                                <option value="" {% if dmo.Type is empty %}selected{% endif %}></option>
                                <option value="Method Lab." {% if dmo.Type == 'Method Lab.' %}selected{% endif %}>Method Lab.</option>
                                <option value="Method Assy." {% if dmo.Type == 'Method Assy.' %}selected{% endif %}>Method Assy.</option>
                                <option value="Engineering" {% if dmo.Type == 'Engineering' %}selected{% endif %}>Engineering</option>
                            </select>
                        {% else %}
                            <p>{{ dmo.Type }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <a href="{{ path('app_dmo_index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>

                <div id="buton_section" class="gap-2 d-flex align-items-center">
                    {% if dmo.Decision in ['CREATED', 'UNDER REVIEW'] and dmo.status == true %}
                        <button class="btn btn-outline-success" onclick="updateDMO('accept')">Accepter</button>
                        <button class="btn btn-outline-danger" onclick="updateDMO('reject')">Rejeter</button>
                        <button class="btn btn-outline-warning" onclick="updateDMO('update')">Modifier</button>
                        <button class="btn btn-outline-info" onclick="updateDMO('under-review')">Mise en Revue</button>
                    {% elseif dmo.Decision == 'ACCEPTED' and dmo.status == true %}
                        <button class="btn btn-outline-success" onclick="updateDMO('close')">Clôturer</button>
                        <button class="btn btn-outline-warning" onclick="updateDMO('update')">Modifier</button>
                        <button class="btn btn-outline-danger" onclick="updateDMO('reject')">Rejeter</button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 h-100">
        {% include 'dmo/brouillon.html.twig' %}
    </div>
</div>

<div class="modal fade" id="attachementModal" tabindex="-1" aria-labelledby="attachementModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header text-white py-2 d-flex justify-content-between align-items-center" style="background: linear-gradient(90deg, #009BFF, #00D4FF);">
                <h5 class="modal-title" id="attachementModalLabel">Pièces jointes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div>
                    <label for="fileInput" class="form-label">Sélectionnez un fichier :</label>
                    <input type="file" class="form-control" id="fileInput" accept="application/pdf" />
                    <input type="hidden" id="id_piecejointe" value="{{ dmo.dmo }}" />
                    <p id="fileName" class="mt-2 text-muted"></p>
                    <div id="file-list" class="mt-3"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="uploadBtn">Ajouter</button>
            </div>
        </div>
    </div>
</div>

<script>

function updateDMO(action) {
    // Données communes
    const dataCommon = {
        EngOwner: $("select[name='EngOwner']").val(),
        ExAssessment: $("textarea[name='ExAssessment']").val(),
        SpentTime: $("input[name='SpentTime']").val(),
        Ex: $("select[name='Ex']").val(),
        productRange: $("select[name='productRange']").val(),
        PrNumber: $("input[name='PrNumber']").val(),
        Project: $("select[name='Project']").val(),
        Document: $("select[name='Document']").val(),
        Type: $("select[name='Type']").val(),
        action: action
    };

    // Si c'est un rejet, on lance d'abord la popup de commentaire
    if (action === 'reject') {
        Swal.fire({
            title: 'Rejet de la DMO',
            input: 'textarea',
            inputPlaceholder: 'Entrez votre commentaire ici...',
            showCancelButton: true,
            confirmButtonText: 'Rejeter',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-secondary'
            },
            preConfirm: (comment) => {
                if (!comment) {
                    Swal.showValidationMessage('Veuillez entrer un commentaire.');
                }
                return comment;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // 1. On enregistre d'abord le commentaire
                $.ajax({
                    url: "{{ path('app_dmo_add_comment') }}",
                    method: "POST",
                    data: {
                        comment: result.value,
                        dmoId: "{{ dmo.id }}"
                    },
                    dataType: "json",
                    success: function(data) {
                        Toast.fire({
                            icon: 'success',
                            title: 'Commentaire enregistré.'
                        });
                        // 2. Puis on lance la mise à jour du statut
                        $.ajax({
                            url: '{{ path("app_dmo_update", {"id": dmo.id}) }}',
                            method: "POST",
                            data: dataCommon,
                            success: function(response) {
                                if (response.status === 'success') {
                                    Toast.fire({
                                        icon: 'success',
                                        title: response.message
                                    });
                                    $('#detail_dmo').load(window.location.href + ' #detail_dmo > *');
                                    $('#buton_section').load(window.location.href + ' #buton_section > *');
                                } else {
                                    Toast.fire({
                                        icon: 'error',
                                        title: response.message
                                    });
                                }
                            },
                            error: function() {
                                Toast.fire({
                                    icon: 'error',
                                    title: 'Erreur lors de la mise à jour de la DMO.'
                                });
                            }
                        });
                    },
                    error: function() {
                        Toast.fire({
                            icon: 'error',
                            title: 'Erreur lors de l\'enregistrement du commentaire.'
                        });
                    }
                });
            }
        });

    } else {
        // Pour toutes les autres actions, on fait directement la mise à jour
        $.ajax({
            url: '{{ path("app_dmo_update", {"id": dmo.id}) }}',
            method: "POST",
            data: dataCommon,
            success: function(response) {
                if (response.status === 'success') {
                    Toast.fire({
                        icon: 'success',
                        title: response.message
                    });
                    $('#detail_dmo').load(window.location.href + ' #detail_dmo > *');
                    $('#buton_section').load(window.location.href + ' #buton_section > *');
                } else {
                    Toast.fire({
                        icon: 'error',
                        title: response.message
                    });
                }
            },
            error: function() {
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la mise à jour de la DMO.'
                });
            }
        });
    }
}

$(document).ready(function () {

    $("#uploadBtn").on("click", function () {
        let file = $("#fileInput")[0].files[0];
        if (!file) {
            Toast.fire({
                icon: "error",
                title: "Veuillez sélectionner un fichier."
            });
            return;
        }
        let formData = new FormData();
        formData.append("file", file);
        formData.append("id", $("#id_piecejointe").val());
        $.ajax({
            url: "{{ path('dmo_upload') }}",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                if (response.status === "error") {
                    Toast.fire({
                        icon: "error",
                        title: response.message
                    });
                    return;
                }
                Toast.fire({
                    icon: "success",
                    title: response.message
                });
                $("#fileInput").val("");
                $("#fileName").text("");
                refresh_files($("#id_piecejointe").val());
            },
            error: function (xhr, status, error) {
                Toast.fire({
                    icon: "error",
                    title:  "Erreur lors de l'upload"
                });
            }
        });
    });

    /*{% if dmo.Decision in ['CREATED', 'UNDER REVIEW', 'ACCEPTED'] and dmo.status == true %}
    $.ajax({
        url: "https://frscmoutils.scmlemans.com/TimeSheet/TS_REQUEST_SROCHDI.php",
        method: "GET",
        xhrFields: {
            withCredentials: true
        },
        success: function(data) {
            data.forEach(function(item) {
                $("#project-select").append(`<option value="${item}">${item}</option>`);
            });
        },
        error: function() {
            console.error("Erreur lors du chargement des projets.");
        }
    });

        $.ajax({
            url: "{{ path('app_dmo_getAllProjectRelation') }}",
            method: 'GET',
            dataType: 'json'
        }).done(function(data) {
            console.log(data);
            var projectSelect = $("#project-select");
            projectSelect.html('<option value="">-- Sélectionner un projet --</option>');

            // data est un tableau d’objets (chaque élément est un project)
            $.each(data, function(index, project) {
                // On utilise project.id pour la value, et project.OTP pour l’affichage
                projectSelect.append('<option value="' + project.id + '">' + project.OTP + '</option>');
            });
        }).fail(function(jqXHR, textStatus, errorThrown) {
            console.error("Erreur lors du chargement des projets:", textStatus, errorThrown);
        });
    {% endif %}*/
});
refresh_files("{{ dmo.dmo }}");
function refresh_files(id) {
    $("#id_piecejointe").val(id);
    $("#file-list").empty();
    $("#preview-image").attr("src", "").addClass("d-none");
    $.ajax({
        url: "{{ path('dmo_get_files') }}",
        type: "GET",
        data: { id: $("#id_piecejointe").val() },
        success: function (response) {
            if (response.status === "error") {
                Toast.fire({
                    icon: "error",
                    title: response.message
                });
                return;
            }
            var files = Object.values(response.files);
            $("#fileCount").text(files.length > 0 ? `${files.length}` : "");
            files.forEach(function (file) {
                let fileElement = `
                    <div class="row p-2">
                        <div class="col">
                            <span class="badge bg-secondary file-link" data-file="${file}">${file.split('/').pop()}</span>
                            <span class="badge bg-danger delete-file" data-file="${file}" style="cursor: pointer;"><i class="fas fa-trash"></i></span>
                        </div>
                    </div>
                `;
                $("#file-list").append(fileElement);
            });

            $(".file-link").on("click", function(){
                let fileUrl = $(this).data("file");
                window.open(fileUrl, "_blank");
            });
        
            $(".delete-file").on("click", function () {
                let fileUrl = $(this).data("file");
                Swal.fire({
                    title: "Êtes-vous sûr ?",
                    text: "Voulez-vous vraiment supprimer ce fichier ?",
                    showCancelButton: true,
                    customClass: {
                        confirmButton: "btn btn-danger",
                        cancelButton: "btn btn-secondary"
                    },
                    confirmButtonText: "Oui, supprimer",
                    cancelButtonText: "Annuler",
                    imageUrl: fileUrl,
                    imageAlt: "Aperçu de l'image"
                }).then((result) => {
                    if (result.isConfirmed) {
                        delete_file(fileUrl);
                    }
                });
            });
        },
        error: function (xhr, status, error) {
            Toast.fire({
                icon: "error",
                title: xhr.responseJSON.message
            });
        }
    });
}

function delete_file(fileUrl) {
    $.ajax({
        url: "{{ path('dmo_delete_file') }}",
        type: "POST",
        data: { file: fileUrl },
        success: function (response) {
            if (response.status === "error") {
                Toast.fire({
                    icon: "error",
                    title: response.message
                });
                return;
            }
            Toast.fire({
                icon: "success",
                title: response.message
            });
            refresh_files($("#id_piecejointe").val());
        },
        error: function (xhr, status, error) {
            Toast.fire({
                icon: "error",
                title: xhr.responseJSON.message
            });
        }
    });
}
</script>

<style>
    body {
        background-image: url("{{ asset('images/wave.svg') }}");
        background-repeat: no-repeat;
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
    }
    .card {
        border-radius: 12px;
    }
    .card-header {
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }
    .editable {
        /* Pour signaler que le champ est potentiellement modifiable */
        cursor: pointer;
    }
    .editable:hover {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}
