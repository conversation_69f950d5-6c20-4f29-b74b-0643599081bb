{# templates/imputation/read.html.twig #}
{% extends 'baseImputation.html.twig' %}

{% block title %}
    Lecture des Imputations
{% endblock %}

{% block body %}
<style>
    /* Style global amélioré */
    .table-responsive {
        max-height: 80vh;
        overflow-y: auto;
        border: none;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    table {
        margin-bottom: 0;
    }
    th, td {
        white-space: nowrap;
        vertical-align: middle;
        padding: 0.75rem 1rem;
    }
    thead th {
        background-color: #f8f9fa;
        font-weight: bold;
        border-bottom: 2px solid #dee2e6;
    }
    tbody tr:hover {
        background-color: #f1f3f5;
        cursor: pointer;
    }
    .imputation-item.active > td {
        background-color: #e9ecef !important;
    }
    thead {
        position: sticky;
        top: 0;
        z-index: 10;
    }
    /* Style des champs de filtres */
    .filter-fields {
        border-radius: 4px;
        border: 1px solid #ced4da;
        transition: all 0.2s ease-in-out;
    }
    .filter-fields:focus {
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
        border-color: #80bdff;
    }
    /* Style de l'entête de la card */
    .card-header {
        background-color: #fff;
        border-bottom: 1px solid #dee2e6;
    }
</style>

<div class="container-fluid my-4">
            {# <h3 class="mb-2">
                Lecture {% if app.user.isManager or app.user.titre in ['Chef de Projets', 'Cheffe de Projets'] %}des{% else %}de vos{% endif %} Imputations
            </h3> #}
            <h3 class="mb-2">
                {% if app.user.titre == "Chef de Projets" %}
                    Lecture des Imputations
                {% elseif app.user.isManager %}
                    Lecture des Imputations de vos collaborateurs
                {% else %}
                    Lecture de vos Imputations
                {% endif %} 
            </h3>
    <div class="card">
        
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table w-100 mb-0 table-hover">
                    <thead>
                        {# Ligne des filtres – le nombre de colonnes dépend du type d'utilisateur #}
                        <tr>
                            {# Colonne pour le filtre de période #}
                            <th>
                                <input type="month" class="form-control form-control-sm filter-fields" id="periode-filter" name="periode" placeholder="Période">
                            </th>
                            {% if app.user.isManager or app.user.titre in ['Chef de Projets', 'Cheffe de Projets'] %}
                                <th>
                                    <input type="text" class="form-control form-control-sm filter-fields text-center" id="username-filter" name="username" placeholder="Utilisateur">
                                </th>
                            {% endif %}
                            <th>
                                <select class="form-select form-select-sm filter-fields" id="filterProject" name="projet">
                                    <option value="">Projet</option>
                                    {% for projet in projects %}
                                        <option value="{{ projet.id }}">{{ projet.title }} ({{ projet.otp }})</option>
                                    {% endfor %}
                                </select>
                            </th>
                            <th>
                                <input type="text" class="form-control form-control-sm filter-fields" id="phase-filter" name="phase" placeholder="Phase">
                            </th>
                            <th>
                                <input type="text" class="form-control form-control-sm filter-fields text-center" id="nbHeures-filter" name="nbHeures" placeholder="Heures">
                            </th>
                            <th>
                                <input type="text" class="form-control form-control-sm filter-fields text-center" id="filterCode" name="code" placeholder="Code">
                            </th>
                            <th>
                                <input type="text" class="form-control form-control-sm filter-fields text-center" id="filterWorkCenter" name="workCenter" placeholder="WorkCenter">
                            </th>
                            {# Colonne pour le bouton Filtrer – toujours présente, car elle correspond à la dernière cellule du header #}
                            <th class="text-center">
                                <button type="button" class="btn btn-sm btn-primary" id="applyFilters">Filtrer</button>
                            </th>
                        </tr>
                        {# Ligne des intitulés des colonnes #}
                        <tr>
                            <th class="bg-light">Période</th>
                            {% if app.user.isManager or app.user.titre in ['Chef de Projets', 'Cheffe de Projets'] %}
                                <th class="text-center bg-light">Utilisateur</th>
                            {% endif %}
                            <th class="bg-light">Projet</th>
                            <th class="bg-light">Phase</th>
                            <th class="text-center bg-light">Heures</th>
                            <th class="text-center bg-light">Code</th>
                            <th class="text-center bg-light">WorkCenter</th>
                            {% if app.user.titre == "Chef de Projets" %}
                                <th class="text-center bg-light">Actions</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody id="imputations-tbody">
                        {% if imputations is defined and imputations|length > 0 %}
                            {% for item in imputations %}
                                <tr class="imputation-item" data-imputation-id="{{ item.id }}">
                                    <td>{{ item.periode }}</td>
                                    {% if app.user.isManager or app.user.titre in ['Chef de Projets', 'Cheffe de Projets'] %}
                                        <td class="text-center">{{ item.userNom }} {{ item.userPrenom }}</td>
                                    {% endif %}
                                    <td>{{ item.projet }}</td>
                                    <td>{{ item.phase }}</td>
                                    <td class="text-center">{{ item.nbHeures }}</td>
                                    <td class="text-center">{{ item.code }}</td>
                                    <td class="text-center">{{ item.workCenter }}</td>
                                    {% if app.user.titre == "Chef de Projets" %}
                                        <td class="text-center">
                                            <i class="fas fa-trash-alt delete-impute" style="cursor:pointer" data-imputation-id="{{ item.id }}"></i>
                                        </td>
                                    {% endif %}
                                </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                {% if app.user.titre == "Chef de Projets" %}
                                    <td colspan="8" class="alert alert-warning text-center">Aucune imputation trouvée.</td>
                                {% elseif app.user.isManager %}
                                    <td colspan="7" class="alert alert-warning text-center">Aucune imputation trouvée.</td>
                                {% else %}
                                    <td colspan="6" class="alert alert-warning text-center">Vous n'avez pas d'imputations.</td>
                                {% endif %}
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    // Définition d'une variable pour savoir si l'utilisateur peut supprimer des imputations
    var canDelete = {{ app.user.titre == "Chef de Projets" ? 'true' : 'false' }};

    function updateImputations() {
        let periode = $('#periode-filter').val();
        let username = $('#username-filter').length ? $('#username-filter').val() : '';
        let projet = $('#filterProject').val();
        let phase = $('#phase-filter').val();
        let nbHeures = $('#nbHeures-filter').val();
        let code = $('#filterCode').val();
        let workCenter = $('#filterWorkCenter').val();

        const params = new URLSearchParams({
            periode: periode,
            username: username,
            projet: projet,
            phase: phase,
            nbHeures: nbHeures,
            code: code,
            workCenter: workCenter
        });
        fetch('{{ path("app_impute_filtered2") }}?' + params.toString())
        .then(response => response.json())
        .then(data => {
            fillImputations(data.imputations);
        })
        .catch(err => console.error(err));
    }

    function fillImputations(imputations) {
        const tbody = $('#imputations-tbody');
        tbody.empty();
        if(imputations.length === 0) {
            {% if app.user.titre == "Chef de Projets" %}
                tbody.append('<tr><td colspan="8" class="alert alert-warning text-center">Aucune imputation trouvée.</td></tr>');
            {% elseif app.user.isManager %}
                tbody.append('<tr><td colspan="7" class="alert alert-warning text-center">Aucune imputation trouvée.</td></tr>');
            {% else %}
                tbody.append('<tr><td colspan="6" class="alert alert-warning text-center">Vous n\'avez pas d\'imputations.</td></tr>');
            {% endif %}
            return;
        }
        imputations.forEach(item => {
            let row = `<tr class="imputation-item" data-imputation-id="${item.id}">
                <td>${item.periode}</td>`;
            {% if app.user.isManager or app.user.titre in ['Chef de Projets', 'Cheffe de Projets'] %}
                row += `<td class="text-center">${item.userNom + ' ' + item.userPrenom}</td>`;
            {% endif %}
            row += `<td>${item.projet}</td>
                <td>${item.phase}</td>
                <td class="text-center">${item.nbHeures}</td>
                <td class="text-center">${item.code}</td>
                <td class="text-center">${item.workCenter}</td>`;
            {% if app.user.titre == "Chef de Projets" %}
                row += `<td class="text-center"><i class="fas fa-trash-alt delete-impute" style="cursor:pointer" data-imputation-id="${item.id}"></i></td>`;
            {% endif %}
            row += `</tr>`;
            tbody.append(row);
        });
    }

    $(document).on('click', '#applyFilters', function() {
        updateImputations();
    });

    $(document).on('submit', 'form', function(e) {
        e.preventDefault();
        updateImputations();
    });

    $(document).on('click', '.delete-impute', function() {
        var $row = $(this).closest('tr');
        var imputationId = $row.data('imputation-id');
        if (confirm("Êtes-vous sûr de vouloir supprimer cette imputation ?")) {
            fetch('{{ path("app_impute_admin_delete", { "id": "edit_me" }) }}'.replace('edit_me', imputationId), {
                method: 'DELETE'
            })
            .then(response => {
                if (response.ok) {
                    $row.remove();
                } else {
                    alert("Erreur lors de la suppression de l'imputation !");
                }
            })
            .catch(err => console.error(err));
        }
    });

    // Mise à jour des imputations au chargement de la page
    updateImputations();
</script>
{% endblock %}
