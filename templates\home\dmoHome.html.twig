{# templates/home/<USER>
{% extends 'base.html.twig' %}

{% block title %}Tableau de bord – Accueil{% endblock %}

{% block navbar %}
    {% include '_partials/_nav2.html.twig' %}
{% endblock %}

{% block body %}

<style>

    /* Animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in-up {
        opacity: 0;
        animation: fadeInUp 0.6s ease-out forwards;
    }

    .animate-delay-100 { animation-delay: 0.1s; }
    .animate-delay-200 { animation-delay: 0.2s; }
    .animate-delay-300 { animation-delay: 0.3s; }
    .animate-delay-400 { animation-delay: 0.4s; }
    .animate-delay-500 { animation-delay: 0.5s; }

    /* Styles pour les cartes de statistiques */
    .stat-card {
        border: 1px solid;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: 100%;
        background: white;
        transition: all 0.2s ease-in-out;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .stat-card-body {
        padding: 1.5rem;
        flex-grow: 1;
    }

    .stat-card-footer {
        padding: 0.75rem 1rem;
        margin-top: auto;
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        font-size: 1.5rem;
        color: white;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #111827;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stat-label {
        font-size: 1.125rem;
        font-weight: 500;
        color: #111827;
        margin-bottom: 0.5rem;
    }

    .stat-description {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 1rem;
        flex-grow: 1;
    }

    .stat-percentage {
        font-size: 0.875rem;
        color: #6b7280;
    }

    /* Styles par type de carte */
    .stat-card-created {
        border-color: #e5e7eb;
    }

    .stat-card-created .stat-icon {
        background-color: #f3f4f6;
        color: #6b7280;
    }

    .stat-card-created .stat-card-footer {
        background-color: #f9fafb;
        border-top: 1px solid #f3f4f6;
    }

    .stat-card-accepted {
        border-color: #d1fae5;
    }

    .stat-card-accepted .stat-icon {
        background-color: #10b981;
    }

    .stat-card-accepted .stat-card-footer {
        background-color: #ecfdf5;
        border-top: 1px solid #d1fae5;
    }

    .stat-card-under {
        border-color: #dbeafe;
    }

    .stat-card-under .stat-icon {
        background-color: #3b82f6;
    }

    .stat-card-under .stat-card-footer {
        background-color: #eff6ff;
        border-top: 1px solid #dbeafe;
    }

    .stat-card-rejected {
        border-color: #fecaca;
    }

    .stat-card-rejected .stat-icon {
        background-color: #ef4444;
    }

    .stat-card-rejected .stat-card-footer {
        background-color: #fef2f2;
        border-top: 1px solid #fecaca;
    }

    /* Styles pour les sections principales */
    .main-section {
        background: white;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
        margin-bottom: 1.5rem;
    }

    .section-header {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        background: white;
    }

    .section-title {
        font-size: 1.125rem;
        font-weight: 500;
        color: #111827;
        margin: 0;
    }

    .section-subtitle {
        font-size: 0.875rem;
        color: #6b7280;
        margin: 0.25rem 0 0 0;
    }

    .section-body {
        padding: 1.5rem;
    }

    /* Styles pour le tableau */
    .table-modern {
        margin-bottom: 0;
        background: white;
    }

    .table-modern thead th {
        background-color: #f8fafc;
        border-bottom: 1px solid #e5e7eb;
        border-top: none;
        font-weight: 500;
        color: #374151;
        font-size: 0.875rem;
    }

    .table-modern tbody td {
        padding: 0.85rem;
        border-bottom: 1px solid #f3f4f6;
        border-top: none;
        color: #374151;
        font-weight: 400;
    }

    .table-modern tbody tr:hover {
        background-color: #f8fafc;
    }

    /* Badges modernes */
    .badge-modern {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-weight: 500;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .badge-status-open {
        background-color: #d1fae5;
        color: #065f46;
    }

    .badge-status-closed {
        background-color: #fee2e2;
        color: #991b1b;
    }

    .badge-decision-CREATED {
        background-color: #f3f4f6;
        color: #374151;
    }

    .badge-decision-UNDER {
        background-color: #dbeafe;
        color: #1e40af;
    }

    .badge-decision-REJECTED {
        background-color: #fef2f2;
        color: #dc2626;
    }

    .badge-decision-ACCEPTED {
        background-color: #ecfdf5;
        color: #166534;
    }

    /* Styles pour les graphiques */
    .chart-container {
        position: relative;
        height: 300px;
        padding: 1rem;
    }

    /* Titre principal */
    .page-title {
        font-size: 1.875rem;
        font-weight: 700;
        color: #111827;
        margin-bottom: 1.5rem;
    }

    /* Liens d'action */
    .action-link {
        text-decoration: none;
        font-weight: 500;
        display: flex;
        align-items: center;
        transition: color 0.2s;
    }

    .action-link:hover {
        text-decoration: none;
    }

    .stat-card-rejected .action-link {
        color: #dc2626;
    }
    .stat-card-accepted .action-link {
        color: #16a34a;
    }
    .stat-card-under .action-link {
        color: #2563eb;
    }
    .stat-card-created .action-link {
        color: #6b7280;
    }

    .action-link svg {
        margin-left: 0.25rem;
        width: 1rem;
        height: 1rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .stat-card-body {
            padding: 1rem;
        }
        
        .section-header {
            padding: 1rem;
        }
        
        .section-body {
            padding: 1rem;
        }
        
        .page-title {
            font-size: 1.5rem;
        }
    }

    /* Animation des compteurs */
    @keyframes countUp {
        from { opacity: 0.5; }
        to { opacity: 1; }
    }

    .count-up {
        animation: countUp 0.3s ease-out;
    }
</style>

<div class="mt-4" style="margin: 0 10%">
    <div class="row">
        <div class="col-12">
            <h1 class="page-title animate-fade-in-up">Tableau de bord DMO</h1>
        </div>
    </div>

    {# --- Section cartes de statistiques --- #}
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-created animate-fade-in-up animate-delay-100">
                <div class="stat-card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="stat-icon me-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h4 class="stat-label mb-0">Créées</h4>
                    </div>
                    <div class="d-flex flex-column">
                        <p class="stat-number mb-2"><span class="count-up">{{ created }}</span></p>
                        <div class="stat-percentage">{{ ((created / (created + accepte + underReview + rejected)) * 100)|number_format(0) }}% du total des demandes</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <a href="{{ path('app_dmo_index') }}?decision=CREATED" class="action-link">
                        Voir les nouvelles demandes
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-accepted animate-fade-in-up animate-delay-200">
                <div class="stat-card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="stat-icon me-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h4 class="stat-label mb-0">Acceptées</h4>
                    </div>
                    <div class="d-flex flex-column">
                        <p class="stat-number mb-2"><span class="count-up">{{ accepte }}</span></p>
                        <div class="stat-percentage">{{ ((accepte / (created + accepte + underReview + rejected)) * 100)|number_format(0) }}% du total des demandes</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <a href="{{ path('app_dmo_index') }}?decision=ACCEPTED&status=1" class="action-link">
                        Voir les demandes acceptées
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-under animate-fade-in-up animate-delay-300">
                <div class="stat-card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="stat-icon me-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h4 class="stat-label mb-0">En révision</h4>
                    </div>
                    <div class="d-flex flex-column">
                        <p class="stat-number mb-2"><span class="count-up">{{ underReview }}</span></p>
                        <div class="stat-percentage">{{ ((underReview / (created + accepte + underReview + rejected)) * 100)|number_format(0) }}% du total des demandes</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <a href="{{ path('app_dmo_index') }}?decision=UNDER" class="action-link">
                        Voir les demandes en cours
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-rejected animate-fade-in-up animate-delay-400">
                <div class="stat-card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="stat-icon me-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </div>
                        <h4 class="stat-label mb-0">Refusées</h4>
                    </div>
                    <div class="d-flex flex-column">
                        <p class="stat-number mb-2"><span class="count-up">{{ rejected }}</span></p>
                        <div class="stat-percentage">{{ ((rejected / (created + accepte + underReview + rejected)) * 100)|number_format(0) }}% du total des demandes</div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <a href="{{ path('app_dmo_index') }}?decision=REJECTED" class="action-link">
                        Voir les demandes refusées
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    {# --- Section graphique et liste récente --- #}
    <div class="row g-4">
        <div class="col-lg-6">
            <div class="main-section animate-fade-in-up animate-delay-500">
                <div class="section-header">
                    <h3 class="section-title">Évolution mensuelle des DMO</h3>
                    <p class="section-subtitle">Tendance des demandes sur les derniers mois</p>
                </div>
                <div class="section-body">
                    <div class="chart-container">
                        <canvas id="chartMonthly"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="main-section animate-fade-in-up animate-delay-500">
                <div class="section-header d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="section-title">Dernières demandes</h3>
                        <p class="section-subtitle">Les demandes les plus récentes</p>
                    </div>
                    <a href="{{ path('app_dmo_new') }}" class="btn btn-primary btn-sm">
                        Nouvelle demande
                    </a>
                </div>
                <div class="section-body p-0">
                    <div class="table-responsive">
                        <table class="table table-modern">
                            <thead>
                                <tr>
                                    <th>#ID</th>
                                    <th>Demandeur</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Décision</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for d in recentDmos %}
                                    {# badge de statut ouvert / fermé #}
                                    {% set statusClass = d.status
                                        ? 'badge-modern badge-status-open'
                                        : 'badge-modern badge-status-closed' %}
                                    {% set statusLabel = d.status ? 'Ouvert' : 'Fermé' %}

                                    {# badge de décision avec mapping de classe #}
                                    {% set decisionKey = d.decision == 'UNDER REVIEW' ? 'UNDER' : d.decision %}
                                    {% set decisionClass = 'badge-modern badge-decision-' ~ decisionKey %}
                                    {% set decisionLabel = 
                                        d.decision == 'CREATED'       ? 'Créée'
                                      : d.decision == 'ACCEPTED'      ? 'Acceptée'
                                      : d.decision == 'REJECTED'      ? 'Refusée'
                                      : d.decision == 'UNDER REVIEW'  ? 'En révision'
                                      : d.decision
                                    %}

                                    <tr>
                                        <td><strong>{{ d.dmo }}</strong></td>
                                        <td>
                                            {% if d.requestor %}
                                                {{ d.requestor.nom }} {{ d.requestor.prenom }}
                                            {% else %}
                                                <em class="text-muted">Non assigné</em>
                                            {% endif %}
                                        </td>
                                        <td>{{ d.dateInit|date('d/m/Y') }}</td>
                                        <td class="text-center">
                                            <span class="{{ statusClass }}">{{ statusLabel }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="{{ decisionClass }}">{{ decisionLabel }}</span>
                                        </td>
                                    </tr>
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center py-5">
                                            <div class="text-muted">
                                                <div style="font-size: 3rem; margin-bottom: 1rem;">📋</div>
                                                <div>Aucune demande récente à afficher</div>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    const ctx = document.getElementById('chartMonthly').getContext('2d');
    
    // Configuration du graphique moderne
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: {{ chartLabels|json_encode()|raw }},
            datasets: [{
                label: 'Demandes DMO',
                data: {{ chartData|json_encode()|raw }},
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                pointHoverBackgroundColor: '#3b82f6',
                pointHoverBorderColor: '#ffffff',
                pointHoverBorderWidth: 3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(17, 24, 39, 0.95)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: 'rgba(59, 130, 246, 0.5)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    padding: 12
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6b7280',
                        font: {
                            weight: 500
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(229, 231, 235, 0.5)',
                        borderDash: [5, 5]
                    },
                    ticks: {
                        color: '#6b7280',
                        font: {
                            weight: 500
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // Animation des compteurs
    document.addEventListener('DOMContentLoaded', function() {
        const counters = document.querySelectorAll('.count-up');
        counters.forEach(counter => {
            const target = parseInt(counter.textContent);
            counter.textContent = '0';
            
            const increment = target / 70;
            let current = 0;
            
            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.ceil(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };
            
            setTimeout(updateCounter, 200);
        });
    });
</script>

{% endblock %}