<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use App\Entity\Document;
use App\Entity\Visa;
use App\Entity\Commentaire;
use App\Entity\ReleasedPackage;
use App\Entity\Project;
use App\Entity\User;
use App\Entity\Material;
use App\Repository\UserRepository;
use App\Repository\ProjectRepository;
use App\Repository\MaterialRepository;
use App\Repository\ProductCodeRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\DBAL\Attribute\Connection as ConnectionName;
use Symfony\Component\Workflow\Registry;

#[AsCommand(
    name: 'app:migrate-legacy-optimized',
    description: 'Migre les données de l\'ancienne BD vers le nouveau schéma (version optimisée)'
)]
class MigrateLegacyDataOptimizedCommand extends Command
{
    protected static $defaultName = 'app:migrate-legacy-optimized';

    // Caches pour optimiser les performances
    private array $userIdCache = [];
    private array $projectIdCache = [];
    private array $packageDataCache = [];
    private array $supervisorCache = [];
    private array $materialCache = [];
    private array $productCodeCache = [];
    private ?User $adminUser = null;
    private array $stateCounts = [];

    public function __construct(
        #[ConnectionName('legacy')]
        private Connection $oldDb,
        private EntityManagerInterface $em,
        private UserRepository $users,
        private ProjectRepository $projects,
        private MaterialRepository $materials,
        private ProductCodeRepository $productCodes,
        private Registry $workflowRegistry
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('limit', null, InputOption::VALUE_OPTIONAL, 'Limiter le nombre de documents à migrer')
            ->addOption('skip-packages', null, InputOption::VALUE_NONE, 'Ignorer la migration des packages')
            ->addOption('skip-documents', null, InputOption::VALUE_NONE, 'Ignorer la migration des documents');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $limit = $input->getOption('limit');
        $skipPackages = $input->getOption('skip-packages');
        $skipDocuments = $input->getOption('skip-documents');

        // Optimisations mémoire
        $this->optimizeForMigration($output);

        // 0. Nettoyer les tables existantes
        $this->cleanExistingData($output);

        // 1. Initialiser tous les caches
        $this->initializeAllCaches($output);

        // 2. Créer les projets manquants
        $this->createMissingProjectsOptimized($output);

        $packageMapping = [];

        // 3. Migrer les packages si demandé
        if (!$skipPackages) {
            $packageMapping = $this->migratePackagesOptimized($output);
        }

        // 4. Migrer les documents si demandé
        if (!$skipDocuments) {
            $this->migrateDocumentsOptimized($packageMapping, $limit, $output);

            // 4.1. Créer les relations Document ↔ Material
            $this->createDocumentMaterialRelations($output);
        }

        // 5. Migrer les commentaires
        if (!$skipDocuments) {
            $this->migrateCommentsOptimized($output);
        }

        // 6. Établir les relations Package ↔ DMO
        if (!$skipPackages) {
            $this->establishPackageDMORelations($packageMapping, $output);
        }

        // Afficher le résumé
        $this->displayStateSummary($output);

        $output->writeln('<info>Migration optimisée terminée.</info>');
        return Command::SUCCESS;
    }

    /**
     * Optimisations mémoire pour la migration
     */
    private function optimizeForMigration(OutputInterface $output): void
    {
        $output->writeln('<info>Application des optimisations mémoire...</info>');

        // Augmenter la limite de mémoire PHP
        ini_set('memory_limit', '8G');
        $output->writeln('<comment>Limite mémoire PHP augmentée à 8G</comment>');

        // Désactiver le mode debug de Doctrine
        $config = $this->em->getConfiguration();
        if (method_exists($config, 'getSQLLogger') && $config->getSQLLogger()) {
            $config->setSQLLogger(null);
            $output->writeln('<comment>SQL Logger Doctrine désactivé</comment>');
        }

        // Configurer le garbage collector
        gc_enable();
        $output->writeln('<comment>Garbage Collector activé</comment>');

        $output->writeln('<info>Optimisations appliquées avec succès.</info>');
    }

    /**
     * Nettoyer les données existantes
     */
    private function cleanExistingData(OutputInterface $output): void
    {
        $output->writeln('<info>Nettoyage des données existantes...</info>');

        // Utiliser SQL brut pour plus de performance
        $connection = $this->em->getConnection();

        // Supprimer dans l'ordre inverse des dépendances
        $connection->executeStatement('DELETE FROM visa');
        $output->writeln('<comment>Visas supprimés.</comment>');

        $connection->executeStatement('DELETE FROM commentaire WHERE documents_id IS NOT NULL');
        $output->writeln('<comment>Commentaires de documents supprimés.</comment>');

        $connection->executeStatement('DELETE FROM document');
        $output->writeln('<comment>Documents supprimés.</comment>');

        // Supprimer les relations Package ↔ DMO
        $connection->executeStatement('DELETE FROM released_package_dmo');
        $output->writeln('<comment>Relations Package ↔ DMO supprimées.</comment>');

        $connection->executeStatement('DELETE FROM released_package');
        $output->writeln('<comment>Packages supprimés.</comment>');

        $output->writeln('<info>Nettoyage terminé.</info>');
    }

    /**
     * Initialiser tous les caches
     */
    private function initializeAllCaches(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation de tous les caches...</info>');

        // 1. Cache des utilisateurs
        $this->initializeUserCache($output);

        // 2. Cache des projets
        $this->initializeProjectCache($output);

        // 3. Cache des données de packages
        $this->initializePackageDataCache($output);

        // 4. Cache des superviseurs
        $this->initializeSupervisorCache($output);

        // 5. Cache des matériaux
        $this->initializeMaterialCache($output);

        // 6. Cache des ProductCode
        $this->initializeProductCodeCache($output);

        $output->writeln('<info>Tous les caches initialisés.</info>');
    }

    /**
     * Initialiser le cache des utilisateurs
     */
    private function initializeUserCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des utilisateurs...</info>');

        // Vider le cache
        $this->userCache = [];

        // Charger l'utilisateur Admin
        $this->adminUser = $this->users->findOneBy(['nom' => 'Admin']);
        if ($this->adminUser) {
            $output->writeln('<info>Utilisateur Admin trouvé pour fallback.</info>');
        } else {
            $output->writeln('<error>Aucun utilisateur Admin trouvé !</error>');
        }

        $users = $this->users->findAll();
        $output->writeln('<info>Cache utilisateurs initialisé avec ' . count($users) . ' utilisateurs.</info>');
    }

    /**
     * Initialiser le cache des projets
     */
    private function initializeProjectCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des projets...</info>');

        $sql = 'SELECT id, otp FROM project';
        $projects = $this->em->getConnection()->fetchAllAssociative($sql);

        foreach ($projects as $project) {
            $this->projectIdCache[$project['otp']] = $project['id'];
        }

        $output->writeln('<info>Cache projets initialisé avec ' . count($projects) . ' projets.</info>');
    }

    /**
     * Initialiser le cache des données de packages
     */
    private function initializePackageDataCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des données de packages...</info>');

        $sql = 'SELECT Rel_Pack_Num, Creation_VISA, VISA_BE_2, VISA_BE_3, Project, Activity,
                       Creation_Date, Reservation_Date, DATE_BE_2, DATE_BE_3
                FROM tbl_released_package';
        $packages = $this->oldDb->fetchAllAssociative($sql);

        foreach ($packages as $package) {
            $this->packageDataCache[$package['Rel_Pack_Num']] = $package;
        }

        $output->writeln('<info>Cache packages initialisé avec ' . count($packages) . ' packages.</info>');
    }

    /**
     * Initialiser le cache des superviseurs
     */
    private function initializeSupervisorCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des superviseurs...</info>');

        try {
            $sql = 'SELECT Code, Description FROM tbl_supervisor';
            $supervisors = $this->oldDb->fetchAllAssociative($sql);

            foreach ($supervisors as $supervisor) {
                $code = $supervisor['Code'];
                $description = $supervisor['Description'] ?? '';

                // Utiliser notre système de recherche d'utilisateur pour identifier le superviseur
                $user = $this->findUserFlexible($description);
                $this->supervisorCache[$code] = $user;
            }

            $output->writeln('<info>Cache superviseurs initialisé avec ' . count($supervisors) . ' superviseurs.</info>');
        } catch (\Exception $e) {
            $output->writeln('<e>Erreur lors de l\'initialisation du cache superviseurs: ' . $e->getMessage() . '</e>');
            $output->writeln('<comment>La table tbl_supervisor n\'existe peut-être pas dans l\'ancienne base.</comment>');
        }
    }

    /**
     * Initialiser le cache des matériaux
     */
    private function initializeMaterialCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des matériaux...</info>');

        try {
            $materials = $this->materials->findAll();

            foreach ($materials as $material) {
                $this->materialCache[$material->getReference()] = $material->getId();
            }

            $output->writeln('<info>Cache matériaux initialisé avec ' . count($materials) . ' matériaux.</info>');
        } catch (\Exception $e) {
            $output->writeln('<error>Erreur lors de l\'initialisation du cache matériaux: ' . $e->getMessage() . '</error>');
            $output->writeln('<comment>La table material n\'existe peut-être pas ou est vide.</comment>');
        }
    }

    /**
     * Normaliser un nom pour la recherche
     */
    private function normalizeName(string $s): string
    {
        $s = trim($s);
        $s = preg_replace('/[^\p{L}\p{N}\s]/u', '', $s);

        if (class_exists(\Normalizer::class)) {
            $s = \Normalizer::normalize($s, \Normalizer::FORM_D);
            $s = preg_replace('/\p{M}/u', '', $s);
        }

        return mb_strtoupper($s, 'UTF-8');
    }

    /**
     * Décoder les entités HTML dans une chaîne
     */
    private function decodeHtmlEntities(?string $text): ?string
    {
        if ($text === null || $text === '') {
            return $text;
        }

        // Décoder les entités HTML
        $decoded = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Nettoyer les guillemets multiples qui peuvent rester
        $decoded = preg_replace('/"{2,}/', '"', $decoded);

        return $decoded;
    }

    /**
     * Créer les projets manquants avec SQL optimisé
     */
    private function createMissingProjectsOptimized(OutputInterface $output): void
    {
        $output->writeln('<info>Création des projets manquants (optimisé)...</info>');

        // Récupérer tous les codes de projets uniques de l'ancienne base
        $projectCodes = $this->oldDb->fetchAllAssociative(
            'SELECT DISTINCT Project FROM tbl_released_package WHERE Project IS NOT NULL'
        );

        $toCreate = [];
        foreach ($projectCodes as $row) {
            $otpCode = $row['Project'];
            if (!isset($this->projectIdCache[$otpCode])) {
                $toCreate[] = $otpCode;
            }
        }

        if (empty($toCreate)) {
            $output->writeln('<info>Aucun projet à créer.</info>');
            return;
        }

        // Créer les projets en batch avec SQL brut
        $values = [];
        $params = [];

        foreach ($toCreate as $otpCode) {
            $values[] = "(?, ?, 'active')";
            $params[] = $otpCode;
            $params[] = "Projet migré: " . $otpCode;
        }

        $sql = 'INSERT INTO project (otp, title, status) VALUES ' . implode(', ', $values);
        $this->em->getConnection()->executeStatement($sql, $params);

        // Mettre à jour le cache
        $newProjects = $this->em->getConnection()->fetchAllAssociative(
            'SELECT id, otp FROM project WHERE otp IN (' . implode(',', array_fill(0, count($toCreate), '?')) . ')',
            $toCreate
        );

        foreach ($newProjects as $project) {
            $this->projectIdCache[$project['otp']] = $project['id'];
        }

        $output->writeln('<info>' . count($toCreate) . ' projets créés avec succès.</info>');
    }

    /**
     * Migrer les packages avec SQL brut optimisé
     */
    private function migratePackagesOptimized(OutputInterface $output): array
    {
        $output->writeln('<info>Migration des packages (optimisé)...</info>');

        // Récupérer tous les packages legacy
        $sql = 'SELECT * FROM tbl_released_package';
        $packages = $this->oldDb->fetchAllAssociative($sql);

        $validPackages = [];

        // Préparer les données pour l'insertion en batch
        foreach ($packages as $package) {
            $ownerId = $this->findUserIdOptimized($package['Rel_Pack_Owner']);
            $verifId = $this->findUserIdOptimized($package['Verif_Req_Owner']);
            $validId = $this->findUserIdOptimized($package['BE_3_Req_Owner']);
            $projectId = $this->projectIdCache[$package['Project']] ?? null;

            if (!$ownerId || !$projectId) {
                $output->writeln(sprintf(
                    '<e>Package %s ignoré: owner=%s, project=%s</e>',
                    $package['Rel_Pack_Num'],
                    $package['Rel_Pack_Owner'] ?? 'null',
                    $package['Project'] ?? 'null'
                ));
                continue;
            }

            $validPackages[] = [
                'legacy_num' => $package['Rel_Pack_Num'],
                'owner_id' => $ownerId,
                'verif_id' => $verifId,
                'valid_id' => $validId,
                'project_id' => $projectId,
                'description' => $this->decodeHtmlEntities($package['Observations'] ?? ''),
                'activity' => $this->decodeHtmlEntities($package['Activity'] ?? ''),
                'ex' => $this->decodeHtmlEntities($package['Ex'] ?? ''),
                'reservation_date' => $this->formatDateForSql($package['Reservation_Date'] ?? null),
                'creation_date' => $this->formatDateForSql($package['Creation_Date'] ?? null)
            ];
        }

        if (empty($validPackages)) {
            $output->writeln('<e>Aucun package valide à migrer !</e>');
            return [];
        }

        // Préparer la base pour l'insertion avec IDs explicites
        $this->preparePackageTableForExplicitIds($output);

        // Insertion en batch avec SQL brut
        $this->insertPackagesBatch($validPackages, $output);

        // Récupérer les IDs générés pour le mapping
        $packageMapping = $this->buildPackageMapping($validPackages, $output);

        // Restaurer les paramètres de la base de données
        $this->restorePackageTableSettings($output);

        $output->writeln('<info>' . count($packageMapping) . ' packages migrés avec succès.</info>');
        return $packageMapping;
    }

    /**
     * Trouver l'ID d'un utilisateur de manière optimisée avec recherche flexible
     */
    private function findUserIdOptimized(?string $raw): ?int
    {
        $user = $this->findUserWithCache($raw);
        return $user ? $user->getId() : null;
    }

    /**
     * Trouver l'ID d'un superviseur à partir du code
     */
    private function findSupervisorIdOptimized(?string $supervisorCode): ?int
    {
        if (empty($supervisorCode)) {
            return null;
        }

        // Vérifier le cache des superviseurs
        if (isset($this->supervisorCache[$supervisorCode])) {
            $user = $this->supervisorCache[$supervisorCode];
            return $user ? $user->getId() : null;
        }

        // Si pas trouvé dans le cache, retourner null
        return null;
    }

    /**
     * Trouver l'ID d'un matériau à partir de sa référence
     */
    private function findMaterialIdOptimized(?string $materialReference): ?int
    {
        if (empty($materialReference)) {
            return null;
        }

        // Nettoyer la référence (trim)
        $cleanReference = trim($materialReference);

        // Vérifier le cache des matériaux
        if (isset($this->materialCache[$cleanReference])) {
            return $this->materialCache[$cleanReference];
        }

        // Si pas trouvé dans le cache, retourner null
        return null;
    }

    /**
     * Initialiser le cache des ProductCode
     */
    private function initializeProductCodeCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des ProductCode...</info>');

        try {
            $productCodes = $this->productCodes->findAll();

            foreach ($productCodes as $productCode) {
                $this->productCodeCache[$productCode->getCode()] = $productCode->getId();
            }

            $output->writeln('<info>Cache ProductCode initialisé avec ' . count($productCodes) . ' codes produit.</info>');
        } catch (\Exception $e) {
            $output->writeln('<error>Erreur lors de l\'initialisation du cache ProductCode: ' . $e->getMessage() . '</error>');
            $output->writeln('<comment>La table product_code n\'existe peut-être pas ou est vide.</comment>');
        }
    }

    /**
     * Trouver l'ID d'un ProductCode à partir de son code, ou le créer s'il n'existe pas
     */
    private function findProductCodeIdOptimized(?string $productCodeString): ?int
    {
        if (empty($productCodeString)) {
            return null;
        }

        // Nettoyer le code (trim)
        $cleanCode = trim($productCodeString);

        // Vérifier le cache des ProductCode
        if (isset($this->productCodeCache[$cleanCode])) {
            return $this->productCodeCache[$cleanCode];
        }

        // Si pas trouvé dans le cache, créer un nouveau ProductCode avec etat = false
        try {
            $productCode = $this->productCodes->findOrCreateByCode($cleanCode, false);

            // Mettre à jour le cache
            $this->productCodeCache[$cleanCode] = $productCode->getId();

            return $productCode->getId();
        } catch (\Exception $e) {
            // En cas d'erreur, retourner null
            return null;
        }
    }

    /**
     * Trouver un utilisateur avec cache et recherche flexible
     */
    private function findUserWithCache(?string $raw): ?User
    {
        // Si vide, retourner null directement
        if (empty($raw)) {
            return null;
        }

        // Vérifier le cache d'abord
        $cacheKey = trim($raw);
        if (isset($this->userCache[$cacheKey])) {
            $cachedUser = $this->userCache[$cacheKey];
            if ($cachedUser === 'ADMIN_FALLBACK') {
                return $this->adminUser;
            } elseif ($cachedUser === 'NOT_FOUND') {
                return null;
            } else {
                return $cachedUser;
            }
        }

        // Pas en cache, faire la recherche complète
        $user = $this->findUserFlexible($raw);

        // Mettre en cache le résultat
        if ($user === $this->adminUser) {
            $this->userCache[$cacheKey] = 'ADMIN_FALLBACK';
        } elseif ($user === null) {
            $this->userCache[$cacheKey] = 'NOT_FOUND';
        } else {
            $this->userCache[$cacheKey] = $user;
        }

        return $user;
    }

    /**
     * Recherche flexible d'utilisateur
     */
    private function findUserFlexible(?string $raw): ?User
    {
        if (empty($raw)) {
            return null;
        }

        // 1) Normalisation
        $clean = $this->normalizeName($raw);
        $parts = preg_split('/\s+/', $clean);
        $lastName = $parts[0] ?? '';
        $firstPart = $parts[1] ?? '';

        // 2) Exact match
        $allUsers = $this->users->findAll();
        $matches = [];
        foreach ($allUsers as $u) {
            if ($this->normalizeName($u->getNom()) === $lastName) {
                $matches[] = $u;
            }
        }

        if (count($matches) === 1) {
            return $matches[0];
        }

        // 3) Initiales prénom
        if ($firstPart && count($matches) > 1) {
            foreach ($matches as $u) {
                $prenomNorm = $this->normalizeName($u->getPrenom() ?? '');
                $initials = '';
                foreach (preg_split('/\s+/', $prenomNorm) as $p) {
                    $initials .= mb_substr($p, 0, 1, 'UTF-8');
                }
                if ($initials === $firstPart) {
                    return $u;
                }
            }
        }

        // 4) Inclusion partielle
        foreach ($allUsers as $u) {
            if (mb_stripos($this->normalizeName($u->getNom()), $lastName, 0, 'UTF-8') !== false) {
                return $u;
            }
        }

        // 5) Fallback fuzzy (Levenshtein)
        $closest = null;
        $minDist = PHP_INT_MAX;
        foreach ($allUsers as $u) {
            $dist = levenshtein($lastName, $this->normalizeName($u->getNom()));
            if ($dist < $minDist) {
                $minDist = $dist;
                $closest = $u;
            }
        }
        if ($closest && $minDist <= 1) { // Seuil strict pour éviter les mauvaises correspondances
            return $closest;
        }

        // 6) Fallback vers l'utilisateur Admin
        return $this->adminUser;
    }

    /**
     * Formater une date pour SQL
     */
    private function formatDateForSql(?string $date): ?string
    {
        if (empty($date) || !$this->isValidDate($date)) {
            return null;
        }

        try {
            $dateTime = new \DateTime($date);
            return $dateTime->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Vérifier si une date est valide
     */
    private function isValidDate(?string $date): bool
    {
        if (empty($date)) {
            return false;
        }

        // Vérifier si la date contient des années négatives ou invalides
        if (strpos($date, '-0001') !== false || strpos($date, '0000') !== false) {
            return false;
        }

        try {
            $dateTime = new \DateTime($date);
            // Vérifier que l'année est raisonnable (après 1900)
            return $dateTime->format('Y') >= 1900;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Préparer la table released_package pour l'insertion avec IDs explicites
     */
    private function preparePackageTableForExplicitIds(OutputInterface $output): void
    {
        $output->writeln('<info>Préparation de la table released_package pour IDs explicites...</info>');

        $connection = $this->em->getConnection();
        $platform = $connection->getDatabasePlatform();

        if ($platform->getName() === 'mysql') {
            // Pour MySQL, désactiver temporairement les vérifications de clés étrangères
            // et permettre l'insertion d'IDs explicites
            $connection->executeStatement('SET FOREIGN_KEY_CHECKS = 0');
            $connection->executeStatement('SET SESSION sql_mode = ""');
            $output->writeln('<comment>Vérifications MySQL désactivées temporairement.</comment>');
        }

        $output->writeln('<info>Table préparée pour l\'insertion avec IDs explicites.</info>');
    }

    /**
     * Insérer les packages en batch
     */
    private function insertPackagesBatch(array $packages, OutputInterface $output): void
    {
        $batchSize = 500; // Augmenter la taille des batches pour plus de performance
        $batches = array_chunk($packages, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $package) {
                $values[] = '(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';
                $params = array_merge($params, [
                    $package['legacy_num'], // Utiliser Rel_Pack_Num comme ID
                    $package['owner_id'],
                    $package['verif_id'],
                    $package['valid_id'],
                    $package['project_id'],
                    $package['description'],
                    $package['activity'],
                    $package['ex'],
                    $package['reservation_date'],
                    $package['creation_date']
                ]);
            }

            $sql = 'INSERT INTO released_package
                    (id, owner_id, verif_id, valid_id, project_relation_id, description, activity, ex, reservation_date, creation_date)
                    VALUES ' . implode(', ', $values);

            $this->em->getConnection()->executeStatement($sql, $params);

            $output->writeln('<comment>Batch ' . ($batchIndex + 1) .
                '/' . count($batches) . ' inséré (' . count($batch) . ' packages)</comment>');
        }
    }

    /**
     * Construire le mapping des packages
     */
    private function buildPackageMapping(array $validPackages, OutputInterface $output): array
    {
        $mapping = [];

        // Comme nous utilisons Rel_Pack_Num comme ID, le mapping est direct
        foreach ($validPackages as $package) {
            $mapping[$package['legacy_num']] = $package['legacy_num'];
        }

        $output->writeln('<info>Mapping construit pour ' . count($mapping) . ' packages.</info>');
        return $mapping;
    }

    /**
     * Restaurer les paramètres de la table released_package
     */
    private function restorePackageTableSettings(OutputInterface $output): void
    {
        $output->writeln('<info>Restauration des paramètres de la table released_package...</info>');

        $connection = $this->em->getConnection();
        $platform = $connection->getDatabasePlatform();

        if ($platform->getName() === 'mysql') {
            // Réactiver les vérifications de clés étrangères
            $connection->executeStatement('SET FOREIGN_KEY_CHECKS = 1');
            $output->writeln('<comment>Vérifications MySQL réactivées.</comment>');
        }

        $output->writeln('<info>Paramètres de la table restaurés.</info>');
    }

    /**
     * Migrer les documents avec optimisations
     */
    private function migrateDocumentsOptimized(array $packageMapping, ?int $limit, OutputInterface $output): void
    {
        $output->writeln('<info>Migration des documents (optimisé)...</info>');

        // Récupérer les documents legacy
        if ($limit) {
            $sql = 'SELECT * FROM tbl_released_drawing ORDER BY Reference LIMIT ' . (int)$limit;
        } else {
            $sql = 'SELECT * FROM tbl_released_drawing';
        }
        $documents = $this->oldDb->fetchAllAssociative($sql);

        $validDocuments = [];

        $output->writeln('<info>Traitement de ' . count($documents) . ' documents...</info>');

        // Préparer les données pour l'insertion en batch
        foreach ($documents as $index => $doc) {
            if ($index % 100 === 0) {
                $output->writeln('<comment>Traitement document ' .
                    ($index + 1) . '/' . count($documents) . '</comment>');
            }

            if (!isset($packageMapping[$doc['Rel_Pack_Num']])) {
                $output->writeln(sprintf(
                    '<e>Package introuvable pour document %s (Rel_Pack_Num: %s)</e>',
                    $doc['Reference'], $doc['Rel_Pack_Num']
                ));
                continue;
            }

            $packageId = $packageMapping[$doc['Rel_Pack_Num']];

            // Récupérer le superviseur à partir du code
            $superviseurId = $this->findSupervisorIdOptimized($doc['Supervisor'] ?? null);

            // Récupérer le matériau à partir de sa référence (pour la table document_materials)
            $materialId = $this->findMaterialIdOptimized($doc['FXXX'] ?? null);

            // Récupérer le ProductCode à partir de son code
            $productCodeId = $this->findProductCodeIdOptimized($doc['Product_Code'] ?? null);

            $validDocuments[] = [
                'package_id' => $packageId,
                'superviseur_id' => $superviseurId,
                'reference' => $this->decodeHtmlEntities($doc['Reference'] ?? ''),
                'ref_rev' => $this->decodeHtmlEntities($doc['Ref_Rev'] ?? ''),
                'ref_title_fra' => $this->decodeHtmlEntities($doc['Ref_Title'] ?? ''),
                'prod_draw' => $this->decodeHtmlEntities($doc['Prod_Draw'] ?? ''),
                'prod_draw_rev' => $this->decodeHtmlEntities($doc['Prod_Draw_Rev'] ?? ''),
                'alias' => $this->decodeHtmlEntities($doc['Alias'] ?? ''),
                'doc_type' => $this->decodeHtmlEntities($doc['Doc_Type'] ?? ''),
                'material_type' => $this->decodeHtmlEntities($doc['Material_Type'] ?? ''),
                'proc_type' => $this->decodeHtmlEntities($doc['Proc_Type'] ?? ''),
                'inventory_impact' => $this->decodeHtmlEntities($doc['Inventory_Impact'] ?? ''),
                'action' => $this->decodeHtmlEntities($doc['Action'] ?? ''),
                'ex' => $this->decodeHtmlEntities($doc['Ex'] ?? ''),
                'material_id' => $materialId, // Pour la table document_materials
                'doc_impact' => isset($doc['Doc_Impact']) ? (int)$doc['Doc_Impact'] : 0,
                // Champs annexes supplémentaires
                'id_aletiq' => isset($doc['ID_ALETIQ']) ? (int)$doc['ID_ALETIQ'] : null,
                'cust_drawing' => $this->decodeHtmlEntities($doc['Cust_Drawing'] ?? null),
                'cust_drawing_rev' => $this->decodeHtmlEntities($doc['Cust_Drawing_Rev'] ?? null),
                'weight' => isset($doc['Weight']) ? (int)$doc['Weight'] : null,
                'weight_unit' => $this->decodeHtmlEntities($doc['Weight_Unit'] ?? null),
                'plating_surface' => isset($doc['Plating_Surface']) ? (int)$doc['Plating_Surface'] : null,
                'plating_surface_unit' => $this->decodeHtmlEntities($doc['Plating_Surface_Unit'] ?? null),
                'internal_mach_rec' => isset($doc['Internal_Mach_Rec']) ? (bool)$doc['Internal_Mach_Rec'] : null,
                'cls' => isset($doc['CLS']) ? (int)$doc['CLS'] : null,
                'moq' => isset($doc['MOQ']) ? (int)$doc['MOQ'] : null,
                'product_code_id' => $productCodeId,
                'prod_agent' => $this->decodeHtmlEntities($doc['Prod_Agent'] ?? null),
                'mof' => $this->decodeHtmlEntities($doc['MOF'] ?? null),
                'commodity_code' => $this->decodeHtmlEntities($doc['Commodity_Code'] ?? null),
                'purchasing_group' => $this->decodeHtmlEntities($doc['Purchasing_Group'] ?? null),
                'mat_prod_type' => $this->decodeHtmlEntities($doc['Mat_Prod_Type'] ?? null),
                'unit' => $this->decodeHtmlEntities($doc['Unit'] ?? null),
                'leadtime' => isset($doc['leadtime']) ? (int)$doc['leadtime'] : null,
                'pris_dans1' => $this->decodeHtmlEntities($doc['Pris_Dans1'] ?? null),
                'pris_dans2' => $this->decodeHtmlEntities($doc['Pris_Dans2'] ?? null),
                'eccn' => $this->decodeHtmlEntities($doc['ECCN'] ?? null),
                'rdo' => $this->decodeHtmlEntities($doc['RDO'] ?? null),
                'hts' => $this->decodeHtmlEntities($doc['HTS'] ?? null),
                'fia' => $this->decodeHtmlEntities($doc['FIA'] ?? null),
                'metro_time' => isset($doc['Metro_Time']) ? (int)$doc['Metro_Time'] : null,
                'q_inspection' => $this->parseArrayField($doc['Q_Inspection'] ?? null, 'q_inspection'),
                'q_dynamization' => $doc['Q_Dynamization'] ?? null,
                'q_doc_rec' => $this->parseArrayField($doc['Q_Doc_Req'] ?? null, 'q_doc_rec'),
                'q_control_routing' => $doc['Q_Control_Routing'] ?? null,
                'critical_complete' => isset($doc['Critical_Complete']) ? (int)$doc['Critical_Complete'] : null,
                'switch_aletiq' => isset($doc['SWITCH_ALETIQ']) ? (bool)$doc['SWITCH_ALETIQ'] : null,
                'metro_control' => $this->parseArrayField($doc['Metro_Control'] ?? null, 'metro_control'),
                'legacy_data' => $doc // Stocker toutes les données legacy pour les visas et dates
            ];
        }

        if (empty($validDocuments)) {
            $output->writeln('<e>Aucun document valide à migrer !</e>');
            return;
        }

        // Insertion en batch avec SQL brut
        $this->insertDocumentsBatch($validDocuments, $output);

        // Récupérer les IDs générés et créer les visas
        $this->createDocumentVisasBatch($validDocuments, $output);

        $output->writeln('<info>' . count($validDocuments) . ' documents migrés avec succès.</info>');
    }

    /**
     * Insérer les documents en batch
     */
    private function insertDocumentsBatch(array $documents, OutputInterface $output): void
    {
        $batchSize = 200; // Taille plus petite pour les documents (plus de colonnes)
        $batches = array_chunk($documents, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $doc) {
                $values[] = '(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';
                $params = array_merge($params, [
                    $doc['package_id'],
                    $doc['superviseur_id'],
                    $doc['reference'],
                    $doc['ref_rev'],
                    $doc['ref_title_fra'],
                    $doc['prod_draw'],
                    $doc['prod_draw_rev'],
                    $doc['alias'],
                    $doc['doc_type'],
                    $doc['material_type'],
                    $doc['proc_type'],
                    $doc['inventory_impact'],
                    $doc['action'],
                    $doc['ex'],
                    $doc['doc_impact'],
                    // Champs annexes
                    $doc['id_aletiq'],
                    $doc['cust_drawing'],
                    $doc['cust_drawing_rev'],
                    $doc['weight'],
                    $doc['weight_unit'],
                    $doc['plating_surface'],
                    $doc['plating_surface_unit'],
                    $doc['internal_mach_rec'] ? 1 : 0,
                    $doc['cls'],
                    $doc['moq'],
                    $doc['product_code_id'],
                    $doc['prod_agent'],
                    $doc['mof'],
                    $doc['commodity_code'],
                    $doc['purchasing_group'],
                    $doc['mat_prod_type'],
                    $doc['unit'],
                    $doc['leadtime'],
                    $doc['pris_dans1'],
                    $doc['pris_dans2'],
                    $doc['eccn'],
                    $doc['rdo'],
                    $doc['hts'],
                    $doc['fia'],
                    $doc['metro_time'],
                    $doc['q_inspection'] ? json_encode($doc['q_inspection']) : null,
                    $doc['q_dynamization'],
                    $doc['q_doc_rec'] ? json_encode($doc['q_doc_rec']) : null,
                    $doc['q_control_routing'],
                    $doc['critical_complete'],
                    $doc['switch_aletiq'] ? 1 : 0,
                    $doc['metro_control'] ? json_encode($doc['metro_control']) : null,
                    '{}' // current_steps par défaut (JSON vide)
                ]);
            }

            $sql = 'INSERT INTO document
                    (rel_pack_id, superviseur_id, reference, ref_rev, ref_title_fra, prod_draw, prod_draw_rev, alias,
                     doc_type, material_type, proc_type, inventory_impact, action, ex, doc_impact,
                     id_aletiq, cust_drawing, cust_drawing_rev, weight, weight_unit, plating_surface, plating_surface_unit,
                     internal_mach_rec, cls, moq, product_code_id, prod_agent, mof, commodity_code, purchasing_group,
                     mat_prod_type, unit, leadtime, pris_dans1, pris_dans2, eccn, rdo, hts, fia, metro_time,
                     q_inspection, q_dynamization, q_doc_rec, q_control_routing, critical_complete, switch_aletiq,
                     metro_control, current_steps)
                    VALUES ' . implode(', ', $values);

            $this->em->getConnection()->executeStatement($sql, $params);

            $output->writeln('<comment>Batch ' . ($batchIndex + 1) .
                '/' . count($batches) . ' inséré (' . count($batch) . ' documents)</comment>');
        }
    }

    /**
     * Créer les visas des documents en batch
     */
    private function createDocumentVisasBatch(array $validDocuments, OutputInterface $output): void
    {
        $output->writeln('<info>Création des visas en batch...</info>');

        // Récupérer les IDs des documents créés
        $count = count($validDocuments);
        $sql = "SELECT id FROM document ORDER BY id DESC LIMIT $count";
        $documentIds = $this->em->getConnection()->fetchAllAssociative($sql);
        $documentIds = array_reverse($documentIds); // Inverser pour avoir l'ordre d'insertion

        $allVisas = [];

        // Préparer tous les visas à créer
        foreach ($validDocuments as $index => $docData) {
            if (!isset($documentIds[$index])) {
                continue;
            }

            $documentId = $documentIds[$index]['id'];
            $legacyData = $docData['legacy_data'];

            // Générer et collecter TOUS les visas du document (BE + legacy)
            $allDocumentVisas = $this->getAllDocumentVisas($legacyData);
            $documentBEVisas = []; // Tracker les visas BE créés pour ce document

            foreach ($allDocumentVisas as $visa) {
                // Utiliser le vrai utilisateur ou fallback vers Admin
                $validatorId = $visa['user'] ? $visa['user']->getId() : ($this->adminUser ? $this->adminUser->getId() : null);

                $allVisas[] = [
                    'document_id'  => $documentId,
                    'name'         => $visa['name'],
                    'date_visa'    => $visa['date'],
                    'status'       => 'valid',
                    'validator_id' => $validatorId
                ];

                // Tracker les visas BE pour la détermination d'état
                if (in_array($visa['name'], ['visa_BE_0', 'visa_BE_1', 'visa_BE'])) {
                    $documentBEVisas[] = $visa['name'];
                }
            }

            // Déterminer les états du workflow basés sur les visas BE créés pour ce document
            $states = $this->determineStatesFromDocumentVisas($legacyData, $documentBEVisas);
            foreach ($states as $state) {
                $this->stateCounts[$state] = ($this->stateCounts[$state] ?? 0) + 1;
            }

            // 4. Créer les state_timestamps et updates
            $stateTimestamps = $this->createStateTimestamps($legacyData, $documentBEVisas, $states);
            $updates = $this->createUpdatesHistory($legacyData);

            // 5. Mettre à jour le document avec current_steps, state_timestamps et updates
            if (!empty($states)) {
                $statesJson = json_encode(array_fill_keys($states, 1));
                $stateTimestampsJson = json_encode($stateTimestamps);
                $updatesJson = json_encode($updates);

                $updateSql = 'UPDATE document SET current_steps = ?, state_timestamps = ?, updates = ? WHERE id = ?';
                $this->em->getConnection()->executeStatement($updateSql, [
                    $statesJson,
                    $stateTimestampsJson,
                    $updatesJson,
                    $documentId
                ]);
            }
        }

        // Insérer tous les visas en batch
        if (!empty($allVisas)) {
            $this->insertVisasBatch($allVisas, $output);
        }

        $output->writeln('<info>' . count($allVisas) . ' visas créés avec succès.</info>');
    }

    /**
     * Insérer les visas en batch
     */
    private function insertVisasBatch(array $visas, OutputInterface $output): void
    {
        $batchSize = 1000;
        $batches = array_chunk($visas, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $visa) {
                $values[] = '(?, ?, ?, ?, ?)';
                $params = array_merge($params, [
                    $visa['document_id'],
                    $visa['name'],
                    $visa['date_visa'],
                    $visa['status'],
                    $visa['validator_id']
                ]);
            }

            $sql = 'INSERT INTO visa (released_drawing_id, name, date_visa, status, validator_id)
                    VALUES ' . implode(', ', $values);

            $this->em->getConnection()->executeStatement($sql, $params);

            $output->writeln('<comment>Batch visas ' . ($batchIndex + 1) .
                '/' . count($batches) . ' inséré (' . count($batch) . ' visas)</comment>');
        }
    }

    /**
     * Récupérer TOUS les visas du document (BE + legacy) depuis tbl_released_drawing
     */
    private function getAllDocumentVisas(array $legacyData): array
    {
        $visas = [];

        // Helper pour vérifier si un champ est non vide
        $isNotEmpty = function($value) {
            return trim($value ?? '') !== '';
        };

        // 1. VISAS BE - Créés à partir des informations du package (tbl_released_package)
        // car les colonnes BE n'existent pas dans tbl_released_drawing
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        if ($packageNum && isset($this->packageDataCache[$packageNum])) {
            $packageData = $this->packageDataCache[$packageNum];

            // visa_BE_0 : généré si Creation_VISA est renseigné dans le package
            if ($isNotEmpty($packageData['Creation_VISA'])) {
                $creationDate = $packageData['Creation_Date'] ?? null;
                if ($creationDate && $this->isValidDate($creationDate)) {
                    $user = $this->findUserWithCache($packageData['Creation_VISA']);
                    $visas[] = [
                        'name' => 'visa_BE_0',
                        'date' => $this->formatDateForSql($creationDate),
                        'user' => $user
                    ];
                }
            }

            // visa_BE_1 : généré si VISA_BE_2 est renseigné dans le package
            if ($isNotEmpty($packageData['VISA_BE_2'])) {
                $dateBE2 = $packageData['DATE_BE_2'] ?? null;
                if ($dateBE2 && $this->isValidDate($dateBE2)) {
                    $user = $this->findUserWithCache($packageData['VISA_BE_2']);
                    $visas[] = [
                        'name' => 'visa_BE_1',
                        'date' => $this->formatDateForSql($dateBE2),
                        'user' => $user
                    ];
                }
            }

            // visa_BE : généré si VISA_BE_3 est renseigné dans le package
            if ($isNotEmpty($packageData['VISA_BE_3'])) {
                $dateBE3 = $packageData['DATE_BE_3'] ?? null;
                if ($dateBE3 && $this->isValidDate($dateBE3)) {
                    $user = $this->findUserWithCache($packageData['VISA_BE_3']);
                    $visas[] = [
                        'name' => 'visa_BE',
                        'date' => $this->formatDateForSql($dateBE3),
                        'user' => $user
                    ];
                }
            }
        }

        // 2. VISAS LEGACY - Récupérés depuis les colonnes du document (tbl_released_drawing)
        $visaMapping = [
            'Product'       => 'visa_Produit',
            'Project'       => 'visa_Project',
            'Quality'       => 'visa_Quality',
            'Inventory'     => 'visa_Logistique',
            'Prod'          => 'visa_prod',
            'Method'        => 'visa_Methode_assemblage',
            'Supply'        => 'visa_Planning',
            'Metro'         => 'visa_Metro',
            'PUR_1'         => 'visa_Achat_Rfq',
            'PUR_2'         => 'visa_Achat_F30',
            'PUR_3'         => 'visa_Achat_FIA',
            'PUR_4'         => 'visa_Achat_RoHs_REACH',
            'PUR_5'         => 'visa_Achat_Hts',
            'GID'           => 'visa_Core_Data',
            'GID_2'         => 'visa_Prod_Data',
            'ROUTING_ENTRY' => 'visa_GID',
            'Finance'       => 'visa_Costing'
        ];

        foreach ($visaMapping as $visaKey => $visaName) {
            $visaCol = "VISA_{$visaKey}";
            $dateCol = "DATE_{$visaKey}";

            if (
                isset($legacyData[$visaCol]) && $isNotEmpty($legacyData[$visaCol]) &&
                isset($legacyData[$dateCol]) && $this->isValidDate($legacyData[$dateCol])
            ) {
                $user = $this->findUserWithCache($legacyData[$visaCol]);
                $visas[] = [
                    'name' => $visaName,
                    'date' => $this->formatDateForSql($legacyData[$dateCol]),
                    'user' => $user
                ];
            }
        }

        return $visas;
    }

    /**
     * Déterminer les états basés sur les visas BE présents sur le document
     */
    private function determineStatesFromDocumentVisas(array $legacyData, array $documentBEVisas): array
    {
        $activeStates = [];

        // Helper pour vérifier si un champ est vide (TRIM = '')
        $isEmpty = function($value) {
            return trim($value ?? '') === '';
        };

        // Helper pour vérifier si un champ n'est pas vide (TRIM <> '')
        $isNotEmpty = function($value) {
            return trim($value ?? '') !== '';
        };

        // 1. CONDITIONS BE basées sur les visas BE présents sur ce document

        // BE_0 state: Document has no visa_BE_0 AND no visa_BE_1
        if (!in_array('visa_BE_0', $documentBEVisas) && !in_array('visa_BE_1', $documentBEVisas)) {
            $activeStates[] = 'BE_0';
            return $activeStates;
        }

        // BE_1 state: Document has visa_BE_0 but no visa_BE_1
        if (in_array('visa_BE_0', $documentBEVisas) && !in_array('visa_BE_1', $documentBEVisas)) {
            $activeStates[] = 'BE_1';
            return $activeStates;
        }

        // BE state: Document has visa_BE_1 but no visa_BE
        if (in_array('visa_BE_1', $documentBEVisas) && !in_array('visa_BE', $documentBEVisas)) {
            $activeStates[] = 'BE';
            return $activeStates;
        }

        // Post-BE states: Document has visa_BE (proceed to Product, Quality, etc.)
        if (!in_array('visa_BE', $documentBEVisas)) {
            // Cas de sécurité : si on arrive ici sans visa_BE, retomber en BE_0
            $activeStates[] = 'BE_0';
            return $activeStates;
        }

        // 2. CONDITIONS POST-BE (le document a visa_BE)
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        $packageData = $packageNum ? ($this->packageDataCache[$packageNum] ?? null) : null;

        $docType         = $legacyData['Doc_Type'] ?? '';
        $procType        = $legacyData['Proc_Type'] ?? '';
        $inventoryImpact = $legacyData['Inventory_Impact'] ?? '';
        $prodDraw        = $legacyData['Prod_Draw'] ?? '';
        $project         = $packageData['Project'] ?? '';
        $activity        = $packageData['Activity'] ?? '';

        // Product_Conditions
        if ($isEmpty($legacyData['VISA_Product'])) {
            $activeStates[] = 'Produit';
        }

        // Quality_Conditions
        if (in_array($docType, ['PUR', 'ASSY', 'DOC']) && $isEmpty($legacyData['VISA_Quality'])) {
            $activeStates[] = 'Quality';
        }

        // Inventory_Conditions (Qual_Logistique)
        if (
            $isEmpty($legacyData['VISA_Inventory']) &&
            $isEmpty($legacyData['VISA_GID']) &&
            $docType !== 'DOC' &&
            $inventoryImpact !== 'NO IMPACT'
        ) {
            $activeStates[] = 'Qual_Logistique';
        }

        // Project_Conditions
        if (
            $isEmpty($legacyData['VISA_Project']) &&
            $project !== 'STAND' &&
            (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT'))
        ) {
            $activeStates[] = 'Project';
        }

        // METRO_Conditions
        if (
            $isEmpty($legacyData['VISA_Metro']) &&
            (
                ($isNotEmpty($legacyData['VISA_Prod']) && $procType === 'E') ||
                ($isNotEmpty($legacyData['VISA_Quality']) && $docType === 'PUR')
            )
        ) {
            $activeStates[] = 'Metro';
        }

        // Q_PROD_Conditions (QProd)
        if (
            $isNotEmpty($legacyData['VISA_Metro']) &&
            in_array($docType, ['MACH', 'MOLD']) &&
            $isEmpty($legacyData['VISA_Q_PROD'])
        ) {
            $activeStates[] = 'QProd';
        }

        // Prod_ASSY_Conditions (Assembly)
        if (
            $isNotEmpty($legacyData['VISA_Product']) &&
            (
                ($docType === 'ASSY' && !str_starts_with($procType, 'F')) ||
                $docType === 'DOC'
            ) &&
            $isEmpty($legacyData['VISA_Prod'])
        ) {
            $activeStates[] = 'Assembly';
        }

        // Method_Conditions (Methode_assemblage)
        if (
            $isEmpty($legacyData['VISA_Method']) &&
            in_array($docType, ['ASSY', 'DOC']) &&
            !str_starts_with($activity, 'MOB_IND') &&
            $isNotEmpty($legacyData['VISA_Prod'])
        ) {
            $activeStates[] = 'Methode_assemblage';
        }

        // Prod_MACH_Conditions (Machining)
        if (
            $isNotEmpty($legacyData['VISA_Product']) &&
            $docType === 'MACH' &&
            $isEmpty($legacyData['VISA_Prod'])
        ) {
            $activeStates[] = 'Machining';
        }

        // Prod_MOLD_Conditions (Molding)
        if (
            $isNotEmpty($legacyData['VISA_Product']) &&
            $docType === 'MOLD' &&
            $isEmpty($legacyData['VISA_Prod'])
        ) {
            $activeStates[] = 'Molding';
        }

        // PUR_1_RFQ_Conditions (Achat_Rfq)
        if (
            $isEmpty($legacyData['VISA_PUR_1']) &&
            $docType === 'PUR' &&
            in_array($procType, ['', 'F', 'F30']) &&
            $isNotEmpty($legacyData['VISA_Quality']) &&
            $isNotEmpty($legacyData['VISA_Product'])
        ) {
            $activeStates[] = 'Achat_Rfq';
        }

        // PUR_2_PRISDANS_Conditions (Achat_F30)
        if (
            $isEmpty($legacyData['VISA_PUR_2']) &&
            $isNotEmpty($legacyData['VISA_PUR_1']) &&
            $procType === 'F30'
        ) {
            $activeStates[] = 'Achat_F30';
        }

        // PUR_3_Conditions (Achat_FIA)
        if (
            $isNotEmpty($legacyData['VISA_PUR_1']) &&
            $isEmpty($legacyData['VISA_PUR_3']) &&
            in_array($procType, ['F', 'F30']) &&
            $isNotEmpty($legacyData['VISA_GID_2'])
        ) {
            $activeStates[] = 'Achat_FIA';
        }

        // PUR_4_Conditions (Achat_RoHs_REACH)
        if (
            $isEmpty($legacyData['VISA_PUR_4']) &&
            $isNotEmpty($legacyData['VISA_PUR_1'])
        ) {
            $activeStates[] = 'Achat_RoHs_REACH';
        }

        // PUR_5_Conditions (Achat_Hts)
        if (
            $isEmpty($legacyData['VISA_PUR_5']) &&
            $isNotEmpty($legacyData['VISA_PUR_3'])
        ) {
            $activeStates[] = 'Achat_Hts';
        }

        // Supply_Conditions (Planning)
        if (
            in_array($docType, ['ASSY', 'MACH', 'MOLD', 'DOC']) &&
            $isNotEmpty($legacyData['VISA_Prod']) &&
            $isEmpty($legacyData['VISA_Supply'])
        ) {
            $activeStates[] = 'Planning';
        }

        // MOF_Conditions (Indus)
        if (
            (
                ($docType === 'ASSY' && $isNotEmpty($legacyData['VISA_Metro'])) ||
                $docType === 'DOC'
            ) &&
            $isNotEmpty($legacyData['VISA_Prod']) &&
            $isEmpty($legacyData['VISA_MOF'])
        ) {
            $activeStates[] = 'Indus';
        }

        // GID_1_Conditions (Core_Data) - Conditions exactes selon la requête SQL legacy
        if ($isEmpty($legacyData['VISA_GID'])) {
            $shouldAddCoreData = false;

            // Condition 1: PUR avec VISA_PUR_1 et Proc_Type = 'F'
            if (
                $isNotEmpty($legacyData['VISA_PUR_1']) &&
                $procType === 'F' &&
                $docType === 'PUR'
            ) {
                $shouldAddCoreData = true;
            }

            // Condition 2: PUR avec VISA_PUR_2 et Proc_Type = 'F30'
            if (
                $isNotEmpty($legacyData['VISA_PUR_2']) &&
                $procType === 'F30' &&
                $docType === 'PUR'
            ) {
                $shouldAddCoreData = true;
            }

            // Condition 3: MACH ou MOLD avec VISA_Metro et VISA_Supply
            if (
                in_array($docType, ['MACH', 'MOLD']) &&
                $isNotEmpty($legacyData['VISA_Metro']) &&
                $isNotEmpty($legacyData['VISA_Supply'])
            ) {
                $shouldAddCoreData = true;
            }

            // Condition 4: ASSY avec conditions complexes
            if (
                $docType === 'ASSY' &&
                $isNotEmpty($legacyData['VISA_Quality']) &&
                $isNotEmpty($legacyData['VISA_Metro']) &&
                $isNotEmpty($legacyData['VISA_Supply'])
            ) {
                // Sous-conditions pour ASSY
                $assyConditionMet = false;

                // Sous-condition 4a: Project <> 'STAND' ET VISA_Project <> '' ET (GA% OU FT%)
                if (
                    $project !== 'STAND' &&
                    $isNotEmpty($legacyData['VISA_Project']) &&
                    (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT'))
                ) {
                    $assyConditionMet = true;
                }

                // Sous-condition 4b: Project <> 'STAND' ET VISA_Project = '' ET NOT (GA% OU FT%)
                if (
                    $project !== 'STAND' &&
                    $isEmpty($legacyData['VISA_Project']) &&
                    !str_starts_with($prodDraw, 'GA') &&
                    !str_starts_with($prodDraw, 'FT')
                ) {
                    $assyConditionMet = true;
                }

                // Sous-condition 4c: Project = 'STAND' ET VISA_Project = ''
                if (
                    $project === 'STAND' &&
                    $isEmpty($legacyData['VISA_Project'])
                ) {
                    $assyConditionMet = true;
                }

                if ($assyConditionMet) {
                    $shouldAddCoreData = true;
                }
            }

            // Condition 5: DOC avec conditions complexes
            if (
                $docType === 'DOC' &&
                $isNotEmpty($legacyData['VISA_Quality']) &&
                $isNotEmpty($legacyData['VISA_Metro'])
            ) {
                // Sous-conditions pour DOC (mêmes que ASSY)
                $docConditionMet = false;

                // Sous-condition 5a: Project <> 'STAND' ET VISA_Project <> '' ET (GA% OU FT%)
                if (
                    $project !== 'STAND' &&
                    $isNotEmpty($legacyData['VISA_Project']) &&
                    (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT'))
                ) {
                    $docConditionMet = true;
                }

                // Sous-condition 5b: Project <> 'STAND' ET VISA_Project = '' ET NOT (GA% OU FT%)
                if (
                    $project !== 'STAND' &&
                    $isEmpty($legacyData['VISA_Project']) &&
                    !str_starts_with($prodDraw, 'GA') &&
                    !str_starts_with($prodDraw, 'FT')
                ) {
                    $docConditionMet = true;
                }

                // Sous-condition 5c: Project = 'STAND' ET VISA_Project = ''
                if (
                    $project === 'STAND' &&
                    $isEmpty($legacyData['VISA_Project'])
                ) {
                    $docConditionMet = true;
                }

                if ($docConditionMet) {
                    $shouldAddCoreData = true;
                }
            }

            // Vérifier les conditions d'inventaire (AND final de la requête)
            if ($shouldAddCoreData) {
                $inventoryOk = false;

                // Condition inventaire 1: VISA_Inventory <> '' ET (TO BE SCRAPPED OU TO BE UPDATED)
                if (
                    $isNotEmpty($legacyData['VISA_Inventory']) &&
                    in_array($inventoryImpact, ['TO BE SCRAPPED', 'TO BE UPDATED'])
                ) {
                    $inventoryOk = true;
                }

                // Condition inventaire 2: VISA_Inventory = '' ET (NO IMPACT OU Doc_Type = 'DOC')
                if (
                    $isEmpty($legacyData['VISA_Inventory']) &&
                    ($inventoryImpact === 'NO IMPACT' || $docType === 'DOC')
                ) {
                    $inventoryOk = true;
                }

                if ($inventoryOk) {
                    $activeStates[] = 'Core_Data';
                }
            }
        }

        // GID_2_Conditions (Prod_Data)
        if (
            $isNotEmpty($legacyData['VISA_GID']) &&
            $isEmpty($legacyData['VISA_GID_2'])
        ) {
            $activeStates[] = 'Prod_Data';
        }

        // LABO_Conditions (Laboratory/methode_Labo)
        if (
            $docType === 'ASSY' &&
            $isNotEmpty($legacyData['VISA_MOF']) &&
            $isEmpty($legacyData['VISA_LABO'])
        ) {
            $activeStates[] = 'Laboratory';
        }

        // ROUTING_ENTRY_Conditions (GID)
        if (
            $isEmpty($legacyData['VISA_ROUTING_ENTRY']) &&
            $isNotEmpty($legacyData['VISA_GID_2'])
        ) {
            $shouldAddGID = false;

            if ($docType === 'ASSY' && $isNotEmpty($legacyData['VISA_MOF'])) {
                $shouldAddGID = true;
            } elseif (
                in_array($docType, ['MOLD', 'MACH']) &&
                $isNotEmpty($legacyData['VISA_Prod'])
            ) {
                $shouldAddGID = true;
            }

            if ($shouldAddGID) {
                $activeStates[] = 'GID';
            }
        }

        // Finance_Conditions (Costing)
        if (
            $docType !== 'DOC' &&
            $isEmpty($legacyData['VISA_Finance'])
        ) {
            $shouldAddCosting = false;

            if ($docType === 'PUR' && $isNotEmpty($legacyData['VISA_PUR_3'])) {
                $shouldAddCosting = true;
            } elseif ($docType !== 'PUR' && $isNotEmpty($legacyData['VISA_ROUTING_ENTRY'])) {
                $shouldAddCosting = true;
            }

            if ($shouldAddCosting) {
                $activeStates[] = 'Costing';
            }
        }

        // Si aucun état trouvé et le document a visa_BE, déterminer le dernier état valide
        if (empty($activeStates)) {
            // Document terminé - déterminer le dernier état dans lequel il était
            $lastValidState = $this->determineLastValidState($legacyData, $docType, $procType);
            if ($lastValidState) {
                return [$lastValidState];
            }
            // Si aucun état spécifique trouvé, considérer comme terminé dans un état générique
            return ['Costing']; // État final par défaut pour les documents terminés
        }

        return $activeStates;
    }

    /**
     * Déterminer le dernier état valide pour un document terminé
     */
    private function determineLastValidState(array $legacyData, string $docType, string $procType): ?string
    {
        // Helper pour vérifier si un champ n'est pas vide
        $isNotEmpty = function($value) {
            return trim($value ?? '') !== '';
        };

        // Ordre de priorité des états finaux (du plus spécialisé au plus général)

        // 1. États de finalisation (Costing est généralement le dernier)
        if ($isNotEmpty($legacyData['VISA_Finance'])) {
            return 'Costing';
        }

        // 2. États de production avancés
        if ($isNotEmpty($legacyData['VISA_ROUTING_ENTRY'])) {
            return 'GID';
        }

        if ($isNotEmpty($legacyData['VISA_GID_2'])) {
            return 'Prod_Data';
        }

        if ($isNotEmpty($legacyData['VISA_GID'])) {
            return 'Core_Data';
        }

        // 3. États de laboratoire et méthodes
        if ($isNotEmpty($legacyData['VISA_LABO'])) {
            return 'Laboratory';
        }

        if ($isNotEmpty($legacyData['VISA_MOF'])) {
            return 'Indus';
        }

        // 4. États de planification
        if ($isNotEmpty($legacyData['VISA_Supply'])) {
            return 'Planning';
        }

        // 5. États d'achat (pour documents PUR)
        if ($docType === 'PUR') {
            if ($isNotEmpty($legacyData['VISA_PUR_5'])) {
                return 'Achat_Hts';
            }
            if ($isNotEmpty($legacyData['VISA_PUR_4'])) {
                return 'Achat_RoHs_REACH';
            }
            if ($isNotEmpty($legacyData['VISA_PUR_3'])) {
                return 'Achat_FIA';
            }
            if ($isNotEmpty($legacyData['VISA_PUR_2'])) {
                return 'Achat_F30';
            }
            if ($isNotEmpty($legacyData['VISA_PUR_1'])) {
                return 'Achat_Rfq';
            }
        }

        // 6. États de production selon le type
        if ($isNotEmpty($legacyData['VISA_Prod'])) {
            switch ($docType) {
                case 'ASSY':
                    return 'Assembly';
                case 'MACH':
                    return 'Machining';
                case 'MOLD':
                    return 'Molding';
                default:
                    return 'Assembly'; // État par défaut pour production
            }
        }

        // 7. États de contrôle qualité
        if ($isNotEmpty($legacyData['VISA_Q_PROD'])) {
            return 'QProd';
        }

        if ($isNotEmpty($legacyData['VISA_Metro'])) {
            return 'Metro';
        }

        // 8. États de méthodes
        if ($isNotEmpty($legacyData['VISA_Method'])) {
            return 'Methode_assemblage';
        }

        // 9. États de base
        if ($isNotEmpty($legacyData['VISA_Project'])) {
            return 'Project';
        }

        if ($isNotEmpty($legacyData['VISA_Inventory'])) {
            return 'Qual_Logistique';
        }

        if ($isNotEmpty($legacyData['VISA_Quality'])) {
            return 'Quality';
        }

        if ($isNotEmpty($legacyData['VISA_Product'])) {
            return 'Produit';
        }

        // Si aucun visa spécialisé trouvé, retourner null
        return null;
    }

    /**
     * Déterminer les états selon les conditions legacy exactes (DEPRECATED - use determineStatesFromDocumentVisas)
     */
    private function determineStatesFromLegacyConditions(array $legacyData): array
    {
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        $packageData = $packageNum ? ($this->packageDataCache[$packageNum] ?? null) : null;

        $activeStates = [];

        // Helper pour vérifier si un champ est vide (TRIM = '')
        $isEmpty = function($value) {
            return trim($value ?? '') === '';
        };

        // Helper pour vérifier si un champ n'est pas vide (TRIM <> '')
        $isNotEmpty = function($value) {
            return trim($value ?? '') !== '';
        };

        // 1. CONDITIONS BE (basées sur les informations du package tbl_released_package)
        // car les colonnes BE n'existent pas dans tbl_released_drawing
        if (!$packageData) {
            $activeStates[] = 'BE_0';
            return $activeStates;
        }

        // BE_0 condition : pas de Creation_VISA ET pas de VISA_BE_2
        if ($isEmpty($packageData['Creation_VISA']) && $isEmpty($packageData['VISA_BE_2'])) {
            $activeStates[] = 'BE_0';
            return $activeStates;
        }

        // BE_1 condition : Creation_VISA non vide ET VISA_BE_2 vide
        if ($isNotEmpty($packageData['Creation_VISA']) && $isEmpty($packageData['VISA_BE_2'])) {
            $activeStates[] = 'BE_1';
            return $activeStates;
        }

        // BE condition : VISA_BE_2 non vide ET VISA_BE_3 vide
        if ($isNotEmpty($packageData['VISA_BE_2']) && $isEmpty($packageData['VISA_BE_3'])) {
            $activeStates[] = 'BE';
            return $activeStates;
        }

        // Si VISA_BE_3 est non vide, on passe aux conditions post-BE
        if ($isEmpty($packageData['VISA_BE_3'])) {
            // Cas de sécurité : si on arrive ici, on retombe en BE_0
            $activeStates[] = 'BE_0';
            return $activeStates;
        }

        // 2. CONDITIONS PRINCIPALES (nécessitent VISA_BE <> '') - États post-BE
        $docType         = $legacyData['Doc_Type'] ?? '';
        $procType        = $legacyData['Proc_Type'] ?? '';
        $inventoryImpact = $legacyData['Inventory_Impact'] ?? '';
        $prodDraw        = $legacyData['Prod_Draw'] ?? '';
        $project         = $packageData['Project'] ?? '';
        $activity        = $packageData['Activity'] ?? '';

        // Product_Conditions
        if ($isEmpty($legacyData['VISA_Product'])) {
            $activeStates[] = 'Produit';
        }

        // Quality_Conditions
        if (in_array($docType, ['PUR', 'ASSY', 'DOC']) && $isEmpty($legacyData['VISA_Quality'])) {
            $activeStates[] = 'Quality';
        }

        // Inventory_Conditions (Qual_Logistique)
        if (
            $isEmpty($legacyData['VISA_Inventory']) &&
            $isEmpty($legacyData['VISA_GID']) &&
            $docType !== 'DOC' &&
            $inventoryImpact !== 'NO IMPACT'
        ) {
            $activeStates[] = 'Qual_Logistique';
        }

        // Project_Conditions
        if (
            $isEmpty($legacyData['VISA_Project']) &&
            $project !== 'STAND' &&
            (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT'))
        ) {
            $activeStates[] = 'Project';
        }

        // METRO_Conditions
        if (
            $isEmpty($legacyData['VISA_Metro']) &&
            (
                ($isNotEmpty($legacyData['VISA_Prod']) && $procType === 'E') ||
                ($isNotEmpty($legacyData['VISA_Quality']) && $docType === 'PUR')
            )
        ) {
            $activeStates[] = 'Metro';
        }

        // Q_PROD_Conditions (QProd)
        if (
            $isNotEmpty($legacyData['VISA_Metro']) &&
            in_array($docType, ['MACH', 'MOLD']) &&
            $isEmpty($legacyData['VISA_Q_PROD'])
        ) {
            $activeStates[] = 'QProd';
        }

        // Prod_ASSY_Conditions (Assembly)
        if (
            $isNotEmpty($legacyData['VISA_Product']) &&
            (
                ($docType === 'ASSY' && !str_starts_with($procType, 'F')) ||
                $docType === 'DOC'
            ) &&
            $isEmpty($legacyData['VISA_Prod'])
        ) {
            $activeStates[] = 'Assembly';
        }

        // Method_Conditions (Methode_assemblage)
        if (
            $isEmpty($legacyData['VISA_Method']) &&
            in_array($docType, ['ASSY', 'DOC']) &&
            !str_starts_with($activity, 'MOB_IND') &&
            $isNotEmpty($legacyData['VISA_Prod'])
        ) {
            $activeStates[] = 'Methode_assemblage';
        }

        // Prod_MACH_Conditions (Machining)
        if (
            $isNotEmpty($legacyData['VISA_Product']) &&
            $docType === 'MACH' &&
            $isEmpty($legacyData['VISA_Prod'])
        ) {
            $activeStates[] = 'Machining';
        }

        // Prod_MOLD_Conditions (Molding)
        if (
            $isNotEmpty($legacyData['VISA_Product']) &&
            $docType === 'MOLD' &&
            $isEmpty($legacyData['VISA_Prod'])
        ) {
            $activeStates[] = 'Molding';
        }

        // PUR_1_RFQ_Conditions (Achat_Rfq)
        if (
            $isEmpty($legacyData['VISA_PUR_1']) &&
            $docType === 'PUR' &&
            in_array($procType, ['', 'F', 'F30']) &&
            $isNotEmpty($legacyData['VISA_Quality']) &&
            $isNotEmpty($legacyData['VISA_Product'])
        ) {
            $activeStates[] = 'Achat_Rfq';
        }

        // PUR_2_PRISDANS_Conditions (Achat_F30)
        if (
            $isEmpty($legacyData['VISA_PUR_2']) &&
            $isNotEmpty($legacyData['VISA_PUR_1']) &&
            $procType === 'F30'
        ) {
            $activeStates[] = 'Achat_F30';
        }

        // PUR_3_Conditions (Achat_FIA)
        if (
            $isNotEmpty($legacyData['VISA_PUR_1']) &&
            $isEmpty($legacyData['VISA_PUR_3']) &&
            in_array($procType, ['F', 'F30']) &&
            $isNotEmpty($legacyData['VISA_GID_2'])
        ) {
            $activeStates[] = 'Achat_FIA';
        }

        // PUR_4_Conditions (Achat_RoHs_REACH)
        if (
            $isEmpty($legacyData['VISA_PUR_4']) &&
            $isNotEmpty($legacyData['VISA_PUR_1'])
        ) {
            $activeStates[] = 'Achat_RoHs_REACH';
        }

        // PUR_5_Conditions (Achat_Hts)
        if (
            $isEmpty($legacyData['VISA_PUR_5']) &&
            $isNotEmpty($legacyData['VISA_PUR_3'])
        ) {
            $activeStates[] = 'Achat_Hts';
        }

        // Supply_Conditions (Planning)
        if (
            in_array($docType, ['ASSY', 'MACH', 'MOLD', 'DOC']) &&
            $isNotEmpty($legacyData['VISA_Prod']) &&
            $isEmpty($legacyData['VISA_Supply'])
        ) {
            $activeStates[] = 'Planning';
        }

        // MOF_Conditions (Indus)
        if (
            (
                ($docType === 'ASSY' && $isNotEmpty($legacyData['VISA_Metro'])) ||
                $docType === 'DOC'
            ) &&
            $isNotEmpty($legacyData['VISA_Prod']) &&
            $isEmpty($legacyData['VISA_MOF'])
        ) {
            $activeStates[] = 'Indus';
        }

        // GID_1_Conditions (Core_Data) - Version simplifiée des conditions complexes
        if ($isEmpty($legacyData['VISA_GID'])) {
            $shouldAddCoreData = false;

            // Conditions pour PUR
            if ($docType === 'PUR') {
                if (
                    ($isNotEmpty($legacyData['VISA_PUR_1']) && $procType === 'F') ||
                    ($isNotEmpty($legacyData['VISA_PUR_2']) && $procType === 'F30')
                ) {
                    $shouldAddCoreData = true;
                }
            }

            // Conditions pour MACH/MOLD
            if (
                in_array($docType, ['MACH', 'MOLD']) &&
                $isNotEmpty($legacyData['VISA_Metro']) &&
                $isNotEmpty($legacyData['VISA_Supply'])
            ) {
                $shouldAddCoreData = true;
            }

            // Conditions pour ASSY
            if (
                $docType === 'ASSY' &&
                $isNotEmpty($legacyData['VISA_Quality']) &&
                $isNotEmpty($legacyData['VISA_Metro']) &&
                $isNotEmpty($legacyData['VISA_Supply'])
            ) {
                $shouldAddCoreData = true;
            }

            // Conditions pour DOC
            if (
                $docType === 'DOC' &&
                $isNotEmpty($legacyData['VISA_Quality']) &&
                $isNotEmpty($legacyData['VISA_Metro'])
            ) {
                $shouldAddCoreData = true;
            }

            // Vérifier les conditions d'inventaire
            if ($shouldAddCoreData) {
                $inventoryOk = false;
                if (
                    $isNotEmpty($legacyData['VISA_Inventory']) &&
                    in_array($inventoryImpact, ['TO BE SCRAPPED', 'TO BE UPDATED'])
                ) {
                    $inventoryOk = true;
                } elseif (
                    $isEmpty($legacyData['VISA_Inventory']) &&
                    ($inventoryImpact === 'NO IMPACT' || $docType === 'DOC')
                ) {
                    $inventoryOk = true;
                }

                if ($inventoryOk) {
                    $activeStates[] = 'Core_Data';
                }
            }
        }

        // GID_2_Conditions (Prod_Data)
        if (
            $isNotEmpty($legacyData['VISA_GID']) &&
            $isEmpty($legacyData['VISA_GID_2'])
        ) {
            $activeStates[] = 'Prod_Data';
        }

        // LABO_Conditions (Laboratory/methode_Labo)
        if (
            $docType === 'ASSY' &&
            $isNotEmpty($legacyData['VISA_MOF']) &&
            $isEmpty($legacyData['VISA_LABO'])
        ) {
            $activeStates[] = 'Laboratory';
        }

        // ROUTING_ENTRY_Conditions (GID)
        if (
            $isEmpty($legacyData['VISA_ROUTING_ENTRY']) &&
            $isNotEmpty($legacyData['VISA_GID_2'])
        ) {
            $shouldAddGID = false;

            if ($docType === 'ASSY' && $isNotEmpty($legacyData['VISA_MOF'])) {
                $shouldAddGID = true;
            } elseif (
                in_array($docType, ['MOLD', 'MACH']) &&
                $isNotEmpty($legacyData['VISA_Prod'])
            ) {
                $shouldAddGID = true;
            }

            if ($shouldAddGID) {
                $activeStates[] = 'GID';
            }
        }

        // Finance_Conditions (Costing)
        if (
            $docType !== 'DOC' &&
            $isEmpty($legacyData['VISA_Finance'])
        ) {
            $shouldAddCosting = false;

            if ($docType === 'PUR' && $isNotEmpty($legacyData['VISA_PUR_3'])) {
                $shouldAddCosting = true;
            } elseif (
                $docType !== 'PUR' &&
                $isNotEmpty($legacyData['VISA_ROUTING_ENTRY'])
            ) {
                $shouldAddCosting = true;
            }

            if ($shouldAddCosting) {
                $activeStates[] = 'Costing';
            }
        }

        // Si aucun état trouvé et VISA_BE_3 validé, considérer comme terminé
        if (empty($activeStates)) {
            // Document terminé, pas d'état actif
            return [];
        }

        return $activeStates;
    }

    /**
     * Afficher le résumé des états
     */
    private function displayStateSummary(OutputInterface $output): void
    {
        if (empty($this->stateCounts)) {
            return;
        }

        $output->writeln('<info>=== RÉSUMÉ DE LA DISTRIBUTION DES ÉTATS ===</info>');

        // Trier par nombre de documents (décroissant)
        arsort($this->stateCounts);

        foreach ($this->stateCounts as $state => $count) {
            $output->writeln("<info>$state: $count documents</info>");
        }

        $total = array_sum($this->stateCounts);
        $output->writeln("<info>TOTAL: $total placements d'états</info>");
    }

    /**
     * Parser un champ array depuis les données legacy
     */
    private function parseArrayField(?string $value, string $fieldName = ''): ?array
    {
        if (empty($value)) {
            return null;
        }

        // Si c'est déjà un array, le retourner
        if (is_array($value)) {
            return $value;
        }

        // Si c'est une chaîne, essayer de la parser
        if (is_string($value)) {
            // Essayer de décoder du JSON
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                // Pour q_doc_rec, enlever le préfixe "DOC_" s'il existe
                if ($fieldName === 'q_doc_rec') {
                    $decoded = array_map(function($item) {
                        return str_starts_with($item, 'DOC_') ? substr($item, 4) : $item;
                    }, $decoded);
                }
                return $decoded;
            }

            // Essayer de parser les valeurs séparées par ";" (format legacy)
            if (strpos($value, ';') !== false) {
                $items = array_filter(explode(';', $value), function($item) {
                    return !empty(trim($item));
                });

                // Pour q_doc_rec, enlever le préfixe "DOC_" s'il existe
                if ($fieldName === 'q_doc_rec') {
                    $items = array_map(function($item) {
                        $item = trim($item);
                        return str_starts_with($item, 'DOC_') ? substr($item, 4) : $item;
                    }, $items);
                }

                return $items;
            }

            // Sinon, traiter comme une valeur unique
            $singleValue = [$value];

            // Pour q_doc_rec, enlever le préfixe "DOC_" s'il existe
            if ($fieldName === 'q_doc_rec') {
                $singleValue = array_map(function($item) {
                    return str_starts_with($item, 'DOC_') ? substr($item, 4) : $item;
                }, $singleValue);
            }

            return $singleValue;
        }

        return null;
    }

    /**
     * Créer les state_timestamps basés sur les visas et états
     */
    private function createStateTimestamps(array $legacyData, array $documentBEVisas, array $currentStates): array
    {
        $stateTimestamps = [];
        $allVisaEvents = [];

        // Helper pour vérifier si un champ n'est pas vide
        $isNotEmpty = function($value) {
            return trim($value ?? '') !== '';
        };

        // 1. Collecter tous les événements de visa avec leurs dates
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        $packageData = $packageNum ? ($this->packageDataCache[$packageNum] ?? null) : null;

        // Événements BE depuis le package
        if ($packageData) {
            if ($isNotEmpty($packageData['Creation_VISA'])) {
                $date = $packageData['Creation_Date'] ?? null;
                if ($date && $this->isValidDate($date)) {
                    $allVisaEvents[] = [
                        'date' => $this->formatDateForSql($date),
                        'state' => 'BE_0',
                        'type' => 'enter'
                    ];
                }
            }

            if ($isNotEmpty($packageData['VISA_BE_2'])) {
                $date = $packageData['DATE_BE_2'] ?? null;
                if ($date && $this->isValidDate($date)) {
                    $allVisaEvents[] = [
                        'date' => $this->formatDateForSql($date),
                        'state' => 'BE_0',
                        'type' => 'exit'
                    ];
                    $allVisaEvents[] = [
                        'date' => $this->formatDateForSql($date),
                        'state' => 'BE_1',
                        'type' => 'enter'
                    ];
                }
            }

            if ($isNotEmpty($packageData['VISA_BE_3'])) {
                $date = $packageData['DATE_BE_3'] ?? null;
                if ($date && $this->isValidDate($date)) {
                    $allVisaEvents[] = [
                        'date' => $this->formatDateForSql($date),
                        'state' => 'BE_1',
                        'type' => 'exit'
                    ];
                    $allVisaEvents[] = [
                        'date' => $this->formatDateForSql($date),
                        'state' => 'BE',
                        'type' => 'enter'
                    ];
                }
            }
        }

        // Événements des autres visas depuis le document
        $visaStateMapping = [
            'VISA_Product' => 'Produit',
            'VISA_Quality' => 'Quality',
            'VISA_Inventory' => 'Qual_Logistique',
            'VISA_Project' => 'Project',
            'VISA_Metro' => 'Metro',
            'VISA_Prod' => $this->getProductionStateFromDocType($legacyData['Doc_Type'] ?? ''),
            'VISA_Method' => 'Methode_assemblage',
            'VISA_Supply' => 'Planning',
            'VISA_PUR_1' => 'Achat_Rfq',
            'VISA_PUR_2' => 'Achat_F30',
            'VISA_PUR_3' => 'Achat_FIA',
            'VISA_PUR_4' => 'Achat_RoHs_REACH',
            'VISA_PUR_5' => 'Achat_Hts',
            'VISA_GID' => 'Core_Data',
            'VISA_GID_2' => 'Prod_Data',
            'VISA_ROUTING_ENTRY' => 'GID',
            'VISA_Finance' => 'Costing',
            'VISA_Q_PROD' => 'QProd',
            'VISA_LABO' => 'Laboratory',
            'VISA_MOF' => 'Indus'
        ];

        foreach ($visaStateMapping as $visaField => $stateName) {
            if ($isNotEmpty($legacyData[$visaField])) {
                $dateField = str_replace('VISA_', 'DATE_', $visaField);
                $date = $legacyData[$dateField] ?? null;

                if ($date && $this->isValidDate($date)) {
                    $allVisaEvents[] = [
                        'date' => $this->formatDateForSql($date),
                        'state' => $stateName,
                        'type' => 'enter'
                    ];
                }
            }
        }

        // 2. Trier tous les événements par date
        usort($allVisaEvents, function($a, $b) {
            return strcmp($a['date'], $b['date']);
        });

        // 3. Construire les state_timestamps avec les transitions
        foreach ($allVisaEvents as $event) {
            $state = $event['state'];
            $date = $event['date'];
            $type = $event['type'];

            if (!isset($stateTimestamps[$state])) {
                $stateTimestamps[$state] = [];
            }

            if ($type === 'enter') {
                $stateTimestamps[$state][] = [
                    'enter' => $date,
                    'exit' => null,
                    'from_state' => null
                ];
            } elseif ($type === 'exit' && !empty($stateTimestamps[$state])) {
                // Fermer la dernière entrée de cet état
                $lastIndex = count($stateTimestamps[$state]) - 1;
                if ($lastIndex >= 0 && $stateTimestamps[$state][$lastIndex]['exit'] === null) {
                    $stateTimestamps[$state][$lastIndex]['exit'] = $date;
                }
            }
        }

        // 4. Pour les états actuels, s'assurer qu'ils ont une entrée et pas d'exit
        foreach ($currentStates as $state) {
            if (isset($stateTimestamps[$state]) && !empty($stateTimestamps[$state])) {
                // L'état existe déjà dans les timestamps, s'assurer qu'il n'a pas d'exit
                $lastIndex = count($stateTimestamps[$state]) - 1;
                if ($lastIndex >= 0) {
                    $stateTimestamps[$state][$lastIndex]['exit'] = null;
                }
            } else {
                // L'état n'existe pas dans les timestamps, mais il est actuel
                // Il faut créer une entrée avec une date d'entrée estimée
                $entryDate = $this->estimateStateEntryDate($state, $allVisaEvents, $legacyData);
                if ($entryDate) {
                    $stateTimestamps[$state] = [[
                        'enter' => $entryDate,
                        'exit' => null,
                        'from_state' => null
                    ]];
                }
            }
        }

        return $stateTimestamps;
    }

    /**
     * Estimer la date d'entrée dans un état basée sur la logique du workflow
     */
    private function estimateStateEntryDate(string $state, array $allVisaEvents, array $legacyData): ?string
    {
        // Logique d'estimation basée sur les conditions de workflow

        // 1. Pour les états qui dépendent de visas spécifiques, utiliser la date du visa prérequis
        $statePrerequisites = [
            // États d'achat
            'Achat_Rfq' => ['VISA_PUR_1'],
            'Achat_F30' => ['VISA_PUR_2'],
            'Achat_FIA' => ['VISA_PUR_3'],
            'Achat_RoHs_REACH' => ['VISA_PUR_4'],
            'Achat_Hts' => ['VISA_PUR_5'],

            // États de production
            'Assembly' => ['VISA_Prod'],
            'Machining' => ['VISA_Prod'],
            'Molding' => ['VISA_Prod'],

            // États de données
            'Core_Data' => ['VISA_GID'],
            'Prod_Data' => ['VISA_GID_2'],
            'GID' => ['VISA_ROUTING_ENTRY'],

            // États finaux
            'Costing' => ['VISA_Finance'],
            'QProd' => ['VISA_Q_PROD'],
            'Laboratory' => ['VISA_LABO'],
            'Indus' => ['VISA_MOF'],
        ];

        // 2. Pour les états avec des conditions complexes, estimer basé sur les visas disponibles
        $complexStateLogic = [
            'Core_Data' => function() use ($legacyData) {
                // Core_Data peut être activé par plusieurs conditions
                $dates = [];

                // Condition PUR
                if (!empty($legacyData['VISA_PUR_1']) && !empty($legacyData['DATE_PUR_1'])) {
                    $dates[] = $legacyData['DATE_PUR_1'];
                }
                if (!empty($legacyData['VISA_PUR_2']) && !empty($legacyData['DATE_PUR_2'])) {
                    $dates[] = $legacyData['DATE_PUR_2'];
                }

                // Condition MACH/MOLD
                if (in_array($legacyData['Doc_Type'] ?? '', ['MACH', 'MOLD'])) {
                    if (!empty($legacyData['VISA_Metro']) && !empty($legacyData['DATE_Metro']) &&
                        !empty($legacyData['VISA_Supply']) && !empty($legacyData['DATE_Supply'])) {
                        $dates[] = max($legacyData['DATE_Metro'], $legacyData['DATE_Supply']);
                    }
                }

                // Condition ASSY/DOC
                if (in_array($legacyData['Doc_Type'] ?? '', ['ASSY', 'DOC'])) {
                    if (!empty($legacyData['VISA_Quality']) && !empty($legacyData['DATE_Quality']) &&
                        !empty($legacyData['VISA_Metro']) && !empty($legacyData['DATE_Metro'])) {
                        $dates[] = max($legacyData['DATE_Quality'], $legacyData['DATE_Metro']);
                        if (!empty($legacyData['VISA_Supply']) && !empty($legacyData['DATE_Supply'])) {
                            $dates[] = max($dates[count($dates)-1], $legacyData['DATE_Supply']);
                        }
                    }
                }

                return !empty($dates) ? min($dates) : null;
            }
        ];

        // 3. Vérifier si l'état a des prérequis simples
        if (isset($statePrerequisites[$state])) {
            foreach ($statePrerequisites[$state] as $visaField) {
                $dateField = str_replace('VISA_', 'DATE_', $visaField);
                if (!empty($legacyData[$visaField]) && !empty($legacyData[$dateField])) {
                    if ($this->isValidDate($legacyData[$dateField])) {
                        return $this->formatDateForSql($legacyData[$dateField]);
                    }
                }
            }
        }

        // 4. Vérifier la logique complexe
        if (isset($complexStateLogic[$state])) {
            $estimatedDate = $complexStateLogic[$state]();
            if ($estimatedDate && $this->isValidDate($estimatedDate)) {
                return $this->formatDateForSql($estimatedDate);
            }
        }

        // 5. Fallback : utiliser la date du dernier événement connu
        if (!empty($allVisaEvents)) {
            $lastEvent = end($allVisaEvents);
            return $lastEvent['date'];
        }

        // 6. Fallback ultime : utiliser la date de création du package
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        if ($packageNum && isset($this->packageDataCache[$packageNum])) {
            $packageData = $this->packageDataCache[$packageNum];
            $creationDate = $packageData['Creation_Date'] ?? null;
            if ($creationDate && $this->isValidDate($creationDate)) {
                return $this->formatDateForSql($creationDate);
            }
        }

        return null;
    }

    /**
     * Obtenir l'état de production selon le type de document
     */
    private function getProductionStateFromDocType(string $docType): string
    {
        switch ($docType) {
            case 'ASSY':
                return 'Assembly';
            case 'MACH':
                return 'Machining';
            case 'MOLD':
                return 'Molding';
            default:
                return 'Assembly';
        }
    }

    /**
     * Créer l'historique des updates
     */
    private function createUpdatesHistory(array $legacyData): array
    {
        $updates = [];
        $allEvents = [];

        // Helper pour vérifier si un champ n'est pas vide
        $isNotEmpty = function($value) {
            return trim($value ?? '') !== '';
        };

        // 1. Ajouter l'événement de création du document
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        $packageData = $packageNum ? ($this->packageDataCache[$packageNum] ?? null) : null;

        if ($packageData && $isNotEmpty($packageData['Creation_VISA'])) {
            $creationDate = $packageData['Creation_Date'] ?? null;
            if ($creationDate && $this->isValidDate($creationDate)) {
                $creationUser = $this->findUserWithCache($packageData['Creation_VISA']);
                $allEvents[] = [
                    'date' => $this->formatDateForSql($creationDate),
                    'type' => 'create',
                    'details' => 'Création du document',
                    'user_id' => $creationUser ? $creationUser->getId() : null,
                    'user_name' => $creationUser ? ($creationUser->getPrenom() . ' ' . $creationUser->getNom()) : null
                ];
            }
        }

        // 2. Ajouter les événements de changement d'état et de visa
        $visaStateMapping = [
            'Creation_VISA' => ['state' => 'BE_0', 'visa' => 'visa_BE_0'],
            'VISA_BE_2' => ['state' => 'BE_1', 'visa' => 'visa_BE_1'],
            'VISA_BE_3' => ['state' => 'BE', 'visa' => 'visa_BE'],
            'VISA_Product' => ['state' => 'Produit', 'visa' => 'visa_Produit'],
            'VISA_Quality' => ['state' => 'Quality', 'visa' => 'visa_Quality'],
            'VISA_Inventory' => ['state' => 'Qual_Logistique', 'visa' => 'visa_Qual_Logistique'],
            'VISA_Project' => ['state' => 'Project', 'visa' => 'visa_Project'],
            'VISA_Metro' => ['state' => 'Metro', 'visa' => 'visa_Metro'],
            'VISA_Prod' => ['state' => $this->getProductionStateFromDocType($legacyData['Doc_Type'] ?? ''), 'visa' => 'visa_prod'],
            'VISA_Method' => ['state' => 'Methode_assemblage', 'visa' => 'visa_Method'],
            'VISA_Supply' => ['state' => 'Planning', 'visa' => 'visa_Planning'],
            'VISA_PUR_1' => ['state' => 'Achat_Rfq', 'visa' => 'visa_PUR_1'],
            'VISA_PUR_2' => ['state' => 'Achat_F30', 'visa' => 'visa_PUR_2'],
            'VISA_PUR_3' => ['state' => 'Achat_FIA', 'visa' => 'visa_PUR_3'],
            'VISA_PUR_4' => ['state' => 'Achat_RoHs_REACH', 'visa' => 'visa_PUR_4'],
            'VISA_PUR_5' => ['state' => 'Achat_Hts', 'visa' => 'visa_PUR_5'],
            'VISA_GID' => ['state' => 'Core_Data', 'visa' => 'visa_Core_Data'],
            'VISA_GID_2' => ['state' => 'Prod_Data', 'visa' => 'visa_Prod_Data'],
            'VISA_ROUTING_ENTRY' => ['state' => 'GID', 'visa' => 'visa_GID'],
            'VISA_Finance' => ['state' => 'Costing', 'visa' => 'visa_Costing'],
            'VISA_Q_PROD' => ['state' => 'QProd', 'visa' => 'visa_QProd'],
            'VISA_LABO' => ['state' => 'Laboratory', 'visa' => 'visa_LABO'],
            'VISA_MOF' => ['state' => 'Indus', 'visa' => 'visa_MOF']
        ];

        foreach ($visaStateMapping as $visaField => $config) {
            $dateField = str_replace('VISA_', 'DATE_', $visaField);
            if ($visaField === 'Creation_VISA') {
                $dateField = 'Creation_Date';
            }

            // Utiliser les données du package pour les visas BE, sinon les données du document
            $sourceData = in_array($visaField, ['Creation_VISA', 'VISA_BE_2', 'VISA_BE_3']) ? $packageData : $legacyData;

            if ($sourceData && $isNotEmpty($sourceData[$visaField])) {
                $date = $sourceData[$dateField] ?? null;
                if ($date && $this->isValidDate($date)) {
                    $user = $this->findUserWithCache($sourceData[$visaField]);
                    $formattedDate = $this->formatDateForSql($date);

                    // Ajouter l'événement de changement d'état (sauf pour Creation_VISA qui est déjà géré)
                    if ($visaField !== 'Creation_VISA') {
                        $allEvents[] = [
                            'date' => $formattedDate,
                            'type' => 'state_change',
                            'details' => "Entrée dans l'état " . $config['state'],
                            'user_id' => null,
                            'user_name' => null
                        ];
                    }

                    // Ajouter l'événement de visa
                    $allEvents[] = [
                        'date' => $formattedDate,
                        'type' => 'visa',
                        'details' => "Ajout du visa " . $config['visa'],
                        'user_id' => $user ? $user->getId() : null,
                        'user_name' => $user ? ($user->getPrenom() . ' ' . $user->getNom()) : null
                    ];
                }
            }
        }

        // 3. Trier tous les événements par date
        usort($allEvents, function($a, $b) {
            return strcmp($a['date'], $b['date']);
        });

        return $allEvents;
    }

    /**
     * Migrer les commentaires depuis l'ancienne base
     */
    private function migrateCommentsOptimized(OutputInterface $output): void
    {
        $output->writeln('<info>Migration des commentaires...</info>');

        // Récupérer tous les documents avec des commentaires depuis l'ancienne base
        $sql = 'SELECT Reference, Requestor_Comments, General_Comments FROM tbl_released_drawing
                WHERE (Requestor_Comments IS NOT NULL AND TRIM(Requestor_Comments) != "")
                   OR (General_Comments IS NOT NULL AND TRIM(General_Comments) != "")';
        $legacyComments = $this->oldDb->fetchAllAssociative($sql);

        if (empty($legacyComments)) {
            $output->writeln('<info>Aucun commentaire à migrer.</info>');
            return;
        }

        $output->writeln('<info>Traitement de ' . count($legacyComments) . ' commentaires...</info>');

        // Récupérer les documents existants pour faire le mapping
        $documentMapping = $this->buildDocumentMapping($output);

        $commentsToCreate = [];
        $commentsFound = 0;

        foreach ($legacyComments as $legacyComment) {
            $reference = $legacyComment['Reference'];
            $requestorComment = $this->decodeHtmlEntities(trim($legacyComment['Requestor_Comments'] ?? ''));
            $generalComment = $this->decodeHtmlEntities(trim($legacyComment['General_Comments'] ?? ''));

            // Trouver le document correspondant
            if (!isset($documentMapping[$reference])) {
                continue;
            }

            $documentId = $documentMapping[$reference];

            // 1. Traiter Requestor_Comments (commentaire simple)
            if (!empty($requestorComment)) {
                // Récupérer la date de création du document
                $creationDate = $this->getDocumentCreationDate($reference);

                $commentsToCreate[] = [
                    'document_id' => $documentId,
                    'comment' => $requestorComment,
                    'user_id' => $this->adminUser ? $this->adminUser->getId() : null,
                    'type' => 'request',
                    'state' => 'be',
                    'created_at' => $creationDate
                ];
                $commentsFound++;
            }

            // 2. Traiter General_Comments (format complexe à parser)
            if (!empty($generalComment)) {
                // Parser les commentaires multiples
                $parsedComments = $this->parseMultipleComments($generalComment);

                // Debug: afficher le parsing pour les premiers commentaires
                if ($commentsFound < 5) {
                    $output->writeln('<comment>Parsing General_Comments pour ' . $reference . ':</comment>');
                    $output->writeln('<comment>Original: ' . substr($generalComment, 0, 100) . '...</comment>');
                    foreach ($parsedComments as $i => $pc) {
                        $output->writeln('<comment>  ' . ($i+1) . '. Type: ' . $pc['type'] . ' (général), État: ' . $pc['state'] . ', Date: ' . $pc['date'] . '</comment>');
                        $output->writeln('<comment>     Texte: ' . substr($pc['text'], 0, 80) . '...</comment>');
                    }
                }

                foreach ($parsedComments as $parsedComment) {
                    $commentsToCreate[] = [
                        'document_id' => $documentId,
                        'comment' => $parsedComment['text'],
                        'user_id' => $this->adminUser ? $this->adminUser->getId() : null,
                        'type' => $parsedComment['type'],
                        'state' => $parsedComment['state'],
                        'created_at' => $parsedComment['date']
                    ];
                    $commentsFound++;
                }
            }
        }

        if (empty($commentsToCreate)) {
            $output->writeln('<info>Aucun commentaire valide à créer.</info>');
            return;
        }

        // Insertion en batch
        $this->insertCommentsBatch($commentsToCreate, $output);

        $output->writeln('<info>' . $commentsFound . ' commentaires migrés avec succès.</info>');
    }

    /**
     * Construire le mapping des documents (référence -> ID)
     */
    private function buildDocumentMapping(OutputInterface $output): array
    {
        $output->writeln('<info>Construction du mapping des documents...</info>');

        $sql = 'SELECT id, reference FROM document';
        $documents = $this->em->getConnection()->fetchAllAssociative($sql);

        $mapping = [];
        foreach ($documents as $doc) {
            $mapping[$doc['reference']] = $doc['id'];
        }

        $output->writeln('<info>Mapping construit pour ' . count($mapping) . ' documents.</info>');
        return $mapping;
    }

    /**
     * Insérer les commentaires en batch
     */
    private function insertCommentsBatch(array $comments, OutputInterface $output): void
    {
        $batchSize = 500;
        $batches = array_chunk($comments, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $comment) {
                $values[] = '(?, ?, ?, ?, ?, ?)';
                $params = array_merge($params, [
                    $comment['comment'],
                    $comment['user_id'],
                    $comment['created_at'],
                    $comment['document_id'],
                    $comment['type'],
                    $comment['state']
                ]);
            }

            $sql = 'INSERT INTO commentaire (commentaire, user_id, created_at, documents_id, type, state)
                    VALUES ' . implode(', ', $values);

            $this->em->getConnection()->executeStatement($sql, $params);

            $output->writeln('<comment>Batch commentaires ' . ($batchIndex + 1) .
                '/' . count($batches) . ' inséré (' . count($batch) . ' commentaires)</comment>');
        }
    }

    /**
     * Parser les commentaires multiples depuis l'ancien format
     * Format: "Finance: ccr ok le 14/12/2022 GID : Groupe Article à revoir ? Prod: Moule à commander"
     */
    private function parseMultipleComments(string $rawComment): array
    {
        $parsedComments = [];

        // Décoder les entités HTML au début
        $rawComment = $this->decodeHtmlEntities($rawComment);

        // Mapping des départements vers les états (workflow states)
        $departmentToStateMapping = [
            'Finance' => 'finance',
            'GID' => 'gid',
            'GID_2' => 'gid',
            'Prod' => 'production',
            'Product' => 'product',
            'Quality' => 'quality',
            'Project' => 'project',
            'Inventory' => 'inventory',
            'Method' => 'method',
            'Supply' => 'supply',
            'Metro' => 'metro',
            'PUR' => 'purchasing',
            'Purchasing' => 'purchasing',
            'Achat' => 'purchasing',
            'BE' => 'be',
            'Logistique' => 'logistics',
            'Planning' => 'planning',
            'Costing' => 'costing'
        ];

        // Diviser le commentaire par les départements
        // Pattern pour capturer "Département: texte"
        $pattern = '/\b(' . implode('|', array_keys($departmentToStateMapping)) . ')\s*:\s*([^:]*?)(?=\s+\b(?:' . implode('|', array_keys($departmentToStateMapping)) . ')\s*:|$)/i';

        if (preg_match_all($pattern, $rawComment, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $department = trim($match[1]);
                $commentText = trim($match[2]);

                if (empty($commentText)) {
                    continue;
                }

                // Extraire la date si présente dans le commentaire
                $date = $this->extractDateFromComment($commentText);

                // Nettoyer le texte du commentaire (enlever la date si elle était dans le texte)
                $cleanText = $this->cleanCommentText($commentText);

                // Trouver l'état correspondant au département (insensible à la casse)
                $state = 'general'; // État par défaut
                foreach ($departmentToStateMapping as $dept => $mappedState) {
                    if (strcasecmp($department, $dept) === 0) {
                        $state = $mappedState;
                        break;
                    }
                }

                $parsedComments[] = [
                    'text' => $cleanText,
                    'type' => 'general', // Type fixe pour General_Comments
                    'state' => $state,   // État basé sur le département
                    'date' => $date
                ];
            }
        } else {
            // Si aucun pattern trouvé, traiter comme un commentaire général
            $date = $this->extractDateFromComment($rawComment);
            $cleanText = $this->cleanCommentText($rawComment);

            $parsedComments[] = [
                'text' => $cleanText,
                'type' => 'general',
                'state' => 'general', // État par défaut
                'date' => $date
            ];
        }

        return $parsedComments;
    }

    /**
     * Extraire la date d'un commentaire
     */
    private function extractDateFromComment(string $comment): string
    {
        // Pattern pour les dates au format dd/mm/yyyy
        if (preg_match('/\b(\d{1,2}\/\d{1,2}\/\d{4})\b/', $comment, $matches)) {
            try {
                $date = \DateTime::createFromFormat('d/m/Y', $matches[1]);
                if ($date) {
                    return $date->format('Y-m-d H:i:s');
                }
            } catch (\Exception $e) {
                // Ignore les erreurs de parsing de date
            }
        }

        // Si aucune date trouvée, utiliser la date actuelle
        return date('Y-m-d H:i:s');
    }

    /**
     * Déterminer l'état du commentaire basé sur son contenu
     */
    private function determineCommentState(string $comment): string
    {
        $comment = strtolower($comment);

        // Mots-clés pour déterminer l'état
        if (strpos($comment, 'ok') !== false || strpos($comment, 'validé') !== false || strpos($comment, 'approuvé') !== false) {
            return 'approved';
        }

        if (strpos($comment, 'ko') !== false || strpos($comment, 'rejeté') !== false || strpos($comment, 'refusé') !== false) {
            return 'rejected';
        }

        if (strpos($comment, 'revoir') !== false || strpos($comment, 'à corriger') !== false || strpos($comment, 'modifier') !== false) {
            return 'review';
        }

        if (strpos($comment, 'en cours') !== false || strpos($comment, 'traitement') !== false) {
            return 'in_progress';
        }

        // État par défaut
        return 'draft';
    }

    /**
     * Nettoyer le texte du commentaire
     */
    private function cleanCommentText(string $comment): string
    {
        // Enlever les dates du texte
        $comment = preg_replace('/\ble\s+\d{1,2}\/\d{1,2}\/\d{4}\b/', '', $comment);

        // Nettoyer les espaces multiples
        $comment = preg_replace('/\s+/', ' ', $comment);

        return trim($comment);
    }

    /**
     * Récupérer la date de création d'un document
     */
    private function getDocumentCreationDate(string $reference): string
    {
        try {
            // Essayer de récupérer la date depuis l'ancienne base
            $sql = 'SELECT Creation_Date, Date_Released FROM tbl_released_drawing WHERE Reference = ?';
            $result = $this->oldDb->fetchAssociative($sql, [$reference]);

            if ($result) {
                // Utiliser Creation_Date en priorité, sinon Date_Released
                $dateField = $result['Creation_Date'] ?? $result['Date_Released'];

                if ($dateField) {
                    // Convertir la date si nécessaire
                    if ($dateField instanceof \DateTime) {
                        return $dateField->format('Y-m-d H:i:s');
                    } elseif (is_string($dateField)) {
                        // Essayer de parser différents formats de date
                        $formats = ['Y-m-d H:i:s', 'Y-m-d', 'd/m/Y', 'd-m-Y'];
                        foreach ($formats as $format) {
                            $date = \DateTime::createFromFormat($format, $dateField);
                            if ($date) {
                                return $date->format('Y-m-d H:i:s');
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // En cas d'erreur, utiliser la date actuelle
        }

        // Fallback: date actuelle
        return date('Y-m-d H:i:s');
    }

    /**
     * Établir les relations Package ↔ DMO
     */
    private function establishPackageDMORelations(array $packageMapping, OutputInterface $output): void
    {
        $output->writeln('<info>Établissement des relations Package ↔ DMO...</info>');

        // Récupérer tous les DMO avec leur identifiant DMO (champ texte)
        $sql = 'SELECT id, dmo FROM dmo WHERE dmo IS NOT NULL AND dmo != ""';
        $dmos = $this->em->getConnection()->fetchAllAssociative($sql);

        if (empty($dmos)) {
            $output->writeln('<info>Aucun DMO avec identifiant DMO trouvé.</info>');
            return;
        }

        $output->writeln('<info>Traitement de ' . count($dmos) . ' DMO...</info>');

        $relationsToCreate = [];
        $foundRelations = 0;
        $notFoundDmos = [];

        // Chercher les relations via le champ DMO (texte) dans tbl_released_package
        foreach ($dmos as $dmo) {
            $dmoIdentifier = $dmo['dmo']; // Le champ DMO (texte) de la nouvelle table
            $newDmoId = $dmo['id'];

            $output->writeln('<comment>Recherche pour DMO: ' . $dmoIdentifier . ' (ID: ' . $newDmoId . ')</comment>');

            try {
                // Chercher dans tbl_released_package avec le champ DMO (texte)
                $linkedPackagesSql = 'SELECT Rel_Pack_Num FROM tbl_released_package WHERE DMO = ?';
                $linkedPackages = $this->oldDb->fetchAllAssociative($linkedPackagesSql, [$dmoIdentifier]);

                $output->writeln('<comment>Packages trouvés pour DMO ' . $dmoIdentifier . ': ' . count($linkedPackages) . '</comment>');

                if (!empty($linkedPackages)) {
                    foreach ($linkedPackages as $package) {
                        $relPackNum = $package['Rel_Pack_Num'];
                        $output->writeln('<comment>  - Package: ' . $relPackNum . '</comment>');

                        if (isset($packageMapping[$relPackNum])) {
                            $relationsToCreate[] = [
                                'released_package_id' => $packageMapping[$relPackNum],
                                'dmo_id' => $newDmoId
                            ];
                            $foundRelations++;
                            $output->writeln('<comment>    ✓ Relation créée</comment>');
                        } else {
                            $output->writeln('<comment>    ✗ Package ' . $relPackNum . ' non trouvé dans le mapping</comment>');
                        }
                    }
                } else {
                    $notFoundDmos[] = $dmoIdentifier;
                    $output->writeln('<comment>  ✗ Aucun package trouvé</comment>');
                }
            } catch (\Exception $e) {
                $output->writeln('<error>Erreur lors de la recherche pour DMO ' . $dmoIdentifier . ': ' . $e->getMessage() . '</error>');
            }
        }

        $output->writeln('<info>Relations trouvées: ' . $foundRelations . '</info>');
        if (!empty($notFoundDmos)) {
            $output->writeln('<comment>' . count($notFoundDmos) . ' DMO sans package associé: ' . implode(', ', array_slice($notFoundDmos, 0, 10)) . (count($notFoundDmos) > 10 ? '...' : '') . '</comment>');
        }

        if (empty($relationsToCreate)) {
            $output->writeln('<info>Aucune relation Package ↔ DMO à créer. Vérifiez la structure de l\'ancienne base.</info>');
            return;
        }

        // Insertion des relations en batch
        $this->insertPackageDMORelationsBatch($relationsToCreate, $output);

        $output->writeln('<info>' . count($relationsToCreate) . ' relations Package ↔ DMO créées.</info>');
    }

    /**
     * Insérer les relations Package ↔ DMO en batch
     */
    private function insertPackageDMORelationsBatch(array $relations, OutputInterface $output): void
    {
        $batchSize = 500;
        $batches = array_chunk($relations, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $relation) {
                $values[] = '(?, ?)';
                $params = array_merge($params, [
                    $relation['released_package_id'],
                    $relation['dmo_id']
                ]);
            }

            $sql = 'INSERT IGNORE INTO released_package_dmo (released_package_id, dmo_id)
                    VALUES ' . implode(', ', $values);

            $this->em->getConnection()->executeStatement($sql, $params);

            $output->writeln('<comment>Batch relations ' . ($batchIndex + 1) .
                '/' . count($batches) . ' inséré (' . count($batch) . ' relations)</comment>');
        }
    }

    /**
     * Créer les relations Document ↔ Material dans la table document_materials
     */
    private function createDocumentMaterialRelations(OutputInterface $output): void
    {
        $output->writeln('<info>Création des relations Document ↔ Material...</info>');

        // Récupérer tous les documents avec leurs matériaux depuis la base legacy
        $sql = 'SELECT Reference, FXXX FROM tbl_released_drawing WHERE FXXX IS NOT NULL AND TRIM(FXXX) != ""';
        $legacyDocuments = $this->oldDb->fetchAllAssociative($sql);

        $relations = [];
        $notFoundDocuments = 0;
        $notFoundMaterials = 0;

        foreach ($legacyDocuments as $legacyDoc) {
            $reference = $legacyDoc['Reference'];
            $materialRef = trim($legacyDoc['FXXX']);

            // Trouver l'ID du document dans la nouvelle base
            $documentSql = 'SELECT id FROM document WHERE reference = ?';
            $documentResult = $this->em->getConnection()->fetchAssociative($documentSql, [$reference]);

            if (!$documentResult) {
                $notFoundDocuments++;
                continue;
            }

            $documentId = $documentResult['id'];

            // Trouver l'ID du matériau
            $materialId = $this->findMaterialIdOptimized($materialRef);

            if (!$materialId) {
                $notFoundMaterials++;
                continue;
            }

            $relations[] = [
                'document_id' => $documentId,
                'material_id' => $materialId
            ];
        }

        $output->writeln('<info>Relations à créer: ' . count($relations) . '</info>');
        $output->writeln('<comment>Documents non trouvés: ' . $notFoundDocuments . '</comment>');
        $output->writeln('<comment>Matériaux non trouvés: ' . $notFoundMaterials . '</comment>');

        if (empty($relations)) {
            $output->writeln('<comment>Aucune relation à créer.</comment>');
            return;
        }

        // Insérer les relations par batch
        $this->insertDocumentMaterialRelationsBatch($relations, $output);

        $output->writeln('<info>Relations Document ↔ Material créées avec succès.</info>');
    }

    /**
     * Insérer les relations Document ↔ Material en batch
     */
    private function insertDocumentMaterialRelationsBatch(array $relations, OutputInterface $output): void
    {
        $batchSize = 500;
        $batches = array_chunk($relations, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $relation) {
                $values[] = '(?, ?)';
                $params = array_merge($params, [
                    $relation['document_id'],
                    $relation['material_id']
                ]);
            }

            $sql = 'INSERT IGNORE INTO document_materials (document_id, material_id)
                    VALUES ' . implode(', ', $values);

            $this->em->getConnection()->executeStatement($sql, $params);

            $output->writeln('<comment>Batch relations ' . ($batchIndex + 1) .
                '/' . count($batches) . ' inséré (' . count($batch) . ' relations)</comment>');
        }
    }
}
