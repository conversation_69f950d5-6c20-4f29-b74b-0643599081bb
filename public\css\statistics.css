/* Animations supplémentaires pour les statistiques */

/* Animation de pulsation pour les métriques importantes */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.metric-card.highlight {
    animation: pulse 2s ease-in-out;
}

/* Animation de chargement pour les graphiques */
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.chart-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 8px;
    height: 100%;
}

/* Effet de survol amélioré pour les cartes */
.stats-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Animation pour les tableaux */
.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.08) !important;
    transform: scale(1.01);
}

/* Indicateurs de statut animés */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    animation: blink 2s infinite;
}

.status-indicator.success {
    background-color: var(--success-color);
}

.status-indicator.error {
    background-color: var(--error-color);
}

.status-indicator.warning {
    background-color: var(--warning-color);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Amélioration des badges */
.badge {
    transition: all 0.2s ease;
}

.badge:hover {
    transform: scale(1.1);
}

/* Animation de progression pour les barres */
@keyframes progressBar {
    from { width: 0%; }
    to { width: var(--progress-width); }
}

.progress-bar-animated {
    animation: progressBar 1.5s ease-out;
}

/* Effet de focus amélioré pour les boutons */
.btn:focus {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.25);
    outline: none;
}

/* Animation de rotation pour les icônes de chargement */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.fa-spin {
    animation: rotate 1s linear infinite;
}

/* Styles pour les tooltips personnalisés */
.custom-tooltip {
    position: relative;
    cursor: help;
}

.custom-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--dark-color);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.custom-tooltip::before {
    content: '';
    position: absolute;
    bottom: 115%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--dark-color);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.custom-tooltip:hover::after,
.custom-tooltip:hover::before {
    opacity: 1;
    visibility: visible;
}

/* Amélioration des scrollbars pour tous les navigateurs */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

/* Animation d'apparition en cascade pour les éléments */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s ease forwards;
}

.fade-in-up:nth-child(1) { animation-delay: 0.1s; }
.fade-in-up:nth-child(2) { animation-delay: 0.2s; }
.fade-in-up:nth-child(3) { animation-delay: 0.3s; }
.fade-in-up:nth-child(4) { animation-delay: 0.4s; }
.fade-in-up:nth-child(5) { animation-delay: 0.5s; }

/* Styles pour les graphiques en mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
    .chart-container {
        background: #2d3748;
    }
}

/* Responsive amélioré */
@media (max-width: 576px) {
    .stats-header h1 {
        font-size: 1.75rem;
    }
    
    .metric-value {
        font-size: 1.75rem;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.875rem;
    }
    
    .stats-card {
        padding: 16px;
    }
    
    .chart-container {
        height: 250px;
    }
}

/* Animation de succès pour les actions */
@keyframes successPulse {
    0% { 
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
        transform: scale(1);
    }
    70% { 
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
        transform: scale(1.05);
    }
    100% { 
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
        transform: scale(1);
    }
}

.success-animation {
    animation: successPulse 0.6s ease-out;
}

/* Styles pour les états de chargement */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Amélioration des transitions globales */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Styles pour les notifications toast (si nécessaire plus tard) */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    padding: 16px;
    margin-bottom: 12px;
    border-left: 4px solid var(--primary-color);
    transform: translateX(400px);
    animation: slideInRight 0.3s ease forwards;
}

@keyframes slideInRight {
    to { transform: translateX(0); }
}

.toast.success { border-left-color: var(--success-color); }
.toast.error { border-left-color: var(--error-color); }
.toast.warning { border-left-color: var(--warning-color); }
