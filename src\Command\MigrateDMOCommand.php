<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use App\Entity\DMO;
use App\Entity\User;
use App\Entity\Project;
use App\Entity\ProductRange;
use App\Entity\Commentaire;
use App\Repository\UserRepository;
use App\Repository\ProjectRepository;
use App\Repository\ProductRangeRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\DBAL\Attribute\Connection as ConnectionName;

#[AsCommand(
    name: 'app:migrate-dmo',
    description: 'Migre les données DMO de l\'ancienne BD vers le nouveau schéma'
)]
class MigrateDMOCommand extends Command
{
    protected static $defaultName = 'app:migrate-dmo';

    // Caches pour optimiser les performances
    private array $userCache = [];
    private array $projectIdCache = [];
    private array $productRangeCache = [];
    private ?User $adminUser = null;

    public function __construct(
        #[ConnectionName('legacy_dmo')]
        private Connection $oldDb,
        private EntityManagerInterface $em,
        private UserRepository $users,
        private ProjectRepository $projects,
        private ProductRangeRepository $productRanges
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('limit', 'l', InputOption::VALUE_OPTIONAL, 'Limite le nombre de DMO à migrer')
            ->addOption('skip-cleanup', null, InputOption::VALUE_NONE, 'Ignore le nettoyage des tables existantes');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $limit = $input->getOption('limit');
        $skipCleanup = $input->getOption('skip-cleanup');

        // Optimisations mémoire
        $this->optimizeForMigration($output);

        // 0. Nettoyer les tables existantes
        if (!$skipCleanup) {
            $this->cleanExistingData($output);
        }

        // 1. Initialiser tous les caches
        $this->initializeAllCaches($output);

        // 2. Mettre à jour les ProductRange existants avec etat = true
        $this->updateExistingProductRanges($output);

        // 3. Créer les ProductRange manquants
        $this->createMissingProductRanges($output);

        // 4. Créer les projets manquants
        $this->createMissingProjects($output);

        // 5. Migrer les DMO
        $this->migrateDMOs($limit, $output);

        $output->writeln('<info>Migration DMO terminée avec succès !</info>');
        return Command::SUCCESS;
    }

    /**
     * Optimisations mémoire pour la migration
     */
    private function optimizeForMigration(OutputInterface $output): void
    {
        $output->writeln('<info>Application des optimisations mémoire...</info>');

        // Désactiver le logging SQL
        $this->em->getConnection()->getConfiguration()->setSQLLogger(null);

        // Augmenter la limite mémoire
        ini_set('memory_limit', '2G');

        // Optimiser la configuration Doctrine
        $this->em->getConfiguration()->setAutoGenerateProxyClasses(false);
    }

    /**
     * Nettoyer les données existantes
     */
    private function cleanExistingData(OutputInterface $output): void
    {
        $output->writeln('<info>Nettoyage des tables DMO existantes...</info>');

        // Supprimer les commentaires liés aux DMO d'abord (contrainte FK)
        $this->em->getConnection()->executeStatement('DELETE FROM commentaire WHERE dmo_id_id IS NOT NULL');

        // Supprimer les DMO
        $this->em->getConnection()->executeStatement('DELETE FROM dmo');

        $output->writeln('<info>Tables nettoyées.</info>');
    }

    /**
     * Initialiser tous les caches
     */
    private function initializeAllCaches(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation de tous les caches...</info>');

        // 1. Cache des utilisateurs
        $this->initializeUserCache($output);

        // 2. Cache des projets
        $this->initializeProjectCache($output);

        // 3. Cache des ProductRange
        $this->initializeProductRangeCache($output);

        $output->writeln('<info>Tous les caches initialisés.</info>');
    }

    /**
     * Initialiser le cache des utilisateurs
     */
    private function initializeUserCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des utilisateurs...</info>');

        // Charger l'utilisateur Admin
        $this->adminUser = $this->users->findOneBy(['nom' => 'Admin']);
        if ($this->adminUser) {
            $output->writeln('<info>Utilisateur Admin trouvé pour fallback.</info>');
        } else {
            $output->writeln('<error>Aucun utilisateur Admin trouvé !</error>');
        }

        $users = $this->users->findAll();
        foreach ($users as $user) {
            $key = $this->normalizeName($user->getNom() . ' ' . $user->getPrenom());
            $this->userCache[$key] = $user;
        }

        $output->writeln('<info>Cache utilisateurs initialisé avec ' . count($users) . ' utilisateurs.</info>');
    }

    /**
     * Initialiser le cache des projets
     */
    private function initializeProjectCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des projets...</info>');

        $sql = 'SELECT id, otp FROM project';
        $projects = $this->em->getConnection()->fetchAllAssociative($sql);

        foreach ($projects as $project) {
            $this->projectIdCache[$project['otp']] = $project['id'];
        }

        $output->writeln('<info>Cache projets initialisé avec ' . count($projects) . ' projets.</info>');
    }

    /**
     * Initialiser le cache des ProductRange
     */
    private function initializeProductRangeCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des ProductRange...</info>');

        $productRanges = $this->productRanges->findAll();
        foreach ($productRanges as $pr) {
            $key = $this->normalizeProductRange($pr->getProductRange(), $pr->getDivision());
            $this->productRangeCache[$key] = $pr;
        }

        $output->writeln('<info>Cache ProductRange initialisé avec ' . count($productRanges) . ' éléments.</info>');
    }

    /**
     * Mettre à jour les ProductRange existants avec etat = true
     */
    private function updateExistingProductRanges(OutputInterface $output): void
    {
        $output->writeln('<info>Mise à jour des ProductRange existants avec etat = true...</info>');

        $sql = 'UPDATE product_range SET etat = 1 WHERE etat IS NULL';
        $updated = $this->em->getConnection()->executeStatement($sql);

        $output->writeln("<info>$updated ProductRange mis à jour.</info>");
    }

    /**
     * Créer les ProductRange manquants
     */
    private function createMissingProductRanges(OutputInterface $output): void
    {
        $output->writeln('<info>Création des ProductRange manquants...</info>');

        // Récupérer tous les Product_Range et Division uniques de l'ancienne base
        $sql = 'SELECT DISTINCT Product_Range, Division FROM tbl_dmo WHERE Product_Range IS NOT NULL AND Division IS NOT NULL';
        $legacyRanges = $this->oldDb->fetchAllAssociative($sql);

        $toCreate = [];
        foreach ($legacyRanges as $range) {
            $key = $this->normalizeProductRange($range['Product_Range'], $range['Division']);
            if (!isset($this->productRangeCache[$key])) {
                $toCreate[] = [
                    'product_range' => $range['Product_Range'],
                    'division' => $range['Division'],
                    'etat' => false
                ];
            }
        }

        if (empty($toCreate)) {
            $output->writeln('<info>Aucun ProductRange à créer.</info>');
            return;
        }

        // Insertion en batch
        $batchSize = 100;
        $batches = array_chunk($toCreate, $batchSize);

        foreach ($batches as $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $pr) {
                $values[] = '(?, ?, ?)';
                $params = array_merge($params, [
                    $pr['product_range'],
                    $pr['division'],
                    $pr['etat'] ? 1 : 0
                ]);
            }

            $sql = 'INSERT INTO product_range (product_range, division, etat) VALUES ' . implode(', ', $values);
            $this->em->getConnection()->executeStatement($sql, $params);
        }

        // Recharger le cache
        $this->initializeProductRangeCache($output);

        $output->writeln('<info>' . count($toCreate) . ' ProductRange créés avec etat = false.</info>');
    }

    /**
     * Normaliser le nom d'utilisateur
     */
    private function normalizeName(?string $name): string
    {
        if (empty($name)) {
            return '';
        }
        return mb_strtoupper(trim(preg_replace('/\s+/', ' ', $name)), 'UTF-8');
    }

    /**
     * Normaliser la clé ProductRange
     */
    private function normalizeProductRange(?string $productRange, ?string $division): string
    {
        return mb_strtoupper(trim($productRange) . '|' . trim($division), 'UTF-8');
    }

    /**
     * Créer les projets manquants
     */
    private function createMissingProjects(OutputInterface $output): void
    {
        $output->writeln('<info>Création des projets manquants...</info>');

        // Récupérer tous les codes de projets uniques de l'ancienne base DMO
        $projectCodes = $this->oldDb->fetchAllAssociative(
            'SELECT DISTINCT REPLACE(Project, "P", "P-") Project FROM tbl_dmo WHERE Project IS NOT NULL AND Project != ""'
        );

        $toCreate = [];
        foreach ($projectCodes as $row) {
            $otpCode = $row['Project'];
            if (!isset($this->projectIdCache[$otpCode])) {
                $toCreate[] = $otpCode;
            }
        }

        if (empty($toCreate)) {
            $output->writeln('<info>Aucun projet à créer.</info>');
            return;
        }

        // Insertion en batch
        $batchSize = 100;
        $batches = array_chunk($toCreate, $batchSize);

        foreach ($batches as $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $otpCode) {
                $values[] = '(?, ?)';
                $params = array_merge($params, [$otpCode, 'Projet migré depuis DMO legacy']);
            }

            $sql = 'INSERT INTO project (otp, title) VALUES ' . implode(', ', $values);
            $this->em->getConnection()->executeStatement($sql, $params);
        }

        // Recharger le cache des projets
        $this->initializeProjectCache($output);

        $output->writeln('<info>' . count($toCreate) . ' projets créés.</info>');
    }

    /**
     * Migrer les DMO
     */
    private function migrateDMOs(?int $limit, OutputInterface $output): void
    {
        $output->writeln('<info>Migration des DMO...</info>');

        // Récupérer les DMO legacy
        if ($limit) {
            $sql = 'SELECT * FROM tbl_dmo ORDER BY ID LIMIT ' . (int)$limit;
        } else {
            $sql = 'SELECT * FROM tbl_dmo ORDER BY ID';
        }
        $dmos = $this->oldDb->fetchAllAssociative($sql);

        $validDMOs = [];
        $skipped = 0;

        $output->writeln('<info>Traitement de ' . count($dmos) . ' DMO...</info>');

        foreach ($dmos as $dmo) {
            // Validation des données obligatoires
            if (empty($dmo['Issue_Date']) || empty($dmo['Decision']) || empty($dmo['Type'])) {
                $skipped++;
                continue;
            }

            // Recherche des relations
            $requestorId = $this->findUserIdOptimized($dmo['Requestor_Name']);
            $engOwnerId = $this->findUserIdOptimized($dmo['Eng_Owner']);
            $lastModificatorId = $this->findUserIdOptimized($dmo['Last_Update_TE_ID']);
            $projectId = $this->findProjectId(str_replace('P', 'P-', $dmo['Project'] ?? ''));
            $productRangeId = $this->findProductRangeId($dmo['Product_Range'], $dmo['Division']);

            // ProductRange est obligatoire - créer un ProductRange par défaut si manquant
            if (!$productRangeId) {
                $productRangeId = $this->createDefaultProductRange($dmo['Product_Range'], $dmo['Division'], $output);
                if (!$productRangeId) {
                    $output->writeln('<error>Impossible de créer ProductRange pour: ' . $dmo['Product_Range'] . '|' . $dmo['Division'] . '</error>');
                    $skipped++;
                    continue;
                }
            }

            // Générer le nouvel identifiant DMO au format DMO + AAMMDD + caractère
            

            $validDMOs[] = [
                'legacy_id'            => $dmo['ID'],
                'dmo'                  => $this->decodeHtmlEntities($dmo['DMO']),
                'date_init'            => $dmo['Issue_Date'],
                'description'          => $this->decodeHtmlEntities($dmo['Description']),
                'requestor_id'         => $requestorId,
                'decision'             => $this->decodeHtmlEntities($dmo['Decision']),
                'status'               => $dmo['Status'] === 'Open' ? 1 : 0,
                'ex'                   => $this->decodeHtmlEntities($dmo['Ex']),
                'indus_related'        => $this->parseIndusRelated($dmo['Indus_Related']),
                'eng_owner_id'         => $engOwnerId,
                'date_end'             => $this->parseDate($dmo['End_Date']),
                'pr_number'            => $this->parseInteger($dmo['PR_Number']),
                'last_modificator_id'  => $lastModificatorId,
                'last_update_date'     => $this->parseDate($dmo['Last_Update_Date']) ?: $dmo['Issue_Date'],
                'ex_assessment'        => $this->decodeHtmlEntities($dmo['EX_Assessment']),
                'spent_time'           => (int)$dmo['Spent_Time'],
                'type'                 => $this->decodeHtmlEntities($dmo['Type']),
                'document'             => $this->decodeHtmlEntities($dmo['Document']),
                'product_range_id'     => $productRangeId,
                'project_relation_id'  => $projectId,
                'comment'              => $this->decodeHtmlEntities($dmo['Comment']), // Pour créer les commentaires après
            ];
        }

        if (empty($validDMOs)) {
            $output->writeln('<error>Aucun DMO valide à migrer !</error>');
            return;
        }

        // Insertion en batch avec SQL brut
        $this->insertDMOsBatch($validDMOs, $output);

        // Créer les commentaires
        $this->createDMOComments($validDMOs, $output);

        $output->writeln('<info>' . count($validDMOs) . ' DMO migrés avec succès.</info>');
        if ($skipped > 0) {
            $output->writeln('<comment>' . $skipped . ' DMO ignorés (données manquantes).</comment>');
        }
    }

    /**
     * Trouver l'ID d'un utilisateur de manière optimisée
     */
    private function findUserIdOptimized(?string $raw): ?int
    {
        $user = $this->findUserFlexible($raw);
        return $user ? $user->getId() : ($this->adminUser ? $this->adminUser->getId() : null);
    }

    /**
     * Recherche flexible d'utilisateur (adaptée de MigrateLegacyDataOptimizedCommand)
     */
    private function findUserFlexible(?string $raw): ?User
    {
        if (empty($raw)) {
            return null;
        }

        // 1) Normalisation
        $clean = $this->normalizeName($raw);
        $parts = preg_split('/\s+/', $clean);
        $lastName = $parts[0] ?? '';
        $firstPart = $parts[1] ?? '';

        // 2) Exact match
        $allUsers = $this->users->findAll();
        $matches = [];
        foreach ($allUsers as $u) {
            if ($this->normalizeName($u->getNom()) === $lastName) {
                $matches[] = $u;
            }
        }

        if (count($matches) === 1) {
            return $matches[0];
        }

        // 3) Initiales prénom
        if ($firstPart && count($matches) > 1) {
            foreach ($matches as $u) {
                $prenomNorm = $this->normalizeName($u->getPrenom() ?? '');
                $initials = '';
                foreach (preg_split('/\s+/', $prenomNorm) as $p) {
                    $initials .= mb_substr($p, 0, 1, 'UTF-8');
                }
                if ($initials === $firstPart) {
                    return $u;
                }
            }
        }

        // 4) Inclusion partielle
        foreach ($allUsers as $u) {
            if (mb_stripos($this->normalizeName($u->getNom()), $lastName, 0, 'UTF-8') !== false) {
                return $u;
            }
        }

        // 5) Fallback fuzzy (Levenshtein)
        $closest = null;
        $minDist = PHP_INT_MAX;
        foreach ($allUsers as $u) {
            $dist = levenshtein($lastName, $this->normalizeName($u->getNom()));
            if ($dist < $minDist && $dist <= 3) { // Seuil de tolérance
                $minDist = $dist;
                $closest = $u;
            }
        }

        return $closest;
    }

    private function decodeHtmlEntities(?string $text): ?string
    {
        if ($text === null || $text === '') {
            return $text;
        }

        // Décoder les entités HTML
        $decoded = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Nettoyer les guillemets multiples qui peuvent rester
        $decoded = preg_replace('/"{2,}/', '"', $decoded);

        return $decoded;
    }

    /**
     * Trouver l'ID d'un projet
     */
    private function findProjectId(?string $otpCode): ?int
    {
        if (empty($otpCode)) {
            return null;
        }
        return $this->projectIdCache[$otpCode] ?? null;
    }

    /**
     * Trouver l'ID d'un ProductRange
     */
    private function findProductRangeId(?string $productRange, ?string $division): ?int
    {
        if (empty($productRange) || empty($division)) {
            return null;
        }

        $key = $this->normalizeProductRange($productRange, $division);
        $pr = $this->productRangeCache[$key] ?? null;
        return $pr ? $pr->getId() : null;
    }

    /**
     * Parser Indus_Related
     */
    private function parseIndusRelated(?string $value): ?int
    {
        if (empty($value) || trim($value) === '') {
            return null;
        }
        return $value === 'Yes' ? 1 : ($value === 'No' ? 0 : null);
    }

    /**
     * Parser un entier depuis une chaîne en gérant les valeurs trop grandes
     */
    private function parseInteger(?string $value): ?int
    {
        if (empty($value) || !is_numeric($value)) {
            return null;
        }

        $intValue = (int)$value;

        // Vérifier si la valeur est dans la plage des entiers MySQL (INT)
        // MySQL INT range: -2147483648 to 2147483647
        if ($intValue > 2147483647 || $intValue < -2147483648) {
            return null; // Retourner null pour les valeurs trop grandes
        }

        return $intValue;
    }

    /**
     * Insérer les DMO en batch avec SQL brut
     */
    private function insertDMOsBatch(array $validDMOs, OutputInterface $output): void
    {
        $output->writeln('<info>Insertion des DMO en batch...</info>');

        $batchSize = 100;
        $batches = array_chunk($validDMOs, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $dmo) {
                $values[] = '(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';
                $params = array_merge($params, [
                    $dmo['dmo'],
                    $dmo['date_init'],
                    $dmo['description'],
                    $dmo['requestor_id'],
                    $dmo['decision'],
                    $dmo['status'],
                    $dmo['ex'],
                    $dmo['indus_related'],
                    $dmo['eng_owner_id'],
                    $dmo['date_end'],
                    $dmo['pr_number'],
                    $dmo['last_modificator_id'],
                    $dmo['last_update_date'],
                    $dmo['ex_assessment'],
                    $dmo['spent_time'],
                    $dmo['type'],
                    $dmo['document'],
                    $dmo['product_range_id'],
                    $dmo['project_relation_id'],
                    $dmo['legacy_id'] // Pour le mapping des commentaires
                ]);
            }

            $sql = 'INSERT INTO dmo
                    (dmo, date_init, description, requestor_id, decision, status, ex, indus_related,
                     eng_owner_id, date_end, pr_number, last_modificator_id, last_update_date,
                     ex_assessment, spent_time, type, document, product_range_id, project_relation_id, legacy_id)
                    VALUES ' . implode(', ', $values);

            $this->em->getConnection()->executeStatement($sql, $params);

            $output->writeln('<info>Batch ' . ($batchIndex + 1) . '/' . count($batches) . ' inséré.</info>');
        }
    }

    /**
     * Créer les commentaires pour les DMO
     */
    private function createDMOComments(array $validDMOs, OutputInterface $output): void
    {
        $output->writeln('<info>Création des commentaires DMO...</info>');

        // Récupérer les IDs générés des DMO
        $dmoMapping = $this->buildDMOMapping($validDMOs, $output);

        $commentsToCreate = [];
        foreach ($validDMOs as $dmo) {
            if (!empty($dmo['comment']) && isset($dmoMapping[$dmo['legacy_id']])) {
                $commentsToCreate[] = [
                    'dmo_id' => $dmoMapping[$dmo['legacy_id']],
                    'comment' => $dmo['comment'],
                    'user_id' => $dmo['last_modificator_id'] ?: ($this->adminUser ? $this->adminUser->getId() : null),
                    'date' => $dmo['last_update_date']
                ];
            }
        }

        if (empty($commentsToCreate)) {
            $output->writeln('<info>Aucun commentaire à créer.</info>');
            return;
        }

        // Insertion en batch
        $batchSize = 100;
        $batches = array_chunk($commentsToCreate, $batchSize);

        foreach ($batches as $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $comment) {
                $values[] = '(?, ?, ?, ?, ?, ?)';
                $params = array_merge($params, [
                    $comment['comment'],
                    $comment['user_id'],
                    $comment['date'],
                    $comment['dmo_id'],
                    'migration', // type
                    'migrated'   // state
                ]);
            }

            $sql = 'INSERT INTO commentaire (commentaire, user_id, created_at, dmo_id_id, type, state) VALUES ' . implode(', ', $values);
            $this->em->getConnection()->executeStatement($sql, $params);
        }

        $output->writeln('<info>' . count($commentsToCreate) . ' commentaires créés.</info>');
    }

    /**
     * Construire le mapping des DMO legacy vers nouveaux IDs
     */
    private function buildDMOMapping(array $validDMOs, OutputInterface $output): array
    {
        $mapping = [];

        // Récupérer les DMO avec leurs legacy_id
        $sql = 'SELECT id, legacy_id FROM dmo WHERE legacy_id IS NOT NULL ORDER BY id';
        $results = $this->em->getConnection()->fetchAllAssociative($sql);

        foreach ($results as $result) {
            $mapping[$result['legacy_id']] = $result['id'];
        }

        $output->writeln('<info>Mapping DMO construit pour ' . count($mapping) . ' éléments.</info>');
        return $mapping;
    }

    /**
     * Génère un nouvel identifiant DMO au format DMO + AAMMDD + caractère
     */
    private function generateNewDmoIdentifier(string $issueDate, int $legacyId): string
    {
        $date = new \DateTime($issueDate);
        $dateFormat = $date->format('ymd'); // AAMMDD
        $dayPattern = 'DMO' . $dateFormat;

        // Pour la migration, nous utilisons l'ID legacy pour déterminer l'ordre
        // et générer des caractères séquentiels pour le même jour

        // Simuler l'ordre des DMO du même jour basé sur l'ID legacy
        // Ceci garantit que les DMO migrés auront des identifiants cohérents
        $charIndex = ($legacyId % 26); // Modulo 26 pour les lettres A-Z
        $nextChar = chr(ord('A') + $charIndex);

        return $dayPattern . $nextChar;
    }

    /**
     * Créer ou récupérer un ProductRange "Unknown" pour une division
     */
    private function createDefaultProductRange(?string $productRange, ?string $division, OutputInterface $output): ?int
    {
        $cleanDivision = trim($division ?: 'Unknown');
        if (empty($cleanDivision)) {
            $cleanDivision = 'Unknown';
        }

        // Chercher d'abord si un "Unknown" existe déjà pour cette division
        $unknownKey = $this->normalizeProductRange('Unknown', $cleanDivision);
        if (isset($this->productRangeCache[$unknownKey])) {
            return $this->productRangeCache[$unknownKey]->getId();
        }

        // Chercher en base de données
        $existingUnknown = $this->productRanges->findOneBy([
            'ProductRange' => 'Unknown',
            'Division' => $cleanDivision
        ]);

        if ($existingUnknown) {
            // Mettre à jour le cache
            $this->productRangeCache[$unknownKey] = $existingUnknown;
            return $existingUnknown->getId();
        }

        try {
            // Créer un seul "Unknown" par division avec etat = false (legacy/inactif)
            $sql = 'INSERT INTO product_range (product_range, division, etat) VALUES (?, ?, ?)';
            $this->em->getConnection()->executeStatement($sql, ['Unknown', $cleanDivision, 0]);

            $newId = $this->em->getConnection()->lastInsertId();

            // Mettre à jour le cache
            $productRangeEntity = $this->productRanges->find($newId);
            if ($productRangeEntity) {
                $this->productRangeCache[$unknownKey] = $productRangeEntity;
            }

            $output->writeln('<comment>ProductRange "Unknown" créé pour division: ' . $cleanDivision . '</comment>');
            return (int)$newId;

        } catch (\Exception $e) {
            $output->writeln('<error>Erreur lors de la création du ProductRange Unknown: ' . $e->getMessage() . '</error>');
            return null;
        }
    }

    /**
     * Parser une date en gérant les dates invalides
     */
    private function parseDate(?string $date): ?string
    {
        if (empty($date) || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
            return null;
        }

        // Vérifier si la date est valide
        try {
            $dateObj = new \DateTime($date);
            return $dateObj->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return null;
        }
    }
}
