/*!
 * Bazaarvoice bv-loader v10.2.1
 * Thu Oct 04 2018 17:14:15 GMT+0000 (UTC)
 * 
 * Capabilities:
 * 
 *   inline_ratings@2.1.1
 *   questions@0.2.2
 *   rating_summary@2.13.0
 *   ratings@0.1.23
 *   reviews@0.2.2
 *   swat-submission@2.0.2
 * 
 * http://bazaarvoice.com
 * 
 * Copyright 2018 Bazaarvoice. All rights reserved.
 * 
 */!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.r=function(e){Object.defineProperty(e,"__esModule",{value:!0})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=36)}([function(e,t,n){"use strict";e.exports=new Function("return this;")()},function(e,t,n){"use strict";t.a={deployment:{client:"rscomponents",site:"main_site",environment:"production",locale:"fr_FR"},dataEnvironment:"prod",serverEnvironment:"prod",vpc:"prod",piiDataRegion:"europe",displayCode:"12089",domains:[{firstPartyCookieEnabled:!0,thirdPartyCookieEnabled:!0,domain:".uk.rs-online.com"},{firstPartyCookieEnabled:!0,thirdPartyCookieEnabled:!0,domain:".fr.rs-online.com"},{firstPartyCookieEnabled:!0,thirdPartyCookieEnabled:!0,domain:".bazaarvoice.com"},{firstPartyCookieEnabled:!0,thirdPartyCookieEnabled:!0,domain:".rs-online.com"},{firstPartyCookieEnabled:!0,thirdPartyCookieEnabled:!0,domain:"translate.googleusercontent.com"},{firstPartyCookieEnabled:!0,thirdPartyCookieEnabled:!0,domain:"webcache.googleusercontent.com"},{firstPartyCookieEnabled:!0,thirdPartyCookieEnabled:!0,domain:"translate.baiducontent.com"},{firstPartyCookieEnabled:!0,thirdPartyCookieEnabled:!0,domain:".bazaarvoice.com"},{firstPartyCookieEnabled:!0,thirdPartyCookieEnabled:!0,domain:"localhost"}],scoutVersion:"10.2.1",splitTestingEnabled:!1,isConversations:!0,isPreview:!1,legacyScoutUrl:"https://display.ugc.bazaarvoice.com/static/rscomponents/main_site/fr_FR/bvapi.js",paths:{root:"https://apps.bazaarvoice.com",splitTests:"https://apps.bazaarvoice.com/splitTests.json",components:"https://apps.bazaarvoice.com/components/components-3.0.0.js",render:"https://apps.bazaarvoice.com/render/render-3.0.0.min.js",layoutRoot:"https://apps.bazaarvoice.com/deployments/rscomponents/main_site/production/fr_FR/layouts"},webAnalyticsIntegrationConfigs:[{name:"AdobeSiteCatalyst",evar:103,event:42,tracker:""}],apps:[{name:"inline_ratings",version:"2.1.1",defer:!0,hasConfig:!0,sourcePath:"https://apps.bazaarvoice.com/apps/inline_ratings/inline_ratings-2.1.1.js",legacyPath:null,publicName:"inline_rating",configUrl:"https://apps.bazaarvoice.com/deployments/rscomponents/main_site/production/fr_FR/inline_ratings-config.js"},{name:"questions",version:"0.2.2",defer:!0,hasConfig:!0,sourcePath:"https://apps.bazaarvoice.com/apps/questions/questions-0.2.2.js",legacyPath:null,publicName:"questions",configUrl:"https://apps.bazaarvoice.com/deployments/rscomponents/main_site/production/fr_FR/questions-config.js"},{name:"rating_summary",version:"2.13.0",defer:!0,hasConfig:!0,sourcePath:"https://apps.bazaarvoice.com/apps/rating_summary/rating_summary-2.13.0.js",legacyPath:null,publicName:"rating_summary",configUrl:"https://apps.bazaarvoice.com/deployments/rscomponents/main_site/production/fr_FR/rating_summary-config.js"},{name:"ratings",version:"0.1.23",defer:!0,hasConfig:!0,sourcePath:"https://apps.bazaarvoice.com/apps/ratings/ratings-0.1.23.js",legacyPath:null,publicName:"ratings",configUrl:"https://apps.bazaarvoice.com/deployments/rscomponents/main_site/production/fr_FR/ratings-config.js"},{name:"reviews",version:"0.2.2",defer:!0,hasConfig:!0,sourcePath:"https://apps.bazaarvoice.com/apps/reviews/reviews-0.2.2.js",legacyPath:null,publicName:"reviews",configUrl:"https://apps.bazaarvoice.com/deployments/rscomponents/main_site/production/fr_FR/reviews-config.js"},{name:"swat-submission",version:"2.0.2",defer:!0,hasConfig:!0,sourcePath:"https://apps.bazaarvoice.com/apps/swat-submission/swat-submission-2.0.2.js",legacyPath:null,publicName:"multi_submission",configUrl:"https://apps.bazaarvoice.com/deployments/rscomponents/main_site/production/fr_FR/swat-submission-config.js"}]}},function(e,t,n){"use strict";var r=n(15),o=n.n(r),i=n(0),a=n.n(i),c=n(1),s=console.warn.bind(console),u=["API_BASE_URL","Date","_bvapijs","_internal","_options","_staticAssetRegistry","configure","define","easyXDM","extensions","global","initialData","name","options","performance","pixel","registerProperty","requestAnimationFrame","require","requirejs","serverRender","staticAssetLoader","ud","ui"].concat(c.a.apps.map(function(e){return e.name}));if("Proxy"in a.a){var f=a.a.Proxy;a.a.BV=new f(a.a.BV||{},{set:function(e,t,n){return~u.indexOf(t)||s("Unexpected property '"+t+"' assigned to window.BV. Valid assignment to the BV variable requires approval from the BV Loader Team."),e[t]=n,!0}})}t.a=o.a.namespace("BV")},function(e,t,n){"use strict";e.exports.getEntries=n(14).getEntries,e.exports.getEntriesByName=n(35).getEntriesByName,e.exports.getEntriesByType=n(24).getEntriesByType,e.exports.now=n(12).now,e.exports.mark=n(33).mark,e.exports.measure=n(32).measure},function(e,t,n){"use strict";n.d(t,"d",function(){return a}),n.d(t,"a",function(){return c}),n.d(t,"b",function(){return s}),n.d(t,"c",function(){return u});var r=n(5),o=n.n(r),i=/[,\s]\s*/,a=function(){if(!o.a)return[];var e=o.a.getAttribute("data-bv-preload");return e?e.split(i):[]}(),c=o.a&&o.a.getAttribute("data-bv-callback")||"bvCallback",s=o.a&&o.a.getAttribute("data-bv-display-code"),u=o.a&&o.a.getAttribute("data-bv-hostname")},function(e,t,n){var r=n(0),o=r.document&&(r.document.currentScript||r.document.querySelector('script[src*="bv.js"]'));e.exports=o},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=n(0);if(void 0!==o.Promise&&o.Promise.toString().match(/\[native code]/))e.exports=o.Promise;else{var i=function(){},a=function(e){if("object"!==r(this))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],l(e,this)},c=function(e,t){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,a._immediateFn(function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var r;try{r=n(e._value)}catch(e){return void u(t.promise,e)}s(t.promise,r)}else(1===e._state?s:u)(t.promise,e._value)})):e._deferreds.push(t)},s=function(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"===(void 0===t?"undefined":r(t))||"function"==typeof t)){var n=t.then;if(t instanceof a)return e._state=3,e._value=t,void f(e);if("function"==typeof n)return void l((o=n,i=t,function(){o.apply(i,arguments)}),e)}e._state=1,e._value=t,f(e)}catch(t){u(e,t)}var o,i},u=function(e,t){e._state=2,e._value=t,f(e)},f=function(e){2===e._state&&0===e._deferreds.length&&a._immediateFn(function(){e._handled||a._unhandledRejectionFn(e._value)});for(var t=0,n=e._deferreds.length;t<n;t++)c(e,e._deferreds[t]);e._deferreds=null},l=function(e,t){var n=!1;try{e(function(e){n||(n=!0,s(t,e))},function(e){n||(n=!0,u(t,e))})}catch(e){if(n)return;n=!0,u(t,e)}},d=setTimeout;a.prototype.catch=function(e){return this.then(null,e)},a.prototype.then=function(e,t){var n=new this.constructor(i);return c(this,new function(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}(e,t,n)),n},a.all=function(e){var t=Array.prototype.slice.call(e);return new a(function(e,n){if(0===t.length)return e([]);var o=t.length;function i(a,c){try{if(c&&("object"===(void 0===c?"undefined":r(c))||"function"==typeof c)){var s=c.then;if("function"==typeof s)return void s.call(c,function(e){i(a,e)},n)}t[a]=c,0==--o&&e(t)}catch(e){n(e)}}for(var a=0;a<t.length;a++)i(a,t[a])})},a.resolve=function(e){return e&&"object"===(void 0===e?"undefined":r(e))&&e.constructor===a?e:new a(function(t){t(e)})},a.reject=function(e){return new a(function(t,n){n(e)})},a.race=function(e){return new a(function(t,n){for(var r=0,o=e.length;r<o;r++)e[r].then(t,n)})},a._immediateFn="function"==typeof setImmediate&&function(e){setImmediate(e)}||function(e){d(e,0)},a._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},e.exports=a}},function(e,t,n){"use strict";var r=n(0),o=n(15),i=r.document,a={};function c(){return i.getElementsByTagName("script")[0]}function s(e){return!e||"loaded"===e||"complete"===e||"uninitialized"===e}function u(e,t,n){if(!e||"string"!=typeof e)throw new Error("`url` must be a string");if(t.namespaceName&&"string"!=typeof t.namespaceName)throw new Error("`options.namespaceName` must be a string");if(t.forceLoad&&"boolean"!=typeof t.forceLoad)throw new Error("`options.forceLoad` must be a boolean");if("number"!=typeof t.timeout)throw new Error("`options.timeout` must be a number");if(n&&"function"!=typeof n)throw new Error("`callback` must be a function")}e.exports={_clearLoadedUrls:function(e){e?o.namespace(e).loadedUrls={}:a={}},loadScript:function(e,t,n){var r,f=a;if("function"==typeof t&&(n=t,t=null),(t=t||{}).timeout=t.timeout||1e4,u(e,t,n),t.namespaceName&&(r=o.namespace(t.namespaceName),f=r.loadedUrls||{}),t.forceLoad||!f[e]){var l,d=i.createElement("script"),m=!1;if(t.attributes)for(l in t.attributes)d.setAttribute(l,t.attributes[l]);d.onreadystatechange=d.onload=function(){!m&&s(d.readyState)&&h()},d.onerror=function(){m||h(new Error("Error: could not load "+e))};var p=setTimeout(function(){m||h(new Error("Error: script timeout "+e))},t.timeout);setTimeout(function(){d.src=e;var t=c();t.parentNode.insertBefore(d,t)},0)}else"function"==typeof n&&n();function h(o){m=!0,clearTimeout(p),d.onload=d.onreadystatechange=d.onerror=null,d.parentNode.removeChild(d),o||(f[e]=!0,t.namespaceName?r.loadedUrls=f:a=f),"function"==typeof n&&n(o)}},loadStyleSheet:function(e,t,n){var f,l=a;if("function"==typeof t&&(n=t,t=null),(t=t||{}).timeout=t.timeout||1e4,u(e,t,n),"injectionNode"in t&&!(t.injectionNode instanceof r.Element))throw new Error("`options.injectionNode` must be a DOM node");if(t.namespaceName&&(f=o.namespace(t.namespaceName),l=f.loadedUrls||{}),t.forceLoad||!l[e]){var d,m=i.createElement("link"),p=!1;if(t.attributes)for(d in t.attributes)m.setAttribute(d,t.attributes[d]);m.onreadystatechange=m.onload=function(){!p&&s(m.readyState)&&v()},m.onerror=function(){p||v(new Error("Error: could not load "+e))};var h=setTimeout(m.onerror,t.timeout);setTimeout(function(){m.media="only x",m.rel="stylesheet",m.type="text/css",m.href=e,setTimeout(function(){m.media="all"},0);var n=t.injectionNode||c().parentNode;try{n.appendChild(m)}catch(e){v(new Error("Error: could not append LINK element"))}},0)}else"function"==typeof n&&n();function v(r){p=!0,clearTimeout(h),m.onload=m.onreadystatechange=m.onerror=null,r||(l[e]=!0,t.namespaceName?f.loadedUrls=l:a=l),"function"==typeof n&&n(r)}}}},function(e,t,n){"use strict";var r=n(0);e.exports={create:function(e,t,n,o,i){var a=new Date;a.setTime(a.getTime()+24*n*60*60*1e3);var c=n?";expires="+a.toGMTString():"",s=encodeURIComponent(e)+"="+encodeURIComponent(t)+c+";path=/"+(o?";domain="+o:"")+(i?";secure":"");r.document.cookie=s},read:function(e){var t,n=encodeURIComponent(e)+"=",o=r.document.cookie.split(";");for(t=0;t<o.length;t++){for(var i=o[t];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(n))return decodeURIComponent(i.substring(n.length,i.length))}return null},remove:function(e){this.create(e,"",-1)}}},function(e,t,n){"use strict";var r=n(31);function o(e,t){if("."===e.charAt(0)&&t){var n=("."+t).lastIndexOf(e);return n>=0&&n===1+t.length-e.length}return t===e||t==="www."+e}e.exports=function(e,t){var n={},i={isValid:!1,get:function(e){return n[e]}},a=function(e,t){for(var n=r(e).host,i=0;i<t.length;i++)if(o(t[i].domain,n))return t[i]}(e,t);if(a){for(var c in i.isValid=!0,a)a.hasOwnProperty(c)&&(n[c]=a[c]);n.domain.match(/^(\.\d+){4}$/)&&(n.domain=n.domain.substr(1))}return i}},function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return o}),n.d(t,"c",function(){return i});var r="color:black; font-size:1.1em; font-weight:bold;",o="color: #0b6392; font-size:1.1em; text-decoration:none; font-weight:normal;",i="font-size:1.0em; font-weight:lighter;"},function(e,t,n){"use strict";var r,o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r=function(){return this}();try{r=r||Function("return this")()||(0,eval)("this")}catch(e){"object"===("undefined"==typeof window?"undefined":o(window))&&(r=window)}e.exports=r},function(e,t,n){"use strict";var r=n(0),o=n(34),i=r.performance,a=i&&i.now,c="function"==typeof a,s=o.now();function u(){return a.call(i)}function f(){return o.now()-s}i&&i.timing&&"number"==typeof i.timing.navigationStart&&(s=i.timing.navigationStart),e.exports={now:function(){return(c?u:f)()}}},function(e,t,n){"use strict";e.exports={timeline:[]}},function(e,t,n){"use strict";var r=n(0),o=n(13).timeline,i=r.performance,a=i&&i.getEntries,c="function"==typeof a;function s(){return a.call(i)}function u(){return o}e.exports={getEntries:function(){return(c?s:u)()}}},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=n(0);function i(e){this.name=e}i.prototype.registerProperty=function(e,t){if(this.hasOwnProperty(e))throw new Error("Cannot register "+e+" because a property with that name already exists on window."+this.name);return this[e]=t,this},e.exports={namespace:function(e){if(void 0===o[e])Object.defineProperty(o,e,{value:new i(e)});else{if("object"!==r(o[e]))throw new Error("Namespace "+e+" cannot be created. A non-object variable is already assigned to window."+e);if(!(o[e]instanceof i))for(var t in i.call(o[e],e),i.prototype)o[e][t]=i.prototype[t]}return o[e]}}},function(e,t,n){"use strict";(function(t){function n(e,t,n){if("function"!=typeof t)throw new Error("A function must be provided to process the queue");var r=e.length;function o(){var r=e.shift();n?t(r):setTimeout(function(){t(r)},0)}for(;e.length>0;)o();return r}function r(e){return this.config=e,this._readyQueue=[],this._renderQueue=[],this._configQueue=[],this._promise=new t(function(e,t){this._resolve=e,this._reject=t}.bind(this)),this}r.prototype.ready=function(e){return"function"==typeof e&&this._readyQueue.push(e),this._promise},r.prototype.render=function(e){return this._renderQueue.push(e)},r.prototype.configure=function(e){return this._configQueue.push(e)};r.prototype.processReady=function(e){var t=function(e,t){"function"==typeof t&&t(e,this)}.bind(this,e);n.call(this,this._readyQueue,t,!0),this._readyQueue.push=t,this.ready=function(e){return t(e),this._promise},e?this._reject(e):this._resolve(this)},r.prototype.processQueue=function(e){n.call(this,this._renderQueue,e),this.render=this._renderQueue.push=e},r.prototype.processConfig=function(e){n.call(this,this._configQueue,e,!0),this.configure=this._configQueue.push=e},e.exports=r}).call(this,n(6))},function(e,t,n){"use strict";n.d(t,"c",function(){return p}),n.d(t,"b",function(){return h}),n.d(t,"a",function(){return v});var r=n(0),o=n.n(r),i=n(1),a=n(2),c=n(23),s=n(3),u=n.n(s),f=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l=function(e){return e.map(function(e){return f({},e,{value:String(e.value)})})},d=function(e){return Object.keys(e).map(function(t){return{name:t,value:e[t]}}).filter(function(e){return void 0!==e.value})},m=function(e){return e&&e!==Math.floor(e)?Number(e.toFixed(2)):e},p=function(){var e=o.a.devicePixelRatio,t=o.a.innerHeight,n=o.a.innerWidth,r=o.a.navigator,i={devicePixelRatio:e,screenHeight:t,screenWidth:n};r.connection&&(i.effectiveType=r.connection.effectiveType,i.downlink=r.connection.downlink);var a=u.a.getEntriesByType("navigation").shift();return a&&["connectEnd","connectStart","domainLookupStart","domainLookupEnd","domComplete","domInteractive","domContentLoadedEventStart","fetchStart","loadEventEnd","requestStart","responseStart","responseEnd","secureConnectionStart"].forEach(function(e){i[e]=m(a[e])}),l(d(i))},h=function(){var e={},t=u.a.getEntriesByType("resource").filter(function(e){return!!e.name.match(c.a.bvLoaderRegex)}).shift();t&&["connectEnd","connectStart","domainLookupStart","domainLookupEnd","domComplete","domInteractive","domContentLoadedEventStart","fetchStart","loadEventEnd","requestStart","responseStart","responseEnd","secureConnectionStart"].forEach(function(n){e[n]=m(t[n])});var n,r,o=(n="requestedApp",r=i.a.apps.map(function(e){return e.name}),r.map(function(e){return{name:n,value:e}})).concat(d(e));return l(o)},v=function(e){var t={},n=a.a[e].perfMark,r=u.a.getEntriesByType("resource").filter(function(e){return!!e.name.match(c.a.appNameRegex)}).shift();return n.load.measures.length&&(t.loadTime=m(n.load.measures.shift().duration)),n.configure.measures.length&&(t.configurationTime=m(n.configuration.measures.shift().duration)),n.render.measures.length&&(t.avgRender=m(n.render.measures.reduce(function(e,t){return e+t.duration},0)/n.render.measures.length),t.render=n.render.measures.map(function(e){return m(e.duration)})),r&&(t.fetchStart=m(r.fetchStart),t.transferSize=r.transferSize),l(d(t))}},function(e,t,n){"use strict";var r=n(22),o=n(1),i=r.a.createTracker({commonData:{bvProduct:"bv-loader",bvProductVersion:o.a.scoutVersion},name:"bv-loader",source:"bv-loader"});t.a=i},function(e,t,n){"use strict";n.d(t,"b",function(){return o}),n.d(t,"a",function(){return i});var r=n(8),o=function(){return r.create("bv_metrics",!0,72e5/864e5),!0},i=!!r.read("bv_metrics")||!Math.floor(1e3*Math.random())&&o()},function(e,t,n){"use strict";var r=n(19),o=n(18);t.a=function(e,t,n,i){if(r.a&&t.length){Object(r.b)();var a={type:"performance",name:"Display",scope:e,metrics:t};n&&(a.bvProduct=n),i&&(a.bvProductVersion=i),o.a.trackEvent("Diagnostic",a)}}},function(e,t,n){"use strict";n.d(t,"b",function(){return a}),n.d(t,"c",function(){return c}),n.d(t,"a",function(){return s});var r=n(0),o=n.n(r),i=function(e){var t=o.a.document.querySelector('meta[name="bv:'+e+'"]');return t&&t.getAttribute("content")||void 0},a=function(){return i("pageType")},c=function(){return i("userToken")},s=function(){return(e=i("disabled"))&&e.split(",").map(function(e){return e.trim()})||[];var e}},function(e,t,n){"use strict";var r=n(43),o=n(27),i=n.n(o),a=n(0),c=n.n(a),s=n(1),u=n(9),f=n.n(u),l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},d=f()(c.a.location.host,s.a.domains);t.a={createTracker:function(e){var t=e.commonData,n=e.name,o=e.source,a=l({},t);a.locale=s.a.deployment.locale;var c={name:n,client:s.a.deployment.client,display:s.a.displayCode,environment:s.a.vpc,source:o,staging:"stg"===s.a.dataEnvironment||"qa"===s.a.vpc,anonymous:r.a.anonymous,brandDomain:d.get("firstPartyCookieEnabled")?d.get("domain"):"",commonData:a},u=new i.a(c);return"europe"===s.a.piiDataRegion&&u.setEU(!0),s.a.webAnalyticsIntegrationConfigs&&s.a.webAnalyticsIntegrationConfigs.length&&s.a.webAnalyticsIntegrationConfigs.forEach(function(e){u.configureSCIProvider(e.name,e)}),u}}},function(e,t,n){"use strict";var r=n(1),o=new RegExp("/deployments/([^/]+)/(?:[^/]+/){3}bv\\.js"),i="dev"===r.a.serverEnvironment?"development":"production.min",a={appNameRegex:function(e){return new RegExp("/"+r.a.paths.root+"/apps/"+e+"/"+e+"-[^/]+.js")},bvLoaderRegex:o,parseClient:function(e){var t=o.exec(e);if(t)return t[1]},react:"https://apps.bazaarvoice.com/vendor/react@16.3.2/react."+i+".js",reactDOM:"https://apps.bazaarvoice.com/vendor/react-dom@16.3.2/react-dom."+i+".js"};t.a=a},function(e,t,n){"use strict";var r=n(0),o=n(14).getEntries,i=r.performance,a=i&&i.getEntriesByType,c="function"==typeof a;function s(e){return a.call(i,e)}function u(e){if(0===arguments.length)throw new TypeError("Failed to execute 'getEntriesByType' on 'Performance': 1 argument required, but only 0 present.");return o().filter(function(t){return t.entryType===e})}e.exports={getEntriesByType:function(e){var t=arguments.length>0,n=c?s:u;return t?n(e):n()}}},function(e,t,n){"use strict";(function(t){var r,o=n(30),i={},a=/function\s+([^\(\s]+)/;e.exports=function(e){return new t(function(t){o(function(){var n=e.name;n||(n=a.exec(e.toString())[1]),r||((r=document.createElement("iframe")).src="about:blank",r.style.display="none",document.body.appendChild(r)),i[n]||(i[n]=r.contentWindow[n]),t(i[n])})})}}).call(this,n(6))},function(e,t,n){"use strict";var r=Array.prototype,o=r.indexOf,i=r.slice;function a(e,t){var n,r,o=this.events=this.events||{},i=e.split(/\s+/),a=i.length;for(n=0;n<a;n++)o[r=i[n]]=o[r]||[],o[r].push(t);return this}function c(e,t){return this.on(e,function n(){this.off(e,n),t.apply(this,i.call(arguments))}),this}function s(e,t){var n,r,o,i,a,c=this.events;if(c){for(r=0,i=(a=e.split(/\s+/)).length;r<i;r++)(n=a[r])in c!=!1&&-1!==(o=t?f(c[n],t):0)&&c[n].splice(o,1);return this}}function u(e){var t,n,r=this.events;if(r&&e in r!=!1){for(t=i.call(arguments,1),n=r[e].length-1;n>=0;n--)try{r[e][n].apply(this,t)}catch(e){}return this}}function f(e,t){var n,r;if(o&&e.indexOf===o)return e.indexOf(t);for(n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}e.exports=function(){return this.on=a,this.off=s,this.trigger=this.emit=u,this.one=this.once=c,this}},function(e,t,n){"use strict";var r,o,i,a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};o=[],void 0===(i="function"==typeof(r=function(){function e(e){if(!(e=e||{}).name||"string"!=typeof e.name)throw new TypeError("config.name is required and must be string");this.name=e.name,this.commonData=e.commonData||{},this._verify(),this.q.push(["create",e]);for(var t,n,r=["Staging","Audience","Anonymous"],o=0;o<r.length;o++)((n=e[(t=r[o]).toLowerCase()])||e.hasOwnProperty(t.toLowerCase()))&&this["set"+t](n);e.validationCallback&&this.enableValidation(e.validationCallback),!1===e.timing&&this.disableTiming(),e.brandDomain&&this.setBrandDomain(e.brandDomain),e.eventClassFilter&&this.setEventClassFilter(e.eventClassFilter)}return function(){function t(){for(var e=n(arguments),t=1;t<e.length;t++)if(null!==e[t]&&"object"===a(e[t]))for(var r in e[t])e[t].hasOwnProperty(r)&&(e[0][r]=e[t][r]);return e[0]}function n(e){return Array.prototype.slice.call(e)}var r,o,i,c,s,u,f=["setClient","setDisplay","setAnonymous","setBrandDomain","setSource","setEnvironment","setAudience","setStaging","setEventClassFilter","setEU"],l={};function d(e){return function(){this._verify();var t=n(arguments);t.unshift(e),t.push(this.name),this.q.push(t)}}for(var m,p=0;p<f.length;p++)l[m=f[p]]=d(m);t(l,{_verify:function(){window._bvaq=window._bvaq||[],this.q=window._bvaq},_flushBatch:(r=function(){this._verify(),this.q.push(["flushBatch",this.name])},u=o||250,function(){var e=i||this,t=+new Date,n=arguments;c&&t<c+u?(clearTimeout(s),s=setTimeout(function(){c=t,r.apply(e,n)},u)):(c=t,r.apply(e,n))}),loadScript:function(e){var t=e||"latest",n=document.createElement("script");n.type="text/javascript",n.async=!0;var r="http:";"https:"===window.location.protocol&&(r="https:"),n.src=r+"//analytics-static.ugc.bazaarvoice.com/prod/static/"+t+"/bv-analytics.js";var o=document.getElementsByTagName("script")[0];o.parentNode.insertBefore(n,o)},setCommonData:function(e){t(this.commonData,e)},configureSCIProvider:function(e,t){this._verify(),this.q.push(["configureSCIProvider",e,t,this.name])},enableValidation:function(e){this._verify(),this.q.push(["enableValidation",e,this.name])},disableValidation:function(){this._verify(),this.q.push(["disableValidation",this.name])},enableTiming:function(){this._verify(),this.q.push(["enableTiming",this.name])},disableTiming:function(){this._verify(),this.q.push(["disableTiming",this.name])},addMeta:function(e,t){this._verify(),this.q.push(["addMeta",e,t,this.name])},removeMeta:function(e){this._verify(),this.q.push(["removeMeta",e,this.name])},trackEvent:function(e,n){this._verify(),this.q.push(["addBatchItem",e,t({},this.commonData,n),this.name]),this._flushBatch()},trackPageView:function(e){this._verify(),this.q.push(["trackPageView",t({},this.commonData,e),this.name])},trackError:function(e){this._verify(),this.q.push(["trackEvent","Error",t({},this.commonData,e),this.name])},trackConversion:function(e){this._verify(),this.q.push(["trackConversion",t({},this.commonData,e),this.name])},trackTransaction:function(e){this._verify(),this.q.push(["trackTransaction",t({},this.commonData,e),this.name])},trackImpression:function(e){this._verify(),this.trackEvent("Impression",t({},this.commonData,e),this.name)},doNotTrack:function(e){this._verify(),this.q.push(["doNotTrack",e,this.name])}}),t(e.prototype,l),function(){var n,r=[],o=0,i=250;function a(e,n){if(!(e=t({minPixels:100,minTime:5e3},e)).containerId)throw new Error("No containerId specified.");return e.observerId=o++,e.callback=function(t){s(e.observerId),n(t)},r.push(e),c(),e.observerId}function c(){var e="number"==typeof n;r.length>=1&&!e?n=setInterval(l,i):0===r.length&&e&&(clearInterval(n),n=null)}function s(e){for(var t=0;t<r.length;t++)r[t].observerId===e&&(r.splice(t,1),c())}function u(){var e=window.document&&window.document.documentElement;return{height:window.innerHeight||e.clientHeight,width:window.innerWidth||e.clientWidth}}function f(e){var t,n=e.getBoundingClientRect(),r=u().height;return n.top<=0?(t=Math.max(n.top+n.height,0),t=Math.min(t,r)):n.top>=r?0:Math.min(r-n.top,n.height)}function l(){for(var e,t,n,o,i,a,c=0;c<r.length;c++)n=r[c],e=document.getElementById(n.containerId),t=+new Date,e?(o=void 0,i=e.getBoundingClientRect(),a=u().width,(i.left<=0?(o=Math.max(i.left+i.width,0),o=Math.min(o,a)):i.left>=a?0:Math.min(a-i.left,i.width))<=0?delete n.inViewAt:f(e)<n.minPixels?delete n.inViewAt:n.inViewAt?t-n.inViewAt>n.minTime&&n.callback():(n.inViewAt=t,0===n.minTime&&n.callback())):delete n.inViewAt}function d(e){this.id=e}d.prototype.remove=function(){s(this.id)},e.prototype.trackEventWhenVisible=function(e,n){var r=this;if(!e||"string"!=typeof e.cl)throw new TypeError("data.cl must exist and be a string");return new d(a(n,function(){var n=t({},r.commonData,e);r.trackEvent(n.cl,n)}))},e.prototype.trackInView=function(e,n){var r=this;return new d(a(n=t({},n,{minTime:0}),function(){var n=t({},r.commonData,e,{name:"InView",type:"Used",interaction:"0"});r.trackEvent("Feature",n)}))},e.prototype.trackViewedCGC=function(e,n){var r=this;return new d(a(n,function(){var n=t({},r.commonData,e,{name:"ViewedUGC",type:"Used",interaction:"1"});r.trackEvent("Feature",n)}))}}()}(),e})?r.apply(t,o):r)||(e.exports=i)},function(e,t,n){"use strict";var r=n(25);function o(e,t){document.body&&(t.disconnect(),e())}function i(e){var t=new MutationObserver(function(){o(e,t)});t.observe(document.documentElement,{childList:!0,subtree:!0}),o(e,t)}var a={},c={},s=n.n(r)()(Array),u=function(e){return s.then(function(t){var n=[];return t.prototype.filter.call(e,function(e){if(1===e.nodeType){e.dataset&&e.dataset.bvShow&&n.push(e);var r=e.querySelectorAll("[data-bv-show]");n=n.concat(t.prototype.slice.call(r))}}),n})},f=function(e){e.filter(function(e){return!e.dataset.bvReady}).forEach(function(e){var t=e.dataset.bvShow;a[t]&&a[t].forEach(function(t){t(e),e.dataset.bvReady=!0})})},l=function(e){e.filter(function(e){return e.dataset.bvReady}).forEach(function(e){var t=e.dataset.bvShow;c[t]&&c[t].forEach(function(t){t(e),delete e.dataset.bvReady})})},d=new MutationObserver(function(e){e.forEach(function(e){var t=e.addedNodes,n=e.removedNodes;t&&u(t).then(f),n&&u(n).then(l)})});i(function(){d.observe(document.body,{childList:!0,subtree:!0})});var m=function(e){return s.then(function(t){var n=document.querySelectorAll("[data-bv-show]");return t.prototype.filter.call(n,function(t){return!e||t.dataset.bvShow===e})})},p=function(e,t){a[e]||(a[e]=[]),a[e].push(t),m(e).then(f)},h=function(e,t){c[e]||(c[e]=[]),c[e].push(t)},v=n(1);n.d(t,"a",function(){return g}),n.d(t,"b",function(){return _}),n.d(t,!1,function(){return i});var y=v.a.apps.reduce(function(e,t){var n=t.publicName;return e[t.name]=n,e},{}),b=Object.keys(y).reduce(function(e,t){return e[y[t]]=function(){},e},{}),g=function(e,t){var n=e.config.name,r=y[n];r&&(b[r]=function(){t()(),e.ready(function(){p(r,function(t){e.trigger("container_added",t)}),h(r,function(t){e.trigger("container_removed",t)})}),b[r]=function(){}})},w=function(e){e.forEach(function(e){var t=e.dataset.bvShow;b[t]&&b[t]()})},E=new MutationObserver(function(e){e.forEach(function(e){e.addedNodes&&u(e.addedNodes).then(w)})}),_=function(){i(function(){E.observe(document.body,{childList:!0,subtree:!0}),m().then(w),_=function(){}})}},function(e,t,n){"use strict";t.a=function(e,t){Object.keys(t).forEach(function(n){void 0!==t[n]&&Object.defineProperty(e,n,{value:t[n]})})}},function(e,t,n){"use strict";function r(e,t){document.body&&(t.disconnect(),e())}e.exports=function(e){if(document.body)e();else{var t=new MutationObserver(function(){r(e,t)});t.observe(document.documentElement,{childList:!0,subtree:!0}),r(e,t)}}},function(e,t,n){"use strict";function r(e){for(var t=r.options,n=t.parser[t.strictMode?"strict":"loose"].exec(e),o={},i=14;i--;)o[t.key[i]]=n[i]||"";return o[t.q.name]={},o[t.key[12]].replace(t.q.parser,function(e,n,r){n&&(o[t.q.name][n]=r)}),o}r.options={strictMode:!1,key:["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],q:{name:"queryKey",parser:/(?:^|&)([^&=]*)=?([^&]*)/g},parser:{strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/}},e.exports=r},function(e,t,n){"use strict";var r=n(0),o=n(12).now,i=n(24).getEntriesByType,a=n(13).timeline,c=r.performance,s=c&&c.measure,u="function"==typeof s;function f(e,t,n){return void 0===t?s.call(c,e):void 0===n?s.call(c,e,t):s.call(c,e,t,n)}function l(e,t,n){if(arguments.length<1)throw new TypeError("Failed to execute 'measure' on 'Performance': 1 argument required, but only 0 present.");var r,c=i("mark"),s=0,u=o();if(void 0!==t){if(!(r=c.filter(function(e){return e.entryName===t})).length)throw new Error("Failed to execute 'measure' on 'Performance': The mark '"+t+"' does not exist.");s=r[r.length-1].startTime}if(void 0!==n){if(!(r=c.filter(function(e){return e.entryName===n})).length)throw new Error("Failed to execute 'measure' on 'Performance': The mark '"+n+"' does not exist.");u=r[r.length-1].startTime}var f={entryType:"measure",name:e,startTime:s,duration:u-s};a.push(f)}e.exports={measure:function(e,t,n){var r=arguments.length>0,o=u?f:l;return r?o(e,t,n):o()}}},function(e,t,n){"use strict";var r=n(0),o=n(12),i=n(13).timeline,a=r.performance,c=a&&a.mark,s="function"==typeof c,u={navigationStart:1,unloadEventStart:1,unloadEventEnd:1,redirectStart:1,redirectEnd:1,fetchStart:1,domainLookupStart:1,domainLookupEnd:1,connectStart:1,connectEnd:1,secureConnectionStart:1,requestStart:1,responseStart:1,responseEnd:1,domLoading:1,domInteractive:1,domContentLoadedEventStart:1,domContentLoadedEventEnd:1,domComplete:1,loadEventStart:1,loadEventEnd:1};function f(e){return c.call(a,e)}function l(e){if(arguments.length<1)throw new SyntaxError("Cannot set mark without a name");if(e in u)throw new SyntaxError('Cannot set mark with reserved name "'+e+'"');var t={entryType:"mark",name:e,startTime:o.now(),duration:0};i.push(t)}e.exports={mark:function(e){var t=arguments.length>0,n=s?f:l;return t?n(e):n()}}},function(e,t,n){"use strict";var r=n(0).Date,o=r&&r.now,i="function"==typeof o;function a(){return o.call(r)}function c(){return(new r).getTime()}e.exports={now:function(){return(i?a:c)()}}},function(e,t,n){"use strict";var r=n(0),o=n(14).getEntries,i=r.performance,a=i&&i.getEntriesByName,c="function"==typeof a;function s(e,t){return a.call(i,e,t)}function u(e,t){if(0===arguments.length)throw new TypeError("Failed to execute 'getEntriesByName' on 'Performance': 1 argument required, but only 0 present.");return o().filter(function(n){if(n.name===e&&(void 0===t||n.entryType===t))return n})}e.exports={getEntriesByName:function(e,t){var n=arguments.length>0,r=c?s:u;return n?r(e,t):r()}}},function(e,t,n){"use strict";n.r(t),function(e){var t=n(45),r=n(17),o=n(20),i=n(19),a=n(1),c=n(2),s=n(7),u=n.n(s),f=n(0),l=n.n(f),d=n(22),m=n(4),p=n(28),h=n(38),v=n(18),y=n(21),b=n(44),g=n(40),w=function(){return function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],r=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(r=(a=c.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{!r&&c.return&&c.return()}finally{if(o)throw i}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),E=l.a.document,_=t.a.createMark("bv_loader");Object(b.a)(),_.end=_.start();l.a.navigator.userAgent.indexOf("MSIE")>-1||l.a.navigator.userAgent.indexOf("Trident/");if(c.a.pixel=d.a.createTracker({commonData:{bvProduct:"pixel"},name:"pixel",source:"bv-loader"}),a.a.apps.forEach(function(e){return Object(g.a)(e,_)}),Object(p.b)(),l.a.$BV&&"ui"in l.a.$BV||a.a.isPreview||(l.a.$BV=Object(h.a)()),"container"===y.b()&&(a.a.isConversations?l.a.$BV.container("global",{}):l.a.$BV.ui("submission_container",{})),u.a.loadScript("https://analytics-static.ugc.bazaarvoice.com/prod/static/3/bv-analytics.js"),m.a)if(l.a[m.a])j(l.a[m.a]);else{var S=void 0;Object.defineProperty(l.a,m.a,{get:function(){return S},set:function(e){S=e,j(e)}})}_.end();var k=w(_.measures,1)[0],T=k.duration,x=k.startTime,P=x+T;try{v.a.trackEvent("Diagnostic",{name:"timeToRunScout",type:"Performance",startTime:x.toFixed(4),endTime:P.toFixed(4),elapsedMs:T.toFixed(4)})}catch(e){}function j(e){if("function"!=typeof e)throw Error("The BV Callback must be a function.");setTimeout(function(){e.call(null,c.a)})}i.a&&(new e(function(e){E.addEventListener("DOMContentLoaded",function(){e()}),setTimeout(function(){e()},3e3)}).then(function(){Object(o.a)("page",Object(r.c)())}),setTimeout(function(){Object(o.a)("bv-loader",Object(r.b)())},0))}.call(this,n(6))},function(e,t,n){"use strict";var r=n(7),o=n.n(r),i=n(39),a=function(){return function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],r=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(r=(a=c.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{!r&&c.return&&c.return()}finally{if(o)throw i}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),c=document.createElement("script"),s=window.navigator.userAgent.toLowerCase(),u={noModule:"noModule"in c,isFirefox:!!~s.indexOf("firefox"),isChrome:!!~s.indexOf("chrome")};u.isSafari=!u.isChrome&&!!~s.indexOf("safari"),u.doModernLoad=u.isSafari&&u.noModule?function(){var e=s.match(/macintosh.+mac os x (\d+)_(\d+)_\d+\)/),t=s.match(/version\/(\d+)\.\d+\.\d+ safari\/\d+\.\d+\.\d/);if(e){var n=a(e,3),r=n[1],o=n[2];if(Number(r)>=10&&Number(o)>=13&&t){var i=a(t,2)[1];return Number(i)>=11}return!1}return!0}():!u.noModule&&u.isFirefox?function(){var e=s.match(/(?:firefox\/)([\d.]+)/);if(e&&e.length){var t=a(e,2)[1];return Number(t)>54}return!1}():u.noModule;var f=u;function l(e,t,n){n.load.end=n.load.start();var r=e.sourcePath;if(e.legacyPath&&!f.doModernLoad&&(r=e.legacyPath),o.a.loadScript(r,function(r){Object(i.a)(r,e,t,n)}),e.configUrl){var a=n.createMark("load","config");a.end=a.start(),o.a.loadScript(e.configUrl,function(){a.end()})}}n.d(t,"a",function(){return l})},function(e,t,n){"use strict";(function(e){var r=n(0),o=n.n(r),i=n(7),a=n.n(i),c=n(1),s=n(4),u=n(21),f=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l=o.a.console,d=!1;function m(){var t=this;this.promiseRef=new e(function(e,n){f(t,{resolve:e,reject:n})})}t.a=function(){return function(){var e=o.a.$BV||[],t={_apiQueue:[],log:function(){l&&l.log&&l.log(arguments)},push:function(e){d||function(){if(!c.a.isConversations&&(s.c&&(c.a.legacyScoutUrl=c.a.legacyScoutUrl.replace(/^(https?:\/\/)([^/]+)/,"$1"+s.c)),s.b&&(c.a.legacyScoutUrl=c.a.legacyScoutUrl.replace(/\/+([^/]+)\/bvapi.js/,"/"+s.b+"/bvapi.js")),/\[HOSTNAME\]/.test(c.a.legacyScoutUrl)||/\[DISPLAYCODE\]/.test(c.a.legacyScoutUrl)))throw Error('Could not determine display code and hostname. Contact Bazaarvoice Support to configure your implementation, or provide explicit "data-bv-display-code" and "data-bv-hostname" attributes.');d=!0,a.a.loadScript(c.a.legacyScoutUrl)}(),t._apiQueue.push(e)}},n=u.c();n&&t._apiQueue.push(["configure","global",{userToken:n}]),e.forEach(function(e){t.push(e)});return["ui","configure","container","ready","SI.trackProductPageView","SI.trackGenericPageView","SI.trackTransactionPageView","SI.disable","SI.enable","SI.setDebugEnabled","SI.trackConversion","DSI.trackBVPageView","DSI.trackProduct","DSI.trackTransaction"].forEach(function(e){!function(e,n){for(var r,o=n.split("."),i=o.pop(),a=o.shift();a;)e=e[a]=e[a]||{},a=o.shift();e[i]=(r=n.replace(/\./g,"_"),function(){for(var e=void 0,n=[r],o=0,i=arguments.length;o<i;o++)n.push(arguments[o]);return"ui"!==r?t.push(n):((e=n[3]=n[3]||{}).loadedDeferred=e.loadedDeferred||new m,t.push(n),e.loadedDeferred.promise())})}(t,e)}),t}()},m.prototype.promise=function(){return this.promiseRef}}).call(this,n(6))},function(e,t,n){"use strict";(function(e){n.d(t,"a",function(){return i});var r=n(17),o=n(20);function i(t,n,i,a){return a.load.end(),t&&i.processReady(t),new e(function(e){i.ready(function(){setTimeout(function(){e()},0)}),setTimeout(function(){e()},3e3)}).then(function(){Object(o.a)(n.name,Object(r.a)(n.name),n.name,n.version)})}}).call(this,n(6))},function(e,t,n){"use strict";(function(e){n.d(t,"a",function(){return v});var r=n(16),o=n.n(r),i=n(26),a=n.n(i),c=n(2),s=n(22),u=n(21),f=n(4),l=n(28),d=n(37),m=n(18),p=e.BV,h=u.a();function v(e,t){var n=t.createMark("register",e.name);n.end=n.start();var r=new o.a({name:e.name});c.a.registerProperty(e.name,r);var i=t.createMark("app",e.name),a=i.createMark("configure"),u=i.createMark("load"),v=i.createMark("render");i.configure=a,i.load=u,i.render=v,c.a[e.name].perfMark=i,c.a[e.name]._analytics=s.a.createTracker({name:e.name,source:"bv-loader"}),p.global.loadUserSegment.then(function(t){c.a[e.name]._analytics.setCommonData({displaySegment:t})}),"api"===e.name&&(c.a[e.name].analytics=c.a[e.name]._analytics);var y=function(){h.some(function(t){return~[e.name,e.publicName].indexOf(t)})?m.a.trackEvent("Feature",{type:"Used",name:"AppDisabled",detail1:e.name}):Object(d.a)(e,r,i),y=function(){}};if(f.d.some(function(t){return t===e.name})||!e.defer)y();else{var b=r.configure,g=r.ready,w=r.render;["configure","ready","render"].forEach(function(e){r[e]=function(){return function(e,t){return y(),r.configure=b,r.ready=g,r.render=w,r[e].apply(this,t)}.call(this,e,arguments)}})}Object(l.a)(r,function(){return y}),n.end()}a.a.call(o.a.prototype)}).call(this,n(11))},function(e,t,n){"use strict";(function(e){n.d(t,"a",function(){return i});var r=1e4,o=new Map;function i(t){var n=o.get(t);if(n)return n;var i=new XMLHttpRequest;return n=new e(function(e,n){i.onreadystatechange=function(){if(i.readyState===XMLHttpRequest.DONE)if(200===i.status){var t=void 0;try{t=JSON.parse(i.responseText)}catch(e){n("Unable to JSON.parse server response: "+i.responseText+".")}e(t)}else n("The call to the server was not successful.")},i.open("GET",t),i.timeout=r,i.ontimeout=function(){i.abort()},i.send()}),o.set(t,n),n}}).call(this,n(6))},function(e,t,n){"use strict";(function(e){n.d(t,"a",function(){return w});var r=n(8),o=n.n(r),i=n(0),a=n.n(i),c=n(2),s=n(1),u=n(41),f=function(){return function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],r=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(r=(a=c.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{!r&&c.return&&c.return()}finally{if(o)throw i}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),l=14,d="baseline",m=864e5,p=60*m,h="bv_segment",v="bv_segment",y=Math.random();function b(){return Object(u.a)(s.a.paths.splitTests).then(function(e){return e.tests},function(){return[]})}function g(e){var t=void 0;try{var n=e.filter(function(e){return t=e,!(new Date(t.start).getTime()+(t.duration||p)<Date.now()||t.forceExpire);var t}),r=n.filter(function(e){var t=e.clients;if(!t)return!1;return-1!==t.indexOf(c.a.global.client)}),o=n.filter(function(e){return!e.clients}),i=r.length>0?r:o;i.sort(function(e,t){return new Date(e.startDate)-new Date(t.startDate)}),t=i.shift()}catch(e){throw new Error("Something went wrong when choosing a split test.\n      Please ensure that all defined tests are valid. "+e)}return t}function w(){if(!s.a.splitTestingEnabled)return e.resolve(d);var t,n=(t=v,a.a.location.search.substr(1).split("&").filter(function(e){return e.split("=")[0]===t}).map(function(e){return e.split("=")[1]})[0]);if(n)return e.resolve(n);var r=o.a.read(h);if(r){var i=void 0;try{i=JSON.parse(r)}catch(e){}if(i){var c=i.segment;if(c){var u=e.resolve(c);return u.then(function(){return e=i.testId,void b().then(function(t){var n=t.filter(function(t){return t.id===e}),r=f(n,1)[0];r&&r.forceExpire&&o.a.remove(h)});var e}),u}}}return b().then(g).then(function(e){if(!e)return d;var t=new Date(e.start).getTime();if(t<=Date.now()){var n=function(e){return((arguments.length>1&&void 0!==arguments[1]?arguments[1]:l*m)-(Date.now()-e))/m}(t,e.duration);if(n>0){var r=e.id,i=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=0,n=Object.keys(e).sort();n.length;){var r=n.pop(),o=e[r];if(y<=t+o)return r;t+=o}return d}(e.splits),a=JSON.stringify({testId:r,segment:i});return o.a.create(h,a,n),i||d}}})}}).call(this,n(6))},function(e,t,n){"use strict";(function(e,r){var o=n(42),i=n(9),a=n.n(i),c=n(1),s=n(2),u=n(29);Object.assign;s.a.global={},Object(u.a)(s.a.global,{client:c.a.deployment.client,dataEnvironment:c.a.dataEnvironment,serverEnvironment:c.a.serverEnvironment,locale:c.a.deployment.locale,anonymous:!a()(e.location.host,c.a.domains).get("thirdPartyCookieEnabled"),siteId:c.a.deployment.site,virtualEnvironment:c.a.deployment.virtualEnvironment,ponyfills:{Promise:r},SystemJS:{}}),Object(u.a)(s.a.global,{loadUserSegment:Object(o.a)()}),Object.freeze(s.a.global),t.a=s.a.global}).call(this,n(11),n(6))},function(e,t,n){"use strict";(function(e){n.d(t,"a",function(){return p});var r=n(9),o=n.n(r),i=n(1),a=n(5),c=n.n(a),s=n(23),u=n(10),f=e.location,l=f.host,d=f.href,m=o()(d,i.a.domains);function p(){!function(e){if(!e.isValid)throw"Bazaarvoice is not configured for the domain "+l+"."}(m),function(e){if(e&&e.src){var t=s.a.parseClient(e.src),n=t&&t.toLowerCase();if(t&&t!==n){var r=e.src.replace("/deployments/"+t,"/deployments/"+n);console.warn("%cYou are using a cased client name (%c"+t+"%c) in the bv.js URL.\n        Please use this URL instead: %c"+r+"%c.\n        For details, see the Conversations Implementation section of https://knowledge.bazaarvoice.com.",u.b,u.a,u.b,u.c,u.b)}}}(c.a)}}).call(this,n(11))},function(e,t,n){"use strict";var r=n(3),o=n.n(r),i=void 0,a=0,c=function(e){o.a.mark(e)},s=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.join("_")},u=s("BV","PERF","MARK"),f=function e(t){for(var n=arguments.length,r=Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];if(r.length){var u=s(t,r.shift());return e.call.apply(e,[i,u].concat(r))}var f=[],l=[],d=function(e){c(e?s(t,e):t);var n=o.a.getEntriesByType("mark"),r=n[n.length-1];return f.push(r),r},m={createMark:{configurable:!0,enumerable:!0,value:e.bind(i,t)},marks:{configurable:!0,enumerable:!0,get:function(){return f}},measures:{configurable:!0,enumerable:!0,get:function(){return l}},start:{configurable:!0,enumerable:!0,value:function(){return e=d("start"),function(){var n=d("end");o.a.measure(t);var r=o.a.getEntriesByType("measure"),i=r[r.length-1];return Object.defineProperties(i,{startTime:{configurable:!0,enumerable:!0,value:e.startTime,writable:!1},duration:{configurable:!0,enumerable:!0,value:n.startTime-e.startTime,writable:!1}}),f.push(n),l.push(i),n};var e}}};return Object.defineProperties(d,m),d};t.a={createMark:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return f.call.apply(f,[i,s(u,e||++a)].concat(n))}}}]);
//# sourceURL=https://apps.bazaarvoice.com/bv.js