<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Accueil - Portail des outils SCM</title>
    <link rel="icon" href="{{ asset('icon.png') }}" type="image/png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('atropos/atropos.min.css') }}">
    <style>
      .tool-card {
        transition: transform 0.3s;
      }
      .tool-card:hover {
        transform: scale(1.05);
      }
    </style>
  </head>
  <body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
      <div class="container">
        <a class="navbar-brand" href="#">Portail SCM</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarHome" aria-controls="navbarHome" aria-expanded="false" aria-label="Basculer la navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        {# <div class="collapse navbar-collapse" id="navbarHome">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="{{ path('app_home') }}">Accueil</a>
            </li>
          </ul>
        </div> #}
      </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container my-5">
      <h1 class="mb-4 text-center">Portail des Outils SCM</h1>
      <p class="lead text-center">Choisissez l'outil à utiliser</p>
      <div class="row">
          <div class="col-md-4 mb-4">
              <div class="atropos my-atropos" data-atropos-offset="10">
                  <div class="atropos-scale">
                      <div class="atropos-rotate">
                          <div class="card tool-card h-100 shadow-sm">
                              <div class="card-body">
                                  <h5 class="card-title">Diffusion</h5>
                                  <p class="card-text">
                                      Gérer la création, modification ou suppression d'articles et docs dans SAP.
                                  </p>
                              </div>
                              <div class="card-footer bg-transparent border-top-0">
                                  <a href="{{ path('suivie_ref') }}" class="btn btn-primary w-100">Diffusion</a>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>

          <div class="col-md-4 mb-4">
              <div class="atropos my-atropos" data-atropos-offset="5">
                  <div class="atropos-scale">
                      <div class="atropos-rotate">
                          <div class="card tool-card h-100 shadow-sm">
                              <div class="card-body">
                                  <h5 class="card-title">DMO</h5>
                                  <p class="card-text">
                                      Créez et suivez vos demandes de modification pour Engineering ou Method.
                                  </p>
                              </div>
                              <div class="card-footer bg-transparent border-top-0">
                                  <a href="{{ path('app_dmo_home') }}" class="btn btn-primary w-100">DMO</a>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>

          {# <div class="col-md-4 mb-4">
              <div class="atropos my-atropos" data-atropos-offset="5">
                  <div class="atropos-scale">
                      <div class="atropos-rotate">
                          <div class="card tool-card h-100 shadow-sm">
                              <div class="card-body">
                                  <h5 class="card-title">Imputation des heures</h5>
                                  <p class="card-text">
                                      Enregistrez vos heures par phase de projet pour un suivi précis.
                                  </p>
                              </div>
                              <div class="card-footer bg-transparant border-top-0">
                                  <a href="{{ path('app_projet') }}" class="btn btn-primary w-100">Imputation</a>
                              </div>
                          </div>
                      </div>
                  </div>
              </div> #}
          </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('atropos/atropos.min.js') }}"></script>
    <script>
      let atroposInitialized = false;
      function initAtropos() {
        if (!atroposInitialized) {
          document.querySelectorAll('.my-atropos').forEach((el) => {
            Atropos({
              el: el,
              activeOffset: 50,
              shadow: false,
              highlight: false,
              rotateXMax: 20,
              rotateYMax: 20
            });
          });
          atroposInitialized = true;
          console.log("On dirait que vous vous êtes endormi... 👀👀👀 !");
        }
      }
      setTimeout(initAtropos, 30000);
    </script>
  </body>
</html>
