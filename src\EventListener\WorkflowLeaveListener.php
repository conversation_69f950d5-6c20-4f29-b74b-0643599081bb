<?php

namespace App\EventListener;

use Symfony\Component\Workflow\Event\LeaveEvent;
use Psr\Log\LoggerInterface;

class WorkflowLeaveListener
{
    private LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function onWorkflowLeave(LeaveEvent $event): void
    {
        $document = $event->getSubject();
        $from = $event->getTransition()->getFroms();

        $this->logger->info(sprintf(
            'Document ID %d a quitté l\'étape %s.',
            $document->getId(),
            implode(', ', $from)
        ));
    }
}
