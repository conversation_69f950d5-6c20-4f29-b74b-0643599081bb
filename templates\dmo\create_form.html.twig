{% extends 'base.html.twig' %}
{% block navbar %}
{% include '_partials/_nav2.html.twig' %}
{% endblock %}

{% block title %}
    Création d'un DMO
{% endblock %}

{% block body %}
<style>
  .card { box-shadow: 0 .5rem 1rem rgba(0,0,0,.15); }
</style>

<div class="mt-5" style="margin: 0 25%">
  <div class="card mx-auto" >
    <div class="card-header bg-primary text-white d-flex align-items-center" style="background: linear-gradient(135deg, #009BFF 0%, #0056b3 100%)">
      <i class="bi bi-plus-circle me-2"></i>
      <h2 class="h5 mb-0">Créer une nouvelle DMO</h2>
    </div>
    <div class="card-body">
      <form method="POST" action="{{ path('app_dmo_create') }}" enctype="multipart/form-data" novalidate>

        <div class="row g-3">
          <!-- Type de modification -->
          <div class="col-md-6">
            <div class="form-floating">
              <select id="type" name="type" class="form-select select2" required>
                <option value="" disabled selected>Choisir…</option>
                <option value="Engineering">Engineering</option>
                <option value="Method Assy.">Assembly Method</option>
                <option value="Method Lab.">Laboratory Method</option>
              </select>
              <label for="type">Type de modification *</label>
              <div class="invalid-feedback">Veuillez sélectionner un type.</div>
            </div>
          </div>

          <!-- Type de document -->
          <div class="col-md-6">
            <div class="form-floating">
              <select id="document" name="document" class="form-select select2" required>
                <option value="" disabled selected>Choisir…</option>
                <!-- Options dynamiques -->
              </select>
              <label for="document">Type de document *</label>
              <div class="invalid-feedback">Ce champ est requis.</div>
            </div>
          </div>

          <!-- Division -->
          <div class="col-md-6">
            <div class="form-floating">
              <select id="division" name="division" class="form-select select2" required>
                <option value="" disabled selected>Choisir…</option>
                <!-- Options dynamiques -->
              </select>
              <label for="division">Division *</label>
              <div class="invalid-feedback">Veuillez sélectionner une division.</div>
            </div>
          </div>

          <!-- Product Range -->
          <div class="col-md-6">
            <div class="form-floating">
              <select id="productRange" name="productRange" class="form-select select2" required>
                <option value="" disabled selected>Choisir…</option>
                <!-- Options dynamiques -->
              </select>
              <label for="productRange">Product Range *</label>
              <div class="invalid-feedback">Ce champ est requis.</div>
            </div>
          </div>

          <!-- Projet -->
          <div class="col-12">
            <div class="form-floating">
              <select id="project" name="project" class="form-select select2" required>
                <option value="" disabled selected>Choisir un projet…</option>
                <!-- Options dynamiques -->
              </select>
              <label for="project">Projet *</label>
              <div class="invalid-feedback">Veuillez sélectionner un projet.</div>
            </div>
          </div>

          <!-- Pièces jointes -->
          <div class="col-12">
            <div class="mb-3">
              <label for="mesfichiers" class="form-label"><strong>Pièces jointes (10 Mo max)</strong></label>
              <input type="file" id="mesfichiers" name="mesfichiers[]" multiple class="form-control">
              <small class="text-muted">Vous pouvez sélectionner plusieurs fichiers à la fois.</small>
            </div>
          </div>

          <!-- Description -->
          <div class="col-12">
            <div class="form-floating">
              <textarea id="description" name="description" class="form-control" placeholder="Décrivez la modification…" style="height: 120px;" required></textarea>
              <label for="description">Description *</label>
              <div class="invalid-feedback">La description est obligatoire.</div>
            </div>
          </div>
        </div>

        <div class="mt-4">
          <button type="submit" class="btn btn-primary w-100">Créer DMO</button>
        </div>
      </form>
    </div>
  </div>
</div>


    <script>
        $(document).on('change', '#type', function() {
            var type = $(this).val();
            console.log('Type:', type);

            var documentSelect = $("#document");
            documentSelect.html('<option value="">-- Sélectionner un document --</option>');

            if (type === 'Engineering') {
                documentSelect.append('<option value="DEO">DEO</option>');
                documentSelect.append('<option value="Drawing">Drawing</option>');
                documentSelect.append('<option value="NT">NT</option>');
                documentSelect.append('<option value="NU">NU</option>');
                documentSelect.append('<option value="Specification">Specification</option>');
                documentSelect.append('<option value="Other">Other</option>');
            } else if (type === 'Method Assy.') {
                documentSelect.append('<option value="Assembly Tool">Assembly Tool</option>');
                documentSelect.append('<option value="Assy. Checklist">Assy. Checklist</option>');
                documentSelect.append('<option value="Bonne Prat.">Bonne Prat.</option>');
                documentSelect.append('<option value="COSIR">COSIR</option>');
                documentSelect.append('<option value="DEO">DEO</option>');
                documentSelect.append('<option value="FI">FI</option>');
                documentSelect.append('<option value="FUM">FUM</option>');
                documentSelect.append('<option value="IRS">IRS</option>');
                documentSelect.append('<option value="NU">NU</option>');
                documentSelect.append('<option value="OSIR">OSIR</option>');
                documentSelect.append('<option value="PHI">PHI</option>');
                documentSelect.append('<option value="SAP Drawing">SAP Drawing</option>');
                documentSelect.append('<option value="Specification">Specification</option>');
                documentSelect.append('<option value="Other">Other</option>');
            } else if (type === 'Method Lab.') {
                documentSelect.append('<option value="Bonne Prat.">Bonne Prat.</option>');
                documentSelect.append('<option value="DEO">DEO</option>');
                documentSelect.append('<option value="FATP">FATP</option>');
                documentSelect.append('<option value="FI">FI</option>');
                documentSelect.append('<option value="FOL">FOL</option>');
                documentSelect.append('<option value="FUM">FUM</option>');
                documentSelect.append('<option value="Lab. Tool">Lab. Tool</option>');
                documentSelect.append('<option value="QPP">QPP</option>');
                documentSelect.append('<option value="QPR">QPR</option>');
                documentSelect.append('<option value="Security Chklst">Security Chklst</option>');
                documentSelect.append('<option value="Specification">Specification</option>');
                documentSelect.append('<option value="Other">Other</option>');
            } else {
                documentSelect.html('<option value="">-- Sélectionner un type de modification ▲ --</option>');
            }
        });

        $(document).ready(function() {
            $.ajax({
                url: "{{ path('app_dmo_getProductRange') }}", // Endpoint qui renvoie { division1: [range1, range2], division2: [...], ... }
                method: 'GET',
                dataType: 'json'
            }).done(function(data) {
                var divisionSelect = $("#division");
                divisionSelect.html('<option value="">-- Sélectionner une division --</option>');
                
                $.each(data, function(division, productRanges) {
                    divisionSelect.append('<option value="' + division + '">' + division + '</option>');
                });
            }).fail(function(jqXHR, textStatus, errorThrown) {
                console.error("Erreur lors du chargement des divisions:", textStatus, errorThrown);
            });

            $.ajax({
                url: "{{ path('app_dmo_getAllProjectRelation') }}", 
                method: 'GET',
                dataType: 'json'
            }).done(function(data) {
                var projectSelect = $("#project");
                projectSelect.html('<option value="">-- Sélectionner un projet --</option>');
                $.each(data, function(index, project) {
                    projectSelect.append('<option value="' + project.id + '">' + project.OTP + '</option>');
                });
            }).fail(function(jqXHR, textStatus, errorThrown) {
                console.error("Erreur lors du chargement des projets:", textStatus, errorThrown);
            });

            $(document).on('change', '#division', function() {
                var selectedDivision = $(this).val();
                var productRangeSelect = $("#productRange");
                productRangeSelect.html('<option value="">-- Sélectionner un product range --</option>');

                if (selectedDivision) {
                    $.ajax({
                        url: "{{ path('app_dmo_getProductRange') }}",
                        method: 'GET',
                        dataType: 'json'
                    }).done(function(data) {
                        if (data[selectedDivision]) {
                            $.each(data[selectedDivision], function(index, item) {
                                productRangeSelect.append('<option value="' + item + '">' + item + '</option>');
                            });
                        }
                    }).fail(function(jqXHR, textStatus, errorThrown) {
                        console.error("Erreur lors du chargement des product ranges:", textStatus, errorThrown);
                    });
                }
            });
        });
    </script>
{% endblock %}
