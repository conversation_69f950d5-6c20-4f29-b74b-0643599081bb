<?php

namespace App\Command;

use App\Entity\DMO;
use App\Service\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-email',
    description: 'Test l\'envoi d\'emails pour les DMO',
)]
class TestEmailCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private EmailService $emailService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('dmo_id', InputArgument::REQUIRED, 'ID de la DMO à tester')
            ->addArgument('email', InputArgument::OPTIONAL, 'Email de test (optionnel)')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $dmoId = $input->getArgument('dmo_id');
        $testEmail = $input->getArgument('email');

        $dmo = $this->entityManager->getRepository(DMO::class)->find($dmoId);
        
        if (!$dmo) {
            $io->error("DMO avec l'ID {$dmoId} non trouvée");
            return Command::FAILURE;
        }

        $io->title('Test d\'envoi d\'email DMO');
        
        // Afficher les informations de la DMO
        $io->section('Informations DMO');
        $io->table(
            ['Propriété', 'Valeur'],
            [
                ['ID', $dmo->getId()],
                ['DMO ID', $dmo->getDmoId()],
                ['Décision', $dmo->getDecision()],
                ['Statut', $dmo->isStatus() ? 'Ouvert' : 'Fermé'],
                ['Description', $dmo->getDescription() ?? 'N/A'],
            ]
        );

        // Vérifier le demandeur
        $requestor = $dmo->getRequestor();
        if (!$requestor) {
            $io->error('Aucun demandeur associé à cette DMO');
            return Command::FAILURE;
        }

        $io->section('Informations Demandeur');
        $io->table(
            ['Propriété', 'Valeur'],
            [
                ['ID', $requestor->getId()],
                ['Nom', $requestor->getNom() ?? 'N/A'],
                ['Prénom', $requestor->getPrenom() ?? 'N/A'],
                ['Email', $requestor->getEmail() ?? 'N/A'],
                ['Département', $requestor->getDepartement() ?? 'N/A'],
            ]
        );

        if (!$requestor->getEmail()) {
            $io->error('Le demandeur n\'a pas d\'adresse email');
            return Command::FAILURE;
        }

        // Si un email de test est fourni, l'utiliser temporairement
        $originalEmail = null;
        if ($testEmail) {
            $originalEmail = $requestor->getEmail();
            $requestor->setEmail($testEmail);
            $io->note("Email temporairement changé vers: {$testEmail}");
        }

        // Test de configuration SMTP
        $io->section('Test de configuration SMTP');
        try {
            $reflection = new \ReflectionClass($this->emailService);
            $createMailerMethod = $reflection->getMethod('createMailer');
            $createMailerMethod->setAccessible(true);
            
            $mailer = $createMailerMethod->invoke($this->emailService);
            $io->success('Configuration PHPMailer créée avec succès');
            
            $io->table(
                ['Paramètre', 'Valeur'],
                [
                    ['Host', $mailer->Host],
                    ['Port', $mailer->Port],
                    ['Username', $mailer->Username],
                    ['SMTPAuth', $mailer->SMTPAuth ? 'Oui' : 'Non'],
                    ['SMTPSecure', $mailer->SMTPSecure],
                ]
            );
        } catch (\Exception $e) {
            $io->error('Erreur lors de la création du mailer: ' . $e->getMessage());
            return Command::FAILURE;
        }

        // Test d'envoi d'email
        $io->section('Test d\'envoi d\'email');
        
        if ($io->confirm('Voulez-vous envoyer un email de test ?', false)) {
            try {
                $this->emailService->sendDecisionEmail($dmo, 'OLD_DECISION');
                $io->success('Email envoyé avec succès !');
            } catch (\Exception $e) {
                $io->error('Erreur lors de l\'envoi: ' . $e->getMessage());
                $io->note('Détails de l\'erreur: ' . $e->getTraceAsString());
            }
        }

        // Restaurer l'email original si nécessaire
        if ($originalEmail) {
            $requestor->setEmail($originalEmail);
            $this->entityManager->flush();
        }

        return Command::SUCCESS;
    }
}
