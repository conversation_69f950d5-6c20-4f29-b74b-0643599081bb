# Nettoyage des données lors des changements de types de documents

## Vue d'ensemble

Cette fonctionnalité implémente le nettoyage automatique des données lors des changements de types de documents (doctype) selon les spécifications métier. **Le nettoyage dépend à la fois de la transition de doctype ET de la place d'origine du document dans le workflow.**

## Règles de nettoyage implémentées

### Transitions vers PUR (Purchase)

#### Depuis Prod Usinage (Machining) : MACH -> PUR
- **Actions** : Changement du doc_type + passage de la ref en Qualité
- **Nettoyage** : Données remplies en étape Prod Usinage
  - Mat type (`matProdType`)
  - Unit (`unit`)
  - Leadtime (`leadtime`)
  - Proc_type (`procType`)
  - Pris dans 1 (`prisDans1`)
  - Pris dans 2 (`prisDans2`)
  - MOF (`mof`)

#### Depuis Prod Moulage (Molding) : MOLD -> PUR
- **Actions** : Changement du doc_type + passage de la ref en Qualité
- **Nettoyage** : Données remplies en étape Prod Moulage
  - Mat type (`matProdType`)
  - Unit (`unit`)
  - Leadtime (`leadtime`)
  - Proc_type (`procType`)
  - Pris dans 1 (`prisDans1`)
  - Pris dans 2 (`prisDans2`)
  - MOF (`mof`)

#### Depuis Prod Assembly (Assembly) : ASSY -> PUR
- **Actions** : Changement du doc_type + passage de la ref en Qualité
- **Nettoyage** : Données remplies en étape Prod Assembly
  - Mat type (`matProdType`)
  - Unit (`unit`)
  - Leadtime (`leadtime`)
  - Proc_type (`procType`)
  - **Note** : MOF, Pris dans 1 et 2 ne sont PAS nettoyés pour ASSY

### Transitions depuis PUR

#### Depuis Pris Dans (Achat_F30) : PUR -> MACH/MOLD/ASSY
- **Actions** : Changement du doc_type + passage vers la production correspondante
- **Nettoyage** : Données remplies en étape RFQ
  - Mat type (`matProdType`)
  - Unit (`unit`)
  - Commodity (`commodityCode`)
  - Buyer (`purchasingGroup`)
  - Proc_type (`procType`)

#### Depuis RFQ (Achat_Rfq) : PUR -> MACH/MOLD/ASSY
- **Actions** : Changement du doc_type + passage vers la production correspondante
- **Nettoyage** : Données remplies en étape RFQ
  - Mat type (`matProdType`)
  - Unit (`unit`)
  - Commodity (`commodityCode`)
  - Buyer (`purchasingGroup`)
  - Proc_type (`procType`)

## Implémentation technique

### Fichiers modifiés

1. **`src/Controller/DocumentController.php`**
   - Ajout de la méthode `cleanDataForDoctypeTransition()`
   - Ajout de la méthode `cleanProductionFields()`
   - Ajout de la méthode `cleanRFQFields()`
   - Intégration dans la logique existante de changement de doctype

### Méthodes ajoutées

#### `cleanDataForDoctypeTransition()`
Méthode principale qui détermine quelle logique de nettoyage appliquer selon la transition et la place d'origine.

#### `getCurrentWorkflowPlace()`
Détermine la place actuelle principale du document dans le workflow en priorisant les places de production et d'achat.

#### `handleTransitionToPur()`
Gère les transitions vers PUR selon la place d'origine (Assembly, Molding, Machining).

#### `handleTransitionFromPur()`
Gère les transitions depuis PUR selon la place d'origine (Achat_F30, Achat_Rfq).

#### `cleanProductionFieldsAssembly()`
Nettoie les champs de base de production pour Assembly (sans MOF, sans pris dans 1 et 2).

#### `cleanProductionFieldsComplete()`
Nettoie tous les champs de production (avec MOF et pris dans 1 et 2) pour Machining et Molding.

#### `cleanRFQFields()`
Nettoie les champs liés à l'étape RFQ (Request for Quotation).

### Historique des modifications

Chaque nettoyage automatique est enregistré dans l'historique des mises à jour du document avec le détail des champs nettoyés.

## Tests

### Tests unitaires
- `tests/Controller/DocumentDoctypeTransitionTest.php`
- Couvre tous les scénarios de nettoyage
- Vérifie que les bons champs sont nettoyés selon les transitions

### Test manuel
- `tests/manual_doctype_transition_test.php`
- Script de démonstration des transitions

## Utilisation

Le nettoyage est automatique et se déclenche lors de la modification du champ `doctype` d'un document via l'API REST du contrôleur.

La logique s'intègre dans le flux existant :
1. Détection du changement de doctype
2. Appel du service de réinitialisation existant
3. **Nouveau** : Nettoyage spécifique selon les règles métier
4. Mise à jour du workflow
5. Enregistrement en base de données

## Notes importantes

- Le nettoyage ne s'applique que si les champs ont des valeurs non-nulles
- L'historique des modifications est automatiquement mis à jour
- La logique respecte les différences entre les types de production (MACH/MOLD vs ASSY)
- Les transitions non définies dans les règles ne déclenchent aucun nettoyage spécifique
