<?php

namespace App\Tests\Repository;

use App\Entity\Document;
use App\Entity\User;
use App\Entity\Visa;
use App\Entity\ReleasedPackage;
use App\Entity\Project;
use App\Entity\DMO;
use App\Repository\DocumentRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class DocumentRepositoryStatisticsTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private DocumentRepository $documentRepository;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()->get('doctrine')->getManager();
        $this->documentRepository = $this->entityManager->getRepository(Document::class);
    }

    public function testGetAverageTimePerWorkflowStep(): void
    {
        // Test que la méthode retourne un array
        $result = $this->documentRepository->getAverageTimePerWorkflowStep();
        
        $this->assertIsArray($result);
        
        // Si des données existent, vérifier la structure
        if (!empty($result)) {
            foreach ($result as $step => $avgTime) {
                $this->assertIsString($step);
                $this->assertIsFloat($avgTime);
                $this->assertGreaterThanOrEqual(0, $avgTime);
            }
        }
    }

    public function testGetCycleTimeStatistics(): void
    {
        $result = $this->documentRepository->getCycleTimeStatistics();
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('avg_cycle_time', $result);
        $this->assertArrayHasKey('min_cycle_time', $result);
        $this->assertArrayHasKey('max_cycle_time', $result);
        $this->assertArrayHasKey('completed_count', $result);
        
        $this->assertIsFloat($result['avg_cycle_time']);
        $this->assertIsInt($result['min_cycle_time']);
        $this->assertIsInt($result['max_cycle_time']);
        $this->assertIsInt($result['completed_count']);
        
        $this->assertGreaterThanOrEqual(0, $result['avg_cycle_time']);
        $this->assertGreaterThanOrEqual(0, $result['min_cycle_time']);
        $this->assertGreaterThanOrEqual(0, $result['max_cycle_time']);
        $this->assertGreaterThanOrEqual(0, $result['completed_count']);
    }

    public function testGetVisaStatisticsByStep(): void
    {
        $result = $this->documentRepository->getVisaStatisticsByStep();
        
        $this->assertIsArray($result);
        
        // Si des données existent, vérifier la structure
        if (!empty($result)) {
            foreach ($result as $step => $stats) {
                $this->assertIsString($step);
                $this->assertIsArray($stats);
                
                $this->assertArrayHasKey('total', $stats);
                $this->assertArrayHasKey('approved', $stats);
                $this->assertArrayHasKey('pending', $stats);
                $this->assertArrayHasKey('approval_rate', $stats);
                $this->assertArrayHasKey('avg_approval_time_hours', $stats);
                
                $this->assertIsInt($stats['total']);
                $this->assertIsInt($stats['approved']);
                $this->assertIsInt($stats['pending']);
                $this->assertIsFloat($stats['approval_rate']);
                
                $this->assertGreaterThanOrEqual(0, $stats['total']);
                $this->assertGreaterThanOrEqual(0, $stats['approved']);
                $this->assertGreaterThanOrEqual(0, $stats['pending']);
                $this->assertGreaterThanOrEqual(0, $stats['approval_rate']);
                $this->assertLessThanOrEqual(100, $stats['approval_rate']);
                
                // avg_approval_time_hours peut être null
                if ($stats['avg_approval_time_hours'] !== null) {
                    $this->assertIsFloat($stats['avg_approval_time_hours']);
                    $this->assertGreaterThanOrEqual(0, $stats['avg_approval_time_hours']);
                }
            }
        }
    }

    public function testGetDocumentsPendingVisaByUser(): void
    {
        // Test sans filtre utilisateur
        $result = $this->documentRepository->getDocumentsPendingVisaByUser();
        $this->assertIsArray($result);
        
        // Si des données existent, vérifier la structure
        if (!empty($result)) {
            foreach ($result as $item) {
                $this->assertArrayHasKey('id', $item);
                $this->assertArrayHasKey('reference', $item);
                $this->assertArrayHasKey('user_id', $item);
                $this->assertArrayHasKey('prenom', $item);
                $this->assertArrayHasKey('nom', $item);
                $this->assertArrayHasKey('departement', $item);
            }
        }
        
        // Test avec un utilisateur spécifique (si des utilisateurs existent)
        $user = $this->entityManager->getRepository(User::class)->findOneBy([]);
        if ($user) {
            $resultFiltered = $this->documentRepository->getDocumentsPendingVisaByUser($user->getId());
            $this->assertIsArray($resultFiltered);
        }
    }

    public function testGetValidatorPerformanceStatistics(): void
    {
        $result = $this->documentRepository->getValidatorPerformanceStatistics();
        
        $this->assertIsArray($result);
        
        // Si des données existent, vérifier la structure
        if (!empty($result)) {
            foreach ($result as $userId => $stats) {
                $this->assertIsInt($userId);
                $this->assertIsArray($stats);
                
                $this->assertArrayHasKey('name', $stats);
                $this->assertArrayHasKey('department', $stats);
                $this->assertArrayHasKey('total_visas', $stats);
                $this->assertArrayHasKey('approved_visas', $stats);
                $this->assertArrayHasKey('pending_visas', $stats);
                $this->assertArrayHasKey('approval_rate', $stats);
                $this->assertArrayHasKey('avg_response_time_hours', $stats);
                
                $this->assertIsString($stats['name']);
                $this->assertIsInt($stats['total_visas']);
                $this->assertIsInt($stats['approved_visas']);
                $this->assertIsInt($stats['pending_visas']);
                $this->assertIsFloat($stats['approval_rate']);
                
                $this->assertGreaterThan(0, $stats['total_visas']);
                $this->assertGreaterThanOrEqual(0, $stats['approved_visas']);
                $this->assertGreaterThanOrEqual(0, $stats['pending_visas']);
                $this->assertGreaterThanOrEqual(0, $stats['approval_rate']);
                $this->assertLessThanOrEqual(100, $stats['approval_rate']);
                
                // avg_response_time_hours peut être null
                if ($stats['avg_response_time_hours'] !== null) {
                    $this->assertIsFloat($stats['avg_response_time_hours']);
                    $this->assertGreaterThanOrEqual(0, $stats['avg_response_time_hours']);
                }
            }
        }
    }

    public function testGetDetailedDocumentTypeDistribution(): void
    {
        $result = $this->documentRepository->getDetailedDocumentTypeDistribution();
        
        $this->assertIsArray($result);
        
        // Si des données existent, vérifier la structure
        if (!empty($result)) {
            foreach ($result as $docType => $procTypes) {
                $this->assertIsString($docType);
                $this->assertIsArray($procTypes);
                
                foreach ($procTypes as $procType => $matProdTypes) {
                    $this->assertIsString($procType);
                    $this->assertIsArray($matProdTypes);
                    
                    foreach ($matProdTypes as $matProdType => $count) {
                        $this->assertIsString($matProdType);
                        $this->assertIsInt($count);
                        $this->assertGreaterThan(0, $count);
                    }
                }
            }
        }
    }

    public function testGetDocumentDistributionByProjectAndDMO(): void
    {
        $result = $this->documentRepository->getDocumentDistributionByProjectAndDMO();
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('projects', $result);
        $this->assertArrayHasKey('dmos', $result);
        $this->assertArrayHasKey('standalone', $result);
        
        $this->assertIsArray($result['projects']);
        $this->assertIsArray($result['dmos']);
        $this->assertIsInt($result['standalone']);
        $this->assertGreaterThanOrEqual(0, $result['standalone']);
        
        // Vérifier la structure des projets
        foreach ($result['projects'] as $project) {
            $this->assertArrayHasKey('id', $project);
            $this->assertArrayHasKey('name', $project);
            $this->assertArrayHasKey('description', $project);
            $this->assertArrayHasKey('document_count', $project);
            
            $this->assertIsInt($project['id']);
            $this->assertIsInt($project['document_count']);
            $this->assertGreaterThan(0, $project['document_count']);
        }
        
        // Vérifier la structure des DMOs
        foreach ($result['dmos'] as $dmo) {
            $this->assertArrayHasKey('id', $dmo);
            $this->assertArrayHasKey('name', $dmo);
            $this->assertArrayHasKey('description', $dmo);
            $this->assertArrayHasKey('document_count', $dmo);
            
            $this->assertIsInt($dmo['id']);
            $this->assertIsInt($dmo['document_count']);
            $this->assertGreaterThan(0, $dmo['document_count']);
        }
    }

    public function testGetDocumentCreationTrends(): void
    {
        // Test avec paramètre par défaut
        $result = $this->documentRepository->getDocumentCreationTrends();
        $this->assertIsArray($result);
        
        // Test avec paramètre personnalisé
        $result6Months = $this->documentRepository->getDocumentCreationTrends(6);
        $this->assertIsArray($result6Months);
        
        // Si des données existent, vérifier la structure
        if (!empty($result)) {
            foreach ($result as $trend) {
                $this->assertArrayHasKey('month', $trend);
                $this->assertArrayHasKey('count', $trend);
                $this->assertArrayHasKey('cumulative', $trend);
                
                $this->assertIsString($trend['month']);
                $this->assertIsInt($trend['count']);
                $this->assertIsInt($trend['cumulative']);
                
                $this->assertGreaterThan(0, $trend['count']);
                $this->assertGreaterThan(0, $trend['cumulative']);
                
                // Vérifier le format de la date (YYYY-MM)
                $this->assertMatchesRegularExpression('/^\d{4}-\d{2}$/', $trend['month']);
            }
        }
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->entityManager->close();
        $this->entityManager = null;
    }
}
